"""
Django template tags for Vite integration
"""

import json
import os
from django import template
from django.conf import settings
from django.utils.safestring import mark_safe
from django.contrib.staticfiles.storage import staticfiles_storage

register = template.Library()


@register.simple_tag
def vite_asset(entry_name):
    """
    Load a Vite asset by entry name.
    In development, loads from Vite dev server.
    In production, loads from manifest.json.
    """
    if settings.DEBUG:
        # Development mode - load from Vite dev server
        return mark_safe(f'<script type="module" src="http://localhost:3000/{entry_name}"></script>')
    
    # Production mode - load from manifest
    manifest_path = os.path.join(settings.BASE_DIR, 'static', 'dist', 'manifest.json')
    
    if not os.path.exists(manifest_path):
        return ''
    
    try:
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)
        
        if entry_name in manifest:
            file_path = manifest[entry_name]['file']
            full_url = staticfiles_storage.url(f'dist/{file_path}')
            
            if file_path.endswith('.js'):
                return mark_safe(f'<script type="module" src="{full_url}"></script>')
            elif file_path.endswith('.css'):
                return mark_safe(f'<link rel="stylesheet" href="{full_url}">')
        
    except (json.JSONDecodeError, KeyError):
        pass
    
    return ''


@register.simple_tag
def vite_css(entry_name):
    """
    Load CSS assets for a Vite entry.
    """
    if settings.DEBUG:
        # In development, CSS is injected by Vite
        return ''
    
    manifest_path = os.path.join(settings.BASE_DIR, 'static', 'dist', 'manifest.json')
    
    if not os.path.exists(manifest_path):
        return ''
    
    try:
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)
        
        if entry_name in manifest:
            entry = manifest[entry_name]
            css_files = entry.get('css', [])
            
            css_tags = []
            for css_file in css_files:
                full_url = staticfiles_storage.url(f'dist/{css_file}')
                css_tags.append(f'<link rel="stylesheet" href="{full_url}">')
            
            return mark_safe('\n'.join(css_tags))
        
    except (json.JSONDecodeError, KeyError):
        pass
    
    return ''


@register.simple_tag
def vite_dev_server():
    """
    Include Vite development server client in development mode.
    """
    if settings.DEBUG:
        return mark_safe(
            '<script type="module" src="http://localhost:3000/@vite/client"></script>'
        )
    return ''


@register.inclusion_tag('utility_app/vite_assets.html')
def vite_bundle(entry_name):
    """
    Include both JS and CSS assets for a Vite entry.
    """
    return {
        'entry_name': entry_name,
        'debug': settings.DEBUG
    }
