"""
Django management command to build assets with Vite
"""

import os
import subprocess
import json
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings


class Command(BaseCommand):
    help = 'Build frontend assets using Vite'

    def add_arguments(self, parser):
        parser.add_argument(
            '--watch',
            action='store_true',
            help='Watch for changes and rebuild automatically',
        )
        parser.add_argument(
            '--dev',
            action='store_true',
            help='Start Vite development server',
        )

    def handle(self, *args, **options):
        # Check if package.json exists
        package_json_path = os.path.join(settings.BASE_DIR, 'package.json')
        if not os.path.exists(package_json_path):
            raise CommandError('package.json not found. Run npm init first.')

        # Check if node_modules exists
        node_modules_path = os.path.join(settings.BASE_DIR, 'node_modules')
        if not os.path.exists(node_modules_path):
            self.stdout.write(
                self.style.WARNING('node_modules not found. Installing dependencies...')
            )
            self.install_dependencies()

        if options['dev']:
            self.start_dev_server()
        elif options['watch']:
            self.watch_build()
        else:
            self.build_production()

    def install_dependencies(self):
        """Install npm dependencies"""
        try:
            subprocess.run(['npm', 'install'], check=True, cwd=settings.BASE_DIR)
            self.stdout.write(
                self.style.SUCCESS('Dependencies installed successfully.')
            )
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Failed to install dependencies: {e}')

    def start_dev_server(self):
        """Start Vite development server"""
        self.stdout.write('Starting Vite development server...')
        try:
            subprocess.run(['npm', 'run', 'dev'], cwd=settings.BASE_DIR)
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Failed to start development server: {e}')
        except KeyboardInterrupt:
            self.stdout.write('\nDevelopment server stopped.')

    def watch_build(self):
        """Watch for changes and rebuild"""
        self.stdout.write('Starting Vite in watch mode...')
        try:
            subprocess.run(['npm', 'run', 'watch'], cwd=settings.BASE_DIR)
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Failed to start watch mode: {e}')
        except KeyboardInterrupt:
            self.stdout.write('\nWatch mode stopped.')

    def build_production(self):
        """Build for production"""
        self.stdout.write('Building assets for production...')
        try:
            # Clean previous build
            subprocess.run(['npm', 'run', 'clean'], cwd=settings.BASE_DIR)
            
            # Build assets
            subprocess.run(['npm', 'run', 'build'], check=True, cwd=settings.BASE_DIR)
            
            self.stdout.write(
                self.style.SUCCESS('Assets built successfully.')
            )
            
            # Show build info
            self.show_build_info()
            
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Failed to build assets: {e}')

    def show_build_info(self):
        """Show information about the build"""
        manifest_path = os.path.join(settings.BASE_DIR, 'static', 'dist', 'manifest.json')
        
        if os.path.exists(manifest_path):
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            
            self.stdout.write('\nBuild manifest:')
            for entry, info in manifest.items():
                self.stdout.write(f'  {entry} -> {info.get("file", "N/A")}')
        else:
            self.stdout.write(
                self.style.WARNING('Manifest file not found.')
            )
