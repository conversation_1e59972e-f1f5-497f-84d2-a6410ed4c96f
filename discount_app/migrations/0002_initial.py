# Generated by Django 5.2.4 on 2025-07-05 20:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("discount_app", "0001_initial"),
        ("venues_app", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="platformdiscount",
            name="category",
            field=models.ForeignKey(
                blank=True,
                help_text="Category this discount applies to (optional - leave blank for all categories)",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="platform_discounts",
                to="venues_app.category",
            ),
        ),
        migrations.AddField(
            model_name="platformdiscount",
            name="created_by",
            field=models.ForeignKey(
                help_text="User who created this discount",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="servicediscount",
            name="approved_by",
            field=models.ForeignKey(
                blank=True,
                help_text="Admin who approved this discount",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="approved_service_discounts",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="servicediscount",
            name="created_by",
            field=models.ForeignKey(
                help_text="User who created this discount",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="servicediscount",
            name="service",
            field=models.ForeignKey(
                help_text="Service this discount applies to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="discounts",
                to="venues_app.service",
            ),
        ),
        migrations.AddField(
            model_name="venuediscount",
            name="approved_by",
            field=models.ForeignKey(
                blank=True,
                help_text="Admin who approved this discount",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="approved_venue_discounts",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="venuediscount",
            name="created_by",
            field=models.ForeignKey(
                help_text="User who created this discount",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="venuediscount",
            name="venue",
            field=models.ForeignKey(
                help_text="Venue this discount applies to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="discounts",
                to="venues_app.venue",
            ),
        ),
        migrations.AddIndex(
            model_name="discountusage",
            index=models.Index(
                fields=["discount_type", "discount_id"],
                name="discount_ap_discoun_925968_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="discountusage",
            index=models.Index(
                fields=["user", "-used_at"], name="discount_ap_user_id_d2ad74_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="discountusage",
            index=models.Index(
                fields=["-used_at"], name="discount_ap_used_at_5ba916_idx"
            ),
        ),
        migrations.AddConstraint(
            model_name="discountusage",
            constraint=models.UniqueConstraint(
                fields=("user", "discount_type", "discount_id", "booking_reference"),
                name="unique_user_discount_booking",
            ),
        ),
    ]
