import { defineConfig } from 'vite';
import { resolve } from 'path';
import legacy from '@vitejs/plugin-legacy';

export default defineConfig({
  // Base public path when served in development or production
  base: '/static/',
  
  // Build configuration
  build: {
    // Output directory for production build
    outDir: 'static/dist',
    
    // Generate manifest for Django integration
    manifest: true,
    
    // Rollup options
    rollupOptions: {
      // Entry points for different parts of the application
      input: {
        main: resolve(__dirname, 'static/js/main.js'),
        alpine: resolve(__dirname, 'static/js/alpine-components.js'),
        styles: resolve(__dirname, 'static/scss/main.scss'),
        critical: resolve(__dirname, 'static/scss/critical.scss'),
        performance: resolve(__dirname, 'static/js/modules/performance.js'),
        galleries: resolve(__dirname, 'static/js/modules/galleries.js'),
        vendor: resolve(__dirname, 'static/js/vendor.js')
      },
      
      // Output configuration
      output: {
        // Asset file naming
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `img/[name]-[hash][extname]`;
          }
          if (/woff2?|eot|ttf|otf/i.test(ext)) {
            return `fonts/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
        
        // Chunk file naming
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',

        // Manual chunk splitting for better caching
        manualChunks: {
          vendor: ['alpinejs', 'htmx.org'],
          utils: ['static/js/modules/performance.js', 'static/js/modules/galleries.js']
        }
      }
    },
    
    // Minification with performance optimizations
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production',
        pure_funcs: process.env.NODE_ENV === 'production' ? ['console.log', 'console.info'] : [],
        passes: 2
      },
      mangle: {
        safari10: true
      },
      format: {
        comments: false
      }
    },

    // Chunk size warnings
    chunkSizeWarningLimit: 1000,

    // Asset optimization
    assetsInlineLimit: 4096, // Inline assets smaller than 4kb

    // Target modern browsers for better optimization
    target: ['es2020', 'chrome80', 'firefox78', 'safari14', 'edge88'],

    // CSS code splitting
    cssCodeSplit: true,
    
    // Source maps for development
    sourcemap: process.env.NODE_ENV === 'development'
  },
  
  // Development server configuration
  server: {
    host: 'localhost',
    port: 3000,
    open: false,
    cors: true,
    
    // Proxy API requests to Django development server
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      },
      '/admin': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  },
  
  // CSS configuration
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "static/scss/abstracts/variables";`
      }
    },
    
    // PostCSS configuration
    postcss: {
      plugins: [
        require('autoprefixer')
      ]
    }
  },
  
  // Plugins
  plugins: [
    // Legacy browser support
    legacy({
      targets: ['defaults', 'not IE 11']
    })
  ],
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, 'static'),
      '@js': resolve(__dirname, 'static/js'),
      '@css': resolve(__dirname, 'static/css'),
      '@scss': resolve(__dirname, 'static/scss'),
      '@img': resolve(__dirname, 'static/img')
    }
  },
  
  // Define global constants
  define: {
    __DEV__: process.env.NODE_ENV === 'development'
  }
});
