amqp==5.3.1
asgiref==3.8.1
beautifulsoup4==4.13.4
billiard==4.2.1
bleach==6.2.0
boto3==1.38.36
botocore==1.38.36
Brotli==1.1.0
celery==5.5.3
certifi==2025.4.26
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
coverage==7.8.2
crispy-bootstrap5==2024.10
django-avatar==8.0.1
django-phonenumber-field[phonenumbers]==8.1.0
dnspython==2.7.0
phonenumbers==9.0.8
dj-database-url==3.0.0
Django==5.2.3
django-crispy-forms==2.4
django-compressor==4.5.1
django-storages==1.14.6
django-widget-tweaks==1.5.0
ecdsa==0.19.1
greenlet==3.1.1
gunicorn==23.0.0
h11==0.16.0
idna==3.10
iniconfig==2.1.0
jmespath==1.0.1
kombu==5.5.4
MarkupSafe==3.0.2
model-bakery==1.20.5
packaging==25.0
pillow==11.2.1
playwright==1.48.0
pluggy==1.6.0
prompt_toolkit==3.0.51
psycopg2-binary==2.9.10
pyee==12.0.0
Pygments==2.19.1
pytest==8.4.0
pytest-asyncio==1.0.0
pytest-base-url==2.1.0
pytest-cov==6.1.1
pytest-django==4.11.1
pytest-playwright==0.5.2
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.0.1
python-http-client==3.3.7
python-slugify==8.0.4
reportlab==4.4.1
requests==2.32.4
s3transfer==0.13.0
sendgrid==6.12.4
six==1.17.0
soupsieve==2.7
sqlparse==0.5.3
text-unidecode==1.3
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
uvicorn==0.34.2
vine==5.1.0
wcwidth==0.2.13
webencodings==0.5.1
Werkzeug==3.1.3
whitenoise==6.9.0

# Location Management Packages
django-cities-light==3.10.1
django-address==0.2.8
django-geoposition==0.3.0
django-leaflet==0.32.0
django-autoslug==1.9.9
progressbar2==4.5.0
python-utils==3.9.1
unidecode==1.4.0

# Search and Filtering Packages
django-filter==25.1
django-autocomplete-light==3.12.1
django-taggit==6.1.0
django-watson==1.6.3

# Modern Dashboard & Admin Interface Packages
django-unfold==0.62.0          # Modern Tailwind-based admin with charts & dashboards - UPDATED
django-jazzmin==3.0.1          # Modern admin theme with Bootstrap - UPDATED
django-import-export==4.3.8    # Data import/export functionality - NEW
django-admin-tools==0.9.3      # Dashboard widgets and tools - UPDATED
django-grappelli==4.0.2        # Enhanced admin interface - NEW
django-admin-interface==0.30.1 # Modern admin theme - NEW

# Wagtail CMS Packages
wagtail==7.0.1
django-modelcluster>=6.2.1
djangorestframework>=3.15.1

# Modern Booking Engine Packages
django-appointment==3.8.0      # Advanced appointment scheduling with conflict handling - NEW
django-ical==1.9.2             # iCal feed generation for calendar exports - NEW
google-api-python-client==2.152.0  # Google Calendar API integration - NEW
google-auth==2.37.0            # Google authentication - NEW
google-auth-oauthlib==1.2.1    # OAuth2 flow for Google services - NEW
google-auth-httplib2==0.2.0    # HTTP library for Google Auth - NEW
django-q2==1.7.4               # Task queue for async operations and reminders - NEW
icalendar==6.0.1               # Calendar data handling and manipulation - NEW
celery==5.5.3                  # Already included - for background tasks
redis==5.2.1                   # Cache and message broker for Celery - NEW
python-dateutil==2.9.0.post0   # Already included - enhanced date parsing
pytz==2024.2                   # Timezone support - NEW
