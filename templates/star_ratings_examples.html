<!-- 
Star Ratings Integration Examples
This file shows how to use the star ratings system with Venue and Service models
-->

<!-- Load star ratings template tags -->
{% load ratings %}

<!-- Example 1: Display venue ratings -->
<div class="venue-ratings">
    <h3>{{ venue.venue_name }} Ratings</h3>
    
    <!-- Display current rating -->
    <div class="current-rating">
        <span class="rating-score">{{ venue.get_average_rating }}/5</span>
        <span class="rating-count">({{ venue.get_rating_count }} reviews)</span>
    </div>
    
    <!-- Star rating widget for users to rate -->
    {% ratings venue %}
    
    <!-- Rating distribution -->
    <div class="rating-distribution">
        {% for star, count in venue.get_rating_distribution.items %}
            <div class="rating-bar">
                <span>{{ star }} stars:</span>
                <div class="bar">
                    <div class="fill" style="width: {% widthratio count venue.get_rating_count 100 %}%"></div>
                </div>
                <span>{{ count }}</span>
            </div>
        {% endfor %}
    </div>
</div>

<!-- Example 2: Display service ratings -->
<div class="service-ratings">
    <h4>{{ service.service_title }} Ratings</h4>
    
    <!-- Display current rating -->
    <div class="current-rating">
        <span class="rating-score">{{ service.get_average_rating }}/5</span>
        <span class="rating-count">({{ service.get_rating_count }} reviews)</span>
    </div>
    
    <!-- Star rating widget for users to rate -->
    {% ratings service %}
    
    <!-- Check if current user has rated -->
    {% if user.is_authenticated %}
        {% if service.has_user_rated:user %}
            <p>You rated this service: {{ service.get_user_rating:user }} stars</p>
        {% else %}
            <p>Rate this service above!</p>
        {% endif %}
    {% endif %}
</div>

<!-- Example 3: Venue listing with ratings -->
<div class="venue-list">
    {% for venue in venues %}
        <div class="venue-card">
            <h3>{{ venue.venue_name }}</h3>
            <p>{{ venue.short_description }}</p>
            
            <!-- Quick rating display -->
            <div class="quick-rating">
                {% ratings venue readonly=True %}
                <span>{{ venue.get_average_rating }} ({{ venue.get_rating_count }})</span>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Example 4: Service listing with ratings -->
<div class="service-list">
    {% for service in services %}
        <div class="service-card">
            <h4>{{ service.service_title }}</h4>
            <p>{{ service.service_description|truncatewords:20 }}</p>
            <p class="price">${{ service.price_min }} - ${{ service.price_max }}</p>
            
            <!-- Quick rating display -->
            <div class="quick-rating">
                {% ratings service readonly=True %}
                <span>{{ service.get_average_rating }} ({{ service.get_rating_count }})</span>
            </div>
        </div>
    {% endfor %}
</div>

<!-- CSS for styling (add to your main CSS file) -->
<style>
.rating-distribution .rating-bar {
    display: flex;
    align-items: center;
    margin: 5px 0;
}

.rating-distribution .bar {
    flex: 1;
    height: 10px;
    background: #e0e0e0;
    margin: 0 10px;
    border-radius: 5px;
    overflow: hidden;
}

.rating-distribution .fill {
    height: 100%;
    background: #ffc107;
    transition: width 0.3s ease;
}

.quick-rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.current-rating {
    margin: 10px 0;
}

.rating-score {
    font-weight: bold;
    font-size: 1.2em;
    color: #ffc107;
}

.rating-count {
    color: #666;
    font-size: 0.9em;
}
</style>

<!-- JavaScript for enhanced functionality (optional) -->
<script>
// Auto-refresh rating display after user rates
document.addEventListener('DOMContentLoaded', function() {
    // Listen for rating changes
    document.addEventListener('rating:change', function(event) {
        // Refresh the page or update the rating display
        location.reload();
    });
});
</script>
