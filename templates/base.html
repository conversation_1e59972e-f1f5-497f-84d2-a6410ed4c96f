<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CozyWish - Book Beauty & Wellness Services{% endblock %}</title>

    <!-- Performance optimizations -->
    {% load performance_tags %}
    {% performance_hints %}
    {% resource_hints %}

    <!-- Inline critical CSS -->
    {% inline_critical_css %}

    <!-- Preload critical resources -->
    {% critical_resource_preload %}

    <!-- External CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    {% load static %}
    {% load compress %}
    {% compress css %}
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <link href="{% static 'css/base.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar.css' %}" rel="stylesheet">
    {% endcompress %}

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#42241A">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CozyWish">
    <meta name="msapplication-TileColor" content="#42241A">

    <!-- PWA Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'images/icons/apple-touch-icon.png' %}">
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'images/icons/favicon-32x32.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'images/icons/favicon-16x16.png' %}">
    <link rel="mask-icon" href="{% static 'images/icons/safari-pinned-tab.svg' %}" color="#42241A">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{% static 'manifest.json' %}">

    <!-- Additional head content -->
    {% block extra_head %}{% endblock %}
    {% block extra_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}" data-authenticated="{% if user.is_authenticated %}true{% else %}false{% endif %}">
    <!-- Navigation Bar -->
    {% include 'includes/navbar_cw.html' %}

    {% if hero_section %}
        <!-- Hero page content with gradient background -->
        <div class="radial-gradient">
            <!-- Hero section content -->
            {% block hero_content %}{% endblock %}
        </div>
    {% endif %}


    <!-- System Messages -->
    {% if messages %}
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;">
            {% for message in messages %}
                <div class="toast text-bg-{{ message.tags }} mb-2" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">{{ message }}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content Area -->
    <main class="container py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Site Footer -->
    <footer class="footer-cw bg-white border-top">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold text-secondary-cw mb-3">CozyWish</h5>
                    <p class="text-neutral-cw mb-2">Find & Book Local Spa and Massage Services</p>
                    <p class="text-neutral-cw">Professional wellness marketplace connecting you with local spa and massage providers.</p>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
                    <h6 class="fw-bold text-secondary-cw mb-3">Services</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{% url 'venues_app:venue_list' %}" class="text-neutral-cw text-decoration-none footer-link">Find Services</a></li>
                        <li class="mb-2"><a href="{% url 'venues_app:venue_list' %}" class="text-neutral-cw text-decoration-none footer-link">Massage</a></li>
                        <li class="mb-2"><a href="{% url 'venues_app:venue_list' %}" class="text-neutral-cw text-decoration-none footer-link">Facial</a></li>
                        <li class="mb-2"><a href="{% url 'venues_app:venue_list' %}" class="text-neutral-cw text-decoration-none footer-link">Spa</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
                    <h6 class="fw-bold text-secondary-cw mb-3">Company</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{% url 'home' %}" class="text-neutral-cw text-decoration-none footer-link">Home</a></li>
                        <li class="mb-2"><a href="{% url 'accounts_app:for_business' %}" class="text-neutral-cw text-decoration-none footer-link">For Business</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">About</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
                    <h6 class="fw-bold text-secondary-cw mb-3">Support</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">Help Center</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">Privacy Policy</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">Terms of Service</a></li>
                        <li class="mb-2"><a href="#" class="text-neutral-cw text-decoration-none footer-link">FAQ</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
                    <h6 class="fw-bold text-secondary-cw mb-3">Connect</h6>
                    <div class="d-flex gap-2 mb-3">
                        <a href="#" class="btn btn-outline-secondary btn-sm rounded-circle social-icon" aria-label="Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn-outline-secondary btn-sm rounded-circle social-icon" aria-label="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-secondary btn-sm rounded-circle social-icon" aria-label="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                    <div class="contact-info">
                        <p class="text-neutral-cw mb-1 small"><i class="fas fa-phone me-2"></i> (*************</p>
                        <p class="text-neutral-cw mb-0 small"><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-neutral-cw mb-0">&copy; {% now "Y" %} CozyWish. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-neutral-cw mb-0">Professional Wellness Marketplace</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <!-- HTMX for dynamic interactions -->
    <script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-D1Kt99CQMDuVetoL1lrYwg5t+9QdHe7NLX/SoJYkXDFfX37iInKRy5xLSi8nO7UC" crossorigin="anonymous"></script>

    <!-- Alpine.js for reactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.13.3/dist/cdn.min.js"></script>

    <!-- Custom JavaScript Files -->
    {% compress js %}
    <script src="{% static 'js/messages.js' %}"></script>
    <script src="{% static 'js/discounts.js' %}"></script>
    <script src="{% static 'js/payments.js' %}"></script>
    <script src="{% static 'js/form_loading.js' %}"></script>
    <script src="{% static 'js/notifications.js' %}"></script>
    {% endcompress %}

    <!-- Bootstrap Tooltip Initialization -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
        });
    </script>

    <!-- Enhanced HTMX Configuration -->
    <script>
        // Advanced HTMX Configuration
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced HTMX configuration
            htmx.config.getCacheBusterParam = true;
            htmx.config.defaultSwapStyle = 'outerHTML';
            htmx.config.defaultSwapDelay = 0;
            htmx.config.defaultSettleDelay = 20;
            htmx.config.useTemplateFragments = true;
            htmx.config.refreshOnHistoryMiss = true;

            // Global loading state management
            let activeRequests = 0;

            // Add CSRF token to all HTMX requests
            document.body.addEventListener('htmx:configRequest', function(evt) {
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
                if (csrfToken) {
                    evt.detail.headers['X-CSRFToken'] = csrfToken;
                }

                // Add custom headers for better debugging
                evt.detail.headers['X-Requested-With'] = 'HTMX';
                evt.detail.headers['X-HTMX-Request'] = 'true';
            });

            // Enhanced loading indicators
            document.body.addEventListener('htmx:beforeRequest', function(evt) {
                activeRequests++;
                const target = evt.target;

                // Show specific indicator if defined
                if (target.hasAttribute('hx-indicator')) {
                    const indicator = document.querySelector(target.getAttribute('hx-indicator'));
                    if (indicator) {
                        indicator.style.display = 'block';
                        indicator.classList.add('htmx-loading');
                    }
                }

                // Add loading class to target
                target.classList.add('htmx-request');

                // Disable form buttons to prevent double submission
                if (target.tagName === 'FORM') {
                    const submitButtons = target.querySelectorAll('button[type="submit"], input[type="submit"]');
                    submitButtons.forEach(btn => {
                        btn.disabled = true;
                        btn.classList.add('htmx-loading-btn');
                    });
                }
            });

            // Enhanced after request handling
            document.body.addEventListener('htmx:afterRequest', function(evt) {
                activeRequests = Math.max(0, activeRequests - 1);
                const target = evt.target;

                // Hide specific indicator
                if (target.hasAttribute('hx-indicator')) {
                    const indicator = document.querySelector(target.getAttribute('hx-indicator'));
                    if (indicator) {
                        indicator.style.display = 'none';
                        indicator.classList.remove('htmx-loading');
                    }
                }

                // Remove loading class from target
                target.classList.remove('htmx-request');

                // Re-enable form buttons
                if (target.tagName === 'FORM') {
                    const submitButtons = target.querySelectorAll('button[type="submit"], input[type="submit"]');
                    submitButtons.forEach(btn => {
                        btn.disabled = false;
                        btn.classList.remove('htmx-loading-btn');
                    });
                }
            });

            // Handle successful responses
            document.body.addEventListener('htmx:afterSwap', function(evt) {
                // Re-initialize Bootstrap components
                const newContent = evt.detail.target;

                // Initialize tooltips in new content
                const tooltips = newContent.querySelectorAll('[data-bs-toggle="tooltip"]');
                tooltips.forEach(el => new bootstrap.Tooltip(el));

                // Initialize popovers in new content
                const popovers = newContent.querySelectorAll('[data-bs-toggle="popover"]');
                popovers.forEach(el => new bootstrap.Popover(el));

                // Re-initialize Alpine.js components
                if (window.Alpine) {
                    Alpine.initTree(newContent);
                }
            });

            // Error handling
            document.body.addEventListener('htmx:responseError', function(evt) {
                console.error('HTMX Response Error:', evt.detail);

                // Show user-friendly error message
                if (window.showNotification) {
                    showNotification('An error occurred. Please try again.', 'error');
                }
            });
        });
    </script>

    <!-- Additional JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>
