{% extends "admin/change_list.html" %}
{% load i18n admin_urls static admin_list %}

{% block content_title %}
    <h1>Login Alerts</h1>
    {% if show_alert_stats %}
        <div class="alert-stats mb-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Alert Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-box bg-light">
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Alerts</span>
                                    <span class="info-box-number">{{ total_alerts }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box {% if unresolved_alerts > 0 %}bg-danger text-white{% else %}bg-light{% endif %}">
                                <div class="info-box-content">
                                    <span class="info-box-text">Unresolved</span>
                                    <span class="info-box-number">{{ unresolved_alerts }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box {% if high_priority_alerts > 0 %}bg-warning text-dark{% else %}bg-light{% endif %}">
                                <div class="info-box-content">
                                    <span class="info-box-text">High Priority</span>
                                    <span class="info-box-number">{{ high_priority_alerts }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if recent_high_priority %}
                        <div class="mt-3">
                            <h6 class="text-warning">
                                <i class="fas fa-bell mr-1"></i>
                                Recent High Priority Alerts
                            </h6>
                            <ul class="list-unstyled">
                                {% for alert in recent_high_priority %}
                                    <li class="mb-1">
                                        <span class="badge badge-warning mr-2">{{ alert.get_alert_type_display }}</span>
                                        <strong>{{ alert.ip_address }}</strong>
                                        <small class="text-muted">({{ alert.created_at|timesince }} ago)</small>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <div class="mt-3">
                        <a href="{% url 'admin:accounts_app_loginalert_changelist' %}?is_resolved__exact=0" class="btn btn-danger mr-2">
                            <i class="fas fa-eye mr-1"></i>
                            View Unresolved Alerts
                        </a>
                        <a href="{% url 'admin:accounts_app_loginalert_changelist' %}?severity__in=high,critical" class="btn btn-warning mr-2">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            View High Priority
                        </a>
                        <a href="{% url 'admin:accounts_app_loginhistory_changelist' %}" class="btn btn-info">
                            <i class="fas fa-history mr-1"></i>
                            View Login History
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .info-box {
            border-radius: 0.25rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
        }
        .info-box-content .info-box-text {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            margin-bottom: 0.5rem;
        }
        .info-box-content .info-box-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
        }
    </style>
{% endblock %}
