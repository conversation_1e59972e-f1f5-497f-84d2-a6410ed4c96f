{% extends "admin/change_list.html" %}
{% load i18n admin_urls static admin_list %}

{% block content_title %}
    <h1>Login History</h1>
    {% if show_security_alerts %}
        <div class="security-alerts mb-4">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt mr-2"></i>
                        Security Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-box {% if failed_last_hour > 10 %}bg-danger text-white{% elif failed_last_hour > 5 %}bg-warning text-dark{% else %}bg-light{% endif %}">
                                <div class="info-box-content">
                                    <span class="info-box-text">Failed Logins (Last Hour)</span>
                                    <span class="info-box-number">{{ failed_last_hour }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box {% if failed_last_day > 50 %}bg-danger text-white{% elif failed_last_day > 20 %}bg-warning text-dark{% else %}bg-light{% endif %}">
                                <div class="info-box-content">
                                    <span class="info-box-text">Failed Logins (Last 24h)</span>
                                    <span class="info-box-number">{{ failed_last_day }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if suspicious_ips %}
                        <div class="mt-3">
                            <h6 class="text-danger">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Suspicious IPs (3+ failed attempts in last hour)
                            </h6>
                            <ul class="list-unstyled">
                                {% for ip in suspicious_ips %}
                                    <li class="mb-1">
                                        <span class="badge badge-danger mr-2">{{ ip.ip_address }}</span>
                                        <strong>{{ ip.attempt_count }} attempts</strong>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <div class="mt-3">
                        <a href="{% url 'admin:accounts_app_loginalert_changelist' %}" class="btn btn-primary mr-2">
                            <i class="fas fa-bell mr-1"></i>
                            View Login Alerts
                        </a>
                        <a href="{% url 'admin:accounts_app_loginhistory_changelist' %}?is_successful__exact=0" class="btn btn-danger">
                            <i class="fas fa-times mr-1"></i>
                            View Failed Logins Only
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .info-box {
            border-radius: 0.25rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
        }
        .info-box-content .info-box-text {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            margin-bottom: 0.5rem;
        }
        .info-box-content .info-box-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
        }
    </style>
{% endblock %}
