{% extends "admin/base_site.html" %}
{% load i18n static admin_urls %}
{% load humanize %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static 'nvd3/build/nv.d3.css' %}" />
    <link rel="stylesheet" type="text/css" href="{% static 'css/admin-dashboard.css' %}" />
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">{% trans 'Dashboard Overview' %}</h1>
        <div class="dashboard-timestamp">
            {% trans 'Last updated:' %} {% now "M j, Y H:i" %}
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-header">
                <h3>{% trans 'Total Users' %}</h3>
                <i class="icon-users"></i>
            </div>
            <div class="metric-value">{{ analytics.total_users|default:0|intcomma }}</div>
            <div class="metric-change">
                <span class="change-positive">+{{ analytics.new_users_30_days|default:0 }}</span>
                {% trans 'this month' %}
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-header">
                <h3>{% trans 'Total Bookings' %}</h3>
                <i class="icon-calendar"></i>
            </div>
            <div class="metric-value">{{ analytics.total_bookings|default:0|intcomma }}</div>
            <div class="metric-change">
                <span class="change-positive">+{{ analytics.bookings_30_days|default:0 }}</span>
                {% trans 'this month' %}
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-header">
                <h3>{% trans 'Total Revenue' %}</h3>
                <i class="icon-dollar"></i>
            </div>
            <div class="metric-value">${{ analytics.total_revenue|default:0|floatformat:2|intcomma }}</div>
            <div class="metric-change">
                <span class="change-positive">${{ analytics.revenue_30_days|default:0|floatformat:2|intcomma }}</span>
                {% trans 'this month' %}
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-header">
                <h3>{% trans 'Active Venues' %}</h3>
                <i class="icon-building"></i>
            </div>
            <div class="metric-value">{{ analytics.approved_venues|default:0|intcomma }}</div>
            <div class="metric-change">
                <span class="change-neutral">{{ analytics.pending_venues|default:0 }}</span>
                {% trans 'pending' %}
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
        <div class="chart-container">
            <div class="chart-header">
                <h3>{% trans 'User Registration Trends' %}</h3>
                <div class="chart-controls">
                    <button class="btn btn-sm" onclick="changeChartPeriod('users', 7)">7D</button>
                    <button class="btn btn-sm active" onclick="changeChartPeriod('users', 30)">30D</button>
                    <button class="btn btn-sm" onclick="changeChartPeriod('users', 90)">90D</button>
                </div>
            </div>
            <div class="chart-wrapper">
                <svg id="userChart" class="chart"></svg>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-header">
                <h3>{% trans 'Booking Trends' %}</h3>
                <div class="chart-controls">
                    <button class="btn btn-sm" onclick="changeChartPeriod('bookings', 7)">7D</button>
                    <button class="btn btn-sm active" onclick="changeChartPeriod('bookings', 30)">30D</button>
                    <button class="btn btn-sm" onclick="changeChartPeriod('bookings', 90)">90D</button>
                </div>
            </div>
            <div class="chart-wrapper">
                <svg id="bookingChart" class="chart"></svg>
            </div>
        </div>

        <div class="chart-container full-width">
            <div class="chart-header">
                <h3>{% trans 'Revenue Analytics' %}</h3>
                <div class="chart-controls">
                    <button class="btn btn-sm" onclick="changeChartPeriod('revenue', 7)">7D</button>
                    <button class="btn btn-sm active" onclick="changeChartPeriod('revenue', 30)">30D</button>
                    <button class="btn btn-sm" onclick="changeChartPeriod('revenue', 90)">90D</button>
                </div>
            </div>
            <div class="chart-wrapper">
                <svg id="revenueChart" class="chart"></svg>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h3>{% trans 'Quick Actions' %}</h3>
        <div class="actions-grid">
            <a href="{% url 'admin:accounts_app_customuser_add' %}" class="action-card">
                <i class="icon-user-plus"></i>
                <span>{% trans 'Add User' %}</span>
            </a>
            <a href="{% url 'admin:venues_app_venue_add' %}" class="action-card">
                <i class="icon-plus"></i>
                <span>{% trans 'Add Venue' %}</span>
            </a>
            <a href="{% url 'admin:booking_cart_app_booking_changelist' %}" class="action-card">
                <i class="icon-list"></i>
                <span>{% trans 'View Bookings' %}</span>
            </a>
            <a href="{% url 'dashboard_app:admin_system_health' %}" class="action-card">
                <i class="icon-monitor"></i>
                <span>{% trans 'System Health' %}</span>
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="recent-activity">
        <div class="activity-section">
            <h3>{% trans 'Recent Bookings' %}</h3>
            <div class="activity-list">
                {% for booking in recent_bookings|slice:":5" %}
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="icon-calendar"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">
                            {{ booking.venue.venue_name }}
                        </div>
                        <div class="activity-meta">
                            {{ booking.customer.user.email }} • {{ booking.booking_date|date:"M j, Y" }}
                        </div>
                    </div>
                    <div class="activity-status status-{{ booking.status }}">
                        {{ booking.get_status_display }}
                    </div>
                </div>
                {% empty %}
                <div class="empty-state">
                    <i class="icon-calendar"></i>
                    <p>{% trans 'No recent bookings' %}</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="activity-section">
            <h3>{% trans 'Recent Reviews' %}</h3>
            <div class="activity-list">
                {% for review in recent_reviews|slice:":5" %}
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="icon-star"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">
                            {{ review.venue.venue_name }}
                        </div>
                        <div class="activity-meta">
                            {{ review.customer.user.email }} • {{ review.created_at|date:"M j, Y" }}
                        </div>
                    </div>
                    <div class="activity-rating">
                        {% for i in "12345"|make_list %}
                            {% if forloop.counter <= review.rating %}
                                <i class="icon-star filled"></i>
                            {% else %}
                                <i class="icon-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% empty %}
                <div class="empty-state">
                    <i class="icon-star"></i>
                    <p>{% trans 'No recent reviews' %}</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extrajs %}
    {{ block.super }}
    <script src="{% static 'd3/d3.js' %}"></script>
    <script src="{% static 'nvd3/build/nv.d3.js' %}"></script>
    <script>
        // Chart data from Django context
        const chartData = {
            users: {{ user_chart_data|safe }},
            bookings: {{ booking_chart_data|safe }},
            revenue: {{ revenue_chart_data|safe }}
        };

        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });

        function initializeCharts() {
            // User Registration Chart
            nv.addGraph(function() {
                var chart = nv.models.lineChart()
                    .margin({left: 80, right: 20, top: 20, bottom: 60})
                    .useInteractiveGuideline(true)
                    .transitionDuration(350)
                    .showLegend(false)
                    .showYAxis(true)
                    .showXAxis(true);

                chart.xAxis
                    .axisLabel('Date')
                    .tickFormat(function(d) {
                        return d3.time.format('%m/%d')(new Date(d));
                    });

                chart.yAxis
                    .axisLabel('Users')
                    .tickFormat(d3.format('d'));

                var data = [{
                    values: chartData.users.map(function(d) {
                        return {x: new Date(d.date), y: d.count};
                    }),
                    key: 'Users',
                    color: '#007cba'
                }];

                d3.select('#userChart')
                    .datum(data)
                    .call(chart);

                nv.utils.windowResize(chart.update);

                return chart;
            });

            // Booking Trends Chart
            nv.addGraph(function() {
                var chart = nv.models.lineChart()
                    .margin({left: 80, right: 20, top: 20, bottom: 60})
                    .useInteractiveGuideline(true)
                    .transitionDuration(350)
                    .showLegend(false)
                    .showYAxis(true)
                    .showXAxis(true);

                chart.xAxis
                    .axisLabel('Date')
                    .tickFormat(function(d) {
                        return d3.time.format('%m/%d')(new Date(d));
                    });

                chart.yAxis
                    .axisLabel('Bookings')
                    .tickFormat(d3.format('d'));

                var data = [{
                    values: chartData.bookings.map(function(d) {
                        return {x: new Date(d.date), y: d.count};
                    }),
                    key: 'Bookings',
                    color: '#28a745'
                }];

                d3.select('#bookingChart')
                    .datum(data)
                    .call(chart);

                nv.utils.windowResize(chart.update);

                return chart;
            });

            // Revenue Chart
            nv.addGraph(function() {
                var chart = nv.models.lineChart()
                    .margin({left: 100, right: 20, top: 20, bottom: 60})
                    .useInteractiveGuideline(true)
                    .transitionDuration(350)
                    .showLegend(false)
                    .showYAxis(true)
                    .showXAxis(true);

                chart.xAxis
                    .axisLabel('Date')
                    .tickFormat(function(d) {
                        return d3.time.format('%m/%d')(new Date(d));
                    });

                chart.yAxis
                    .axisLabel('Revenue ($)')
                    .tickFormat(d3.format('$,.2f'));

                var data = [{
                    values: chartData.revenue.map(function(d) {
                        return {x: new Date(d.date), y: d.revenue};
                    }),
                    key: 'Revenue',
                    color: '#dc3545'
                }];

                d3.select('#revenueChart')
                    .datum(data)
                    .call(chart);

                nv.utils.windowResize(chart.update);

                return chart;
            });
        }

        function changeChartPeriod(chartType, days) {
            // Update chart period (would make AJAX request in real implementation)
            console.log('Changing ' + chartType + ' chart to ' + days + ' days');
            
            // Update button states
            document.querySelectorAll('.chart-controls button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }
    </script>
{% endblock %} 