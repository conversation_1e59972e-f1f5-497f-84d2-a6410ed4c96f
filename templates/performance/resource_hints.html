<!-- DNS Prefetch for external domains -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.gstatic.com">
<link rel="dns-prefetch" href="//cdn.jsdelivr.net">
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
<link rel="dns-prefetch" href="//unpkg.com">

<!-- Preconnect to critical external resources -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="preconnect" href="https://cdn.jsdelivr.net">

<!-- Preload critical fonts -->
{% for font in fonts %}
<link rel="preload" href="{{ font }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="{{ font }}"></noscript>
{% endfor %}

<!-- Preload critical CSS -->
<link rel="preload" href="{% load static %}{% static 'dist/css/critical.css' %}" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="{% load static %}{% static 'dist/css/critical.css' %}"></noscript>

<!-- Prefetch non-critical resources -->
{% for style in styles %}
<link rel="prefetch" href="{{ style }}">
{% endfor %}

{% for script in scripts %}
<link rel="prefetch" href="{{ script }}">
{% endfor %}

<!-- Prefetch likely next pages -->
<link rel="prefetch" href="/venues/">
<link rel="prefetch" href="/venues/search/">
<link rel="prefetch" href="/about/">
<link rel="prefetch" href="/contact/">

<!-- Module preload for ES6 modules -->
<link rel="modulepreload" href="{% load static %}{% static 'dist/js/main.js' %}">
<link rel="modulepreload" href="{% load static %}{% static 'dist/js/alpine.js' %}">
<link rel="modulepreload" href="{% load static %}{% static 'dist/js/performance.js' %}"
