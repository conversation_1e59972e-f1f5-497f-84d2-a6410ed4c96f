<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Reminder - <PERSON>zyWish</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .content {
            padding: 2rem;
        }
        
        .appointment-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 4px solid #667eea;
        }
        
        .appointment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .appointment-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #495057;
        }
        
        .appointment-status {
            background: #28a745;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .appointment-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .detail-icon {
            width: 20px;
            height: 20px;
            color: #667eea;
        }
        
        .detail-label {
            font-weight: 600;
            color: #6c757d;
            min-width: 80px;
        }
        
        .detail-value {
            color: #495057;
        }
        
        .cta-section {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid #e9ecef;
            margin-top: 2rem;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 1rem 2rem;
            border-radius: 6px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: transform 0.2s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .footer {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer p {
            color: #6c757d;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        
        .social-links {
            margin-top: 1rem;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 0.5rem;
            color: #6c757d;
            font-size: 1.2rem;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            
            .content {
                padding: 1rem;
            }
            
            .appointment-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .appointment-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 Appointment Reminder</h1>
            <p>Don't forget about your upcoming appointment!</p>
        </div>
        
        <div class="content">
            <p>Hello {{ customer.get_full_name }},</p>
            
            <p>This is a friendly reminder about your upcoming appointment. We're looking forward to seeing you!</p>
            
            {% for item in items %}
            <div class="appointment-card">
                <div class="appointment-header">
                    <div class="appointment-title">{{ item.service_title }}</div>
                    <div class="appointment-status">{{ booking.get_status_display }}</div>
                </div>
                
                <div class="appointment-details">
                    <div class="detail-item">
                        <div class="detail-icon">📅</div>
                        <div class="detail-label">Date:</div>
                        <div class="detail-value">{{ item.scheduled_date|date:"l, F j, Y" }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon">⏰</div>
                        <div class="detail-label">Time:</div>
                        <div class="detail-value">{{ item.scheduled_time|time:"g:i A" }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon">⏱️</div>
                        <div class="detail-label">Duration:</div>
                        <div class="detail-value">{{ item.duration_minutes }} minutes</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon">🏢</div>
                        <div class="detail-label">Venue:</div>
                        <div class="detail-value">{{ booking.venue.business_name }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon">💰</div>
                        <div class="detail-label">Price:</div>
                        <div class="detail-value">${{ item.service_price }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-icon">📍</div>
                        <div class="detail-label">Address:</div>
                        <div class="detail-value">{{ booking.venue.full_address|default:"Contact venue for address" }}</div>
                    </div>
                </div>
                
                {% if booking.notes %}
                <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e9ecef;">
                    <div class="detail-item">
                        <div class="detail-icon">📝</div>
                        <div class="detail-label">Notes:</div>
                        <div class="detail-value">{{ booking.notes }}</div>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 1rem; margin: 1.5rem 0;">
                <h4 style="color: #856404; margin-bottom: 0.5rem;">📋 What to Bring</h4>
                <ul style="color: #856404; margin-left: 1rem;">
                    <li>Valid ID for verification</li>
                    <li>Payment method (if payment on arrival)</li>
                    <li>Any specific items mentioned by the service provider</li>
                </ul>
            </div>
            
            <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 6px; padding: 1rem; margin: 1.5rem 0;">
                <h4 style="color: #0c5460; margin-bottom: 0.5rem;">ℹ️ Important Information</h4>
                <ul style="color: #0c5460; margin-left: 1rem;">
                    <li>Please arrive 10 minutes early</li>
                    <li>Contact the venue if you need to reschedule</li>
                    <li>Cancellation policy applies as per terms</li>
                </ul>
            </div>
            
            <div class="cta-section">
                <a href="{{ booking.get_absolute_url }}" class="cta-button">
                    View Booking Details
                </a>
                
                <p style="margin-top: 1rem; color: #6c757d;">
                    Need to make changes? 
                    <a href="mailto:<EMAIL>" style="color: #667eea;">Contact Support</a>
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>CozyWish</strong> - Your Booking Platform</p>
            <p>
                This email was sent to {{ customer.email }}. 
                <a href="#">Unsubscribe</a> | 
                <a href="#">Manage Preferences</a>
            </p>
            
            <div class="social-links">
                <a href="#">📘</a>
                <a href="#">🐦</a>
                <a href="#">📷</a>
            </div>
            
            <p style="margin-top: 1rem; font-size: 0.75rem;">
                © 2024 CozyWish. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html> 