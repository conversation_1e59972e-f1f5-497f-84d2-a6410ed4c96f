<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - CozyWish</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #FFF9F4 0%, #F5E6D3 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .offline-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .offline-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(66, 36, 26, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            margin: 2rem;
        }
        
        .offline-icon {
            font-size: 4rem;
            color: #42241A;
            margin-bottom: 1.5rem;
        }
        
        .offline-title {
            color: #2F160F;
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            color: #525252;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .retry-btn {
            background: #42241A;
            border: 2px solid #42241A;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .retry-btn:hover {
            background: white;
            color: #42241A;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 36, 26, 0.2);
        }
        
        .offline-features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #E5E5E5;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            text-align: left;
        }
        
        .feature-icon {
            color: #42241A;
            margin-right: 1rem;
            width: 20px;
        }
        
        .connection-status {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 10px;
            background: #F8F9FA;
            border-left: 4px solid #DC3545;
        }
        
        .connection-status.online {
            border-left-color: #28A745;
            background: #D4EDDA;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #DC3545;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.online {
            background: #28A745;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .cached-content {
            margin-top: 2rem;
            text-align: left;
        }
        
        .cached-item {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #F8F9FA;
            border-radius: 8px;
            border-left: 3px solid #42241A;
        }
        
        .cached-item a {
            color: #42241A;
            text-decoration: none;
            font-weight: 500;
        }
        
        .cached-item a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-card">
            <div class="offline-icon">
                <i class="fas fa-wifi-slash"></i>
            </div>
            
            <h1 class="offline-title">You're Offline</h1>
            
            <p class="offline-message">
                It looks like you've lost your internet connection. Don't worry - you can still browse some content that we've saved for you.
            </p>
            
            <a href="javascript:window.location.reload()" class="retry-btn">
                <i class="fas fa-sync-alt me-2"></i>
                Try Again
            </a>
            
            <div class="connection-status" id="connectionStatus">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">Checking connection...</span>
            </div>
            
            <div class="offline-features">
                <h5 style="color: #2F160F; margin-bottom: 1rem;">What you can do offline:</h5>
                
                <div class="feature-item">
                    <i class="fas fa-eye feature-icon"></i>
                    <span>View previously visited venues</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-heart feature-icon"></i>
                    <span>Browse your saved favorites</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-calendar feature-icon"></i>
                    <span>Check your booking history</span>
                </div>
                
                <div class="feature-item">
                    <i class="fas fa-search feature-icon"></i>
                    <span>Use basic search functionality</span>
                </div>
            </div>
            
            <div class="cached-content">
                <h6 style="color: #2F160F; margin-bottom: 1rem;">Available offline content:</h6>
                
                <div class="cached-item">
                    <a href="/">
                        <i class="fas fa-home me-2"></i>
                        Homepage
                    </a>
                </div>
                
                <div class="cached-item">
                    <a href="/venues/">
                        <i class="fas fa-building me-2"></i>
                        Browse Venues
                    </a>
                </div>
                
                <div class="cached-item">
                    <a href="/venues/search/">
                        <i class="fas fa-search me-2"></i>
                        Search Venues
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const indicatorElement = document.getElementById('statusIndicator');
            const textElement = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.classList.add('online');
                indicatorElement.classList.add('online');
                textElement.textContent = 'Connection restored! You can reload the page.';
            } else {
                statusElement.classList.remove('online');
                indicatorElement.classList.remove('online');
                textElement.textContent = 'No internet connection detected.';
            }
        }
        
        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial check
        updateConnectionStatus();
        
        // Auto-reload when connection is restored
        window.addEventListener('online', function() {
            setTimeout(() => {
                if (navigator.onLine) {
                    window.location.reload();
                }
            }, 2000);
        });
        
        // Service worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(function(registration) {
                console.log('Service Worker is ready');
            });
        }
    </script>
</body>
</html>
