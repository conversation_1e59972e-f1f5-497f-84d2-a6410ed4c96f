{% load static %}

<!-- Modern Image Gallery Component -->
<div class="modern-gallery" x-data="modernGallery({{ images|default:'[]' }})" x-init="init()">
    <!-- Loading State -->
    <div x-show="loading" class="gallery-skeleton">
        <div class="modern-gallery__grid">
            <div class="skeleton-item" x-for="i in 6"></div>
        </div>
    </div>
    
    <!-- Gallery Grid -->
    <div x-show="!loading" class="modern-gallery__grid" x-transition>
        <template x-for="(image, index) in images" :key="image.id || index">
            <div class="modern-gallery__item" @click="openLightbox(index)">
                <img 
                    :src="image.thumbnail || image.url" 
                    :alt="image.alt || image.caption || 'Gallery image'"
                    :data-src="image.url"
                    class="modern-gallery__image lazy"
                    @load="$el.classList.add('loaded')"
                    loading="lazy"
                >
                <div class="modern-gallery__overlay">
                    <div class="modern-gallery__caption" x-text="image.caption"></div>
                </div>
            </div>
        </template>
    </div>
    
    <!-- Empty State -->
    <div x-show="!loading && images.length === 0" class="text-center py-5">
        <i class="fas fa-images fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No images available</h5>
        <p class="text-muted">Images will appear here when they are uploaded.</p>
    </div>
    
    <!-- Lightbox Modal -->
    <div x-show="lightboxOpen" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="gallery-lightbox modal fade show d-block" 
         style="background: rgba(0,0,0,0.9);"
         @click.self="closeLightbox()"
         @keydown.escape.window="closeLightbox()">
        
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <!-- Close Button -->
                    <button type="button" 
                            class="gallery-lightbox__close" 
                            @click="closeLightbox()">
                        <i class="fas fa-times"></i>
                    </button>
                    
                    <!-- Image Counter -->
                    <div class="gallery-lightbox__counter" x-show="images.length > 1">
                        <span x-text="currentIndex + 1"></span> / <span x-text="images.length"></span>
                    </div>
                    
                    <!-- Navigation Buttons -->
                    <template x-if="images.length > 1">
                        <div>
                            <button type="button" 
                                    class="gallery-lightbox__nav gallery-lightbox__nav--prev"
                                    @click="previousImage()"
                                    x-show="currentIndex > 0">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            
                            <button type="button" 
                                    class="gallery-lightbox__nav gallery-lightbox__nav--next"
                                    @click="nextImage()"
                                    x-show="currentIndex < images.length - 1">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </template>
                    
                    <!-- Main Image -->
                    <img :src="currentImage?.url" 
                         :alt="currentImage?.alt || currentImage?.caption || 'Gallery image'"
                         class="gallery-lightbox__image"
                         @load="imageLoaded = true"
                         x-show="imageLoaded">
                    
                    <!-- Loading Spinner -->
                    <div x-show="!imageLoaded" class="d-flex justify-content-center align-items-center" style="height: 400px;">
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    
                    <!-- Caption -->
                    <div x-show="currentImage?.caption" class="gallery-lightbox__caption">
                        <p x-text="currentImage?.caption" class="mb-0"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Thumbnail Navigation (Optional) -->
    <div x-show="showThumbnails && images.length > 1" class="gallery-thumbnails">
        <template x-for="(image, index) in images" :key="image.id || index">
            <div class="gallery-thumbnails__item"
                 :class="{ 'gallery-thumbnails__item--active': index === currentIndex }"
                 @click="goToImage(index)">
                <img :src="image.thumbnail || image.url" 
                     :alt="image.alt || image.caption || 'Thumbnail'">
            </div>
        </template>
    </div>
</div>

<script>
function modernGallery(initialImages = []) {
    return {
        images: initialImages,
        loading: true,
        lightboxOpen: false,
        currentIndex: 0,
        imageLoaded: false,
        showThumbnails: false,
        
        init() {
            // Initialize lazy loading
            this.initializeLazyLoading();
            
            // Set loading to false after a short delay
            setTimeout(() => {
                this.loading = false;
            }, 300);
            
            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (this.lightboxOpen) {
                    if (e.key === 'ArrowLeft') {
                        this.previousImage();
                    } else if (e.key === 'ArrowRight') {
                        this.nextImage();
                    }
                }
            });
        },
        
        get currentImage() {
            return this.images[this.currentIndex];
        },
        
        openLightbox(index) {
            this.currentIndex = index;
            this.lightboxOpen = true;
            this.imageLoaded = false;
            document.body.style.overflow = 'hidden';
        },
        
        closeLightbox() {
            this.lightboxOpen = false;
            document.body.style.overflow = '';
        },
        
        nextImage() {
            if (this.currentIndex < this.images.length - 1) {
                this.currentIndex++;
                this.imageLoaded = false;
            }
        },
        
        previousImage() {
            if (this.currentIndex > 0) {
                this.currentIndex--;
                this.imageLoaded = false;
            }
        },
        
        goToImage(index) {
            this.currentIndex = index;
            this.imageLoaded = false;
        },
        
        initializeLazyLoading() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                this.$nextTick(() => {
                    this.$el.querySelectorAll('img[data-src]').forEach(img => {
                        imageObserver.observe(img);
                    });
                });
            }
        },
        
        addImage(image) {
            this.images.push(image);
        },
        
        removeImage(index) {
            this.images.splice(index, 1);
            if (this.currentIndex >= this.images.length) {
                this.currentIndex = Math.max(0, this.images.length - 1);
            }
        },
        
        updateImage(index, image) {
            this.images[index] = { ...this.images[index], ...image };
        }
    }
}
</script>
