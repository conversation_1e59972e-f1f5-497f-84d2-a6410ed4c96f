{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - CozyWish{% endblock %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/booking-calendar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="calendar-container">
    <!-- Calendar Header -->
    <div class="calendar-page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="page-title">
                        <i class="fas fa-calendar-alt"></i>
                        {{ page_title }}
                    </h1>
                    <p class="page-subtitle">Manage your appointments and bookings</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="calendar-actions">
                        <button class="btn btn-outline-primary btn-sm" id="exportCalendar">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="btn btn-outline-success btn-sm" id="syncCalendar">
                            <i class="fas fa-sync"></i> Sync
                        </button>
                        <button class="btn btn-primary btn-sm" id="addNewAppointment">
                            <i class="fas fa-plus"></i> New Appointment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Component -->
    <div class="container-fluid calendar-main">
        <div id="bookingCalendar" class="booking-calendar-wrapper">
            <!-- Calendar will be rendered here by JavaScript -->
            <div class="calendar-loading">
                <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading calendar...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalAppointments">0</h3>
                        <p>Total Appointments</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="upcomingAppointments">0</h3>
                        <p>Upcoming</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="confirmedAppointments">0</h3>
                        <p>Confirmed</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="revenuePotential">$0</h3>
                        <p>Revenue</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Modal -->
<div class="modal fade" id="appointmentModal" tabindex="-1" aria-labelledby="appointmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="appointmentModalLabel">
                    <i class="fas fa-calendar-plus"></i>
                    New Appointment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="appointmentForm">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentTitle" class="form-label">Title</label>
                                <input type="text" class="form-control" id="appointmentTitle" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentDate" class="form-label">Date</label>
                                <input type="date" class="form-control" id="appointmentDate" name="date" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentStartTime" class="form-label">Start Time</label>
                                <input type="time" class="form-control" id="appointmentStartTime" name="start_time" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentDuration" class="form-label">Duration (minutes)</label>
                                <select class="form-select" id="appointmentDuration" name="duration" required>
                                    <option value="30">30 minutes</option>
                                    <option value="60" selected>1 hour</option>
                                    <option value="90">1.5 hours</option>
                                    <option value="120">2 hours</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentVenue" class="form-label">Venue</label>
                                <select class="form-select" id="appointmentVenue" name="venue" required>
                                    <option value="">Select venue...</option>
                                    {% for venue in user.service_provider_profile.venues.all %}
                                        <option value="{{ venue.id }}">{{ venue.business_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentPrice" class="form-label">Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="appointmentPrice" name="price" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="appointmentNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="appointmentNotes" name="notes" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentMaxAttendees" class="form-label">Max Attendees</label>
                                <input type="number" class="form-control" id="appointmentMaxAttendees" name="max_attendees" min="1" max="20" value="1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentRecurrence" class="form-label">Recurrence</label>
                                <select class="form-select" id="appointmentRecurrence" name="recurrence">
                                    <option value="">No recurrence</option>
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveAppointment">
                    <i class="fas fa-save"></i> Save Appointment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportModalLabel">
                    <i class="fas fa-download"></i>
                    Export Calendar
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportFormat" class="form-label">Format</label>
                        <select class="form-select" id="exportFormat" name="format" required>
                            <option value="ical">iCal (.ics)</option>
                            <option value="csv">CSV (.csv)</option>
                            <option value="pdf">PDF (.pdf)</option>
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="exportStartDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="exportStartDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="exportEndDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="exportEndDate" name="end_date" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeCancelled" name="include_cancelled">
                            <label class="form-check-label" for="includeCancelled">
                                Include cancelled appointments
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="downloadExport">
                    <i class="fas fa-download"></i> Download
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Sync Modal -->
<div class="modal fade" id="syncModal" tabindex="-1" aria-labelledby="syncModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="syncModalLabel">
                    <i class="fas fa-sync"></i>
                    Calendar Sync
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="sync-options">
                    <h6>Sync with External Calendars</h6>
                    <div class="sync-option">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <i class="fab fa-google text-danger"></i>
                                <span class="ms-2">Google Calendar</span>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" id="syncGoogle">
                                <i class="fas fa-link"></i> Connect
                            </button>
                        </div>
                    </div>
                    <div class="sync-option">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <i class="fab fa-microsoft text-primary"></i>
                                <span class="ms-2">Outlook Calendar</span>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" id="syncOutlook">
                                <i class="fas fa-link"></i> Connect
                            </button>
                        </div>
                    </div>
                    <div class="sync-option">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <i class="fab fa-apple text-dark"></i>
                                <span class="ms-2">Apple Calendar</span>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" id="syncApple">
                                <i class="fas fa-link"></i> Connect
                            </button>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="sync-status">
                    <h6>Sync Status</h6>
                    <div class="sync-status-item">
                        <span class="badge bg-success">Connected</span>
                        <span class="ms-2">Last sync: 2 hours ago</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="forceSyncNow">
                    <i class="fas fa-sync"></i> Sync Now
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-spinner"></div>
</div>

{% endblock %}

{% block extra_js %}
    <script src="{% static 'js/booking-calendar.js' %}"></script>
    <script>
        // Initialize calendar with Django data
        document.addEventListener('DOMContentLoaded', function() {
            // Calendar configuration from Django
            const calendarConfig = {{ calendar_settings|safe }};
            
            // Initialize the calendar
            window.bookingCalendar = new BookingCalendar('bookingCalendar', {
                apiEndpoint: '{{ api_endpoint }}',
                ...calendarConfig
            });
            
            // Load statistics
            loadCalendarStatistics();
            
            // Bind modal events
            bindModalEvents();
            
            // Auto-refresh every 5 minutes
            setInterval(function() {
                if (window.bookingCalendar) {
                    window.bookingCalendar.loadAppointments();
                }
            }, 300000);
        });
        
        // Load calendar statistics
        function loadCalendarStatistics() {
            fetch('/api/booking-calendar/statistics/', {
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('totalAppointments').textContent = data.total_appointments;
                document.getElementById('upcomingAppointments').textContent = data.upcoming_appointments;
                document.getElementById('confirmedAppointments').textContent = data.confirmed_appointments;
                document.getElementById('revenuePotential').textContent = '$' + data.revenue_potential.toFixed(2);
            })
            .catch(error => {
                console.error('Error loading statistics:', error);
            });
        }
        
        // Bind modal events
        function bindModalEvents() {
            // New appointment modal
            document.getElementById('addNewAppointment').addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('appointmentModal'));
                modal.show();
            });
            
            // Save appointment
            document.getElementById('saveAppointment').addEventListener('click', function() {
                saveAppointment();
            });
            
            // Export modal
            document.getElementById('exportCalendar').addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('exportModal'));
                
                // Set default dates
                const today = new Date();
                const nextMonth = new Date(today);
                nextMonth.setMonth(nextMonth.getMonth() + 1);
                
                document.getElementById('exportStartDate').value = today.toISOString().split('T')[0];
                document.getElementById('exportEndDate').value = nextMonth.toISOString().split('T')[0];
                
                modal.show();
            });
            
            // Download export
            document.getElementById('downloadExport').addEventListener('click', function() {
                downloadExport();
            });
            
            // Sync modal
            document.getElementById('syncCalendar').addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('syncModal'));
                modal.show();
            });
            
            // Force sync
            document.getElementById('forceSyncNow').addEventListener('click', function() {
                forceSyncNow();
            });
        }
        
        // Save appointment
        function saveAppointment() {
            const formData = new FormData(document.getElementById('appointmentForm'));
            const data = Object.fromEntries(formData.entries());
            
            showLoading();
            
            fetch('/api/booking-calendar/appointments/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                
                if (data.success) {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('appointmentModal'));
                    modal.hide();
                    
                    // Refresh calendar
                    window.bookingCalendar.loadAppointments();
                    
                    showNotification('Appointment created successfully', 'success');
                } else {
                    showNotification(data.error || 'Error creating appointment', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error saving appointment:', error);
                showNotification('Error saving appointment', 'error');
            });
        }
        
        // Download export
        function downloadExport() {
            const formData = new FormData(document.getElementById('exportForm'));
            const params = new URLSearchParams(formData);
            
            showLoading();
            
            fetch('/api/booking-calendar/export/?' + params.toString(), {
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => {
                hideLoading();
                
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Export failed');
                }
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'calendar_export.' + document.getElementById('exportFormat').value;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
                modal.hide();
            })
            .catch(error => {
                hideLoading();
                console.error('Error downloading export:', error);
                showNotification('Error downloading export', 'error');
            });
        }
        
        // Force sync now
        function forceSyncNow() {
            showLoading();
            
            fetch('/api/booking-calendar/sync/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                
                if (data.success) {
                    showNotification('Calendar synced successfully', 'success');
                } else {
                    showNotification(data.error || 'Sync failed', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error syncing calendar:', error);
                showNotification('Error syncing calendar', 'error');
            });
        }
        
        // Utility functions
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
        
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
        
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
        
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    </script>
{% endblock %} 