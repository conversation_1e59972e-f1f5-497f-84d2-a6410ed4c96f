{"name": "cozy<PERSON>sh", "version": "1.0.0", "description": "CozyWish - Book Beauty & Wellness Services", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "watch": "vite build --watch", "clean": "rm -rf static/dist"}, "devDependencies": {"vite": "^5.0.10", "@vitejs/plugin-legacy": "^5.2.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "sass": "^1.69.5", "terser": "^5.26.0"}, "dependencies": {"alpinejs": "^3.13.3", "bootstrap": "^5.3.2", "@popperjs/core": "^2.11.8", "htmx.org": "^1.9.10", "choices.js": "^10.2.0", "@glidejs/glide": "^3.6.0", "leaflet": "^1.9.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}