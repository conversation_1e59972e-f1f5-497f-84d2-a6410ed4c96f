# Generated by Django 5.2.4 on 2025-07-06 05:11

import django.db.models.deletion
import django.utils.timezone
import modelcluster.fields
import wagtail.contrib.forms.models
import wagtail.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("venues_app", "0002_add_star_ratings_integration"),
        ("wagtailcore", "0094_alter_page_locale"),
        ("wagtailimages", "0027_image_description"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BlogIndexPage",
            fields=[
                (
                    "page_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="wagtailcore.page",
                    ),
                ),
                (
                    "intro",
                    models.TextField(
                        blank=True,
                        help_text="A short introduction to this page. This will appear in search results and page listings.",
                    ),
                ),
                (
                    "body",
                    wagtail.fields.StreamField(
                        [
                            ("heading", 0),
                            ("paragraph", 1),
                            ("image", 2),
                            ("quote", 3),
                            ("embed", 4),
                            ("raw_html", 5),
                            ("call_to_action", 10),
                            ("two_columns", 12),
                            ("three_columns", 13),
                        ],
                        blank=True,
                        block_lookup={
                            0: (
                                "wagtail.blocks.CharBlock",
                                (),
                                {"form_classname": "title", "icon": "title"},
                            ),
                            1: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"icon": "pilcrow"},
                            ),
                            2: (
                                "wagtail.images.blocks.ImageChooserBlock",
                                (),
                                {"icon": "image"},
                            ),
                            3: (
                                "wagtail.blocks.BlockQuoteBlock",
                                (),
                                {"icon": "openquote"},
                            ),
                            4: (
                                "wagtail.embeds.blocks.EmbedBlock",
                                (),
                                {"icon": "media"},
                            ),
                            5: (
                                "wagtail.blocks.RawHTMLBlock",
                                (),
                                {"icon": "code", "label": "Raw HTML"},
                            ),
                            6: ("wagtail.blocks.CharBlock", (), {"required": True}),
                            7: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"required": False},
                            ),
                            8: ("wagtail.blocks.URLBlock", (), {"required": True}),
                            9: (
                                "wagtail.blocks.ChoiceBlock",
                                [],
                                {
                                    "choices": [
                                        ("primary", "Primary"),
                                        ("secondary", "Secondary"),
                                        ("success", "Success"),
                                        ("danger", "Danger"),
                                    ]
                                },
                            ),
                            10: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("title", 6),
                                        ("text", 7),
                                        ("button_text", 6),
                                        ("button_url", 8),
                                        ("button_style", 9),
                                    ]
                                ],
                                {"icon": "pick", "label": "Call to Action"},
                            ),
                            11: ("wagtail.blocks.RichTextBlock", (), {}),
                            12: (
                                "wagtail.blocks.StructBlock",
                                [[("left_column", 11), ("right_column", 11)]],
                                {"icon": "placeholder", "label": "Two Columns"},
                            ),
                            13: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("left_column", 11),
                                        ("middle_column", 11),
                                        ("right_column", 11),
                                    ]
                                ],
                                {"icon": "placeholder", "label": "Three Columns"},
                            ),
                        },
                        help_text="The main content of this page",
                        verbose_name="Page content",
                    ),
                ),
                (
                    "meta_description",
                    models.TextField(
                        blank=True,
                        help_text="Description for search engines (recommended: 120-160 characters)",
                        max_length=160,
                        verbose_name="Meta Description",
                    ),
                ),
                (
                    "search_keywords",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated keywords for search engines",
                        max_length=255,
                        verbose_name="Meta Keywords",
                    ),
                ),
                (
                    "featured",
                    models.BooleanField(
                        default=False,
                        help_text="Feature this page in listings and highlights",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "posts_per_page",
                    models.PositiveIntegerField(
                        default=6, help_text="Number of blog posts to show per page"
                    ),
                ),
            ],
            options={
                "verbose_name": "Blog Index Page",
            },
            bases=("wagtailcore.page",),
        ),
        migrations.CreateModel(
            name="BlogTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, unique=True)),
                ("slug", models.SlugField(unique=True)),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Optional description for this tag"
                    ),
                ),
            ],
            options={
                "verbose_name": "Blog Tag",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="CMSSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "site_tagline",
                    models.CharField(
                        default="Find and book cozy venues for your special occasions",
                        help_text="Site tagline/description",
                        max_length=255,
                    ),
                ),
                (
                    "contact_email",
                    models.EmailField(
                        default="<EMAIL>",
                        help_text="Main contact email address",
                        max_length=254,
                    ),
                ),
                (
                    "contact_phone",
                    models.CharField(
                        blank=True, help_text="Main contact phone number", max_length=20
                    ),
                ),
                ("facebook_url", models.URLField(blank=True)),
                ("twitter_url", models.URLField(blank=True)),
                ("instagram_url", models.URLField(blank=True)),
                ("linkedin_url", models.URLField(blank=True)),
                (
                    "google_analytics_id",
                    models.CharField(
                        blank=True,
                        help_text="Google Analytics tracking ID (e.g. GA-XXXXXXXX-X)",
                        max_length=20,
                    ),
                ),
                (
                    "footer_text",
                    wagtail.fields.RichTextField(
                        blank=True, help_text="Footer text/copyright notice"
                    ),
                ),
            ],
            options={
                "verbose_name": "CMS Settings",
            },
        ),
        migrations.CreateModel(
            name="ContactFormPage",
            fields=[
                (
                    "page_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="wagtailcore.page",
                    ),
                ),
                (
                    "intro",
                    models.TextField(
                        blank=True, help_text="Introduction text for the contact form"
                    ),
                ),
                (
                    "thank_you_text",
                    wagtail.fields.RichTextField(
                        blank=True, help_text="Text to display after form submission"
                    ),
                ),
            ],
            options={
                "verbose_name": "Contact Form Page",
            },
            bases=(wagtail.contrib.forms.models.FormMixin, "wagtailcore.page"),
        ),
        migrations.CreateModel(
            name="ContentPage",
            fields=[
                (
                    "page_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="wagtailcore.page",
                    ),
                ),
                (
                    "intro",
                    models.TextField(
                        blank=True,
                        help_text="A short introduction to this page. This will appear in search results and page listings.",
                    ),
                ),
                (
                    "body",
                    wagtail.fields.StreamField(
                        [
                            ("heading", 0),
                            ("paragraph", 1),
                            ("image", 2),
                            ("quote", 3),
                            ("embed", 4),
                            ("raw_html", 5),
                            ("call_to_action", 10),
                            ("two_columns", 12),
                            ("three_columns", 13),
                        ],
                        blank=True,
                        block_lookup={
                            0: (
                                "wagtail.blocks.CharBlock",
                                (),
                                {"form_classname": "title", "icon": "title"},
                            ),
                            1: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"icon": "pilcrow"},
                            ),
                            2: (
                                "wagtail.images.blocks.ImageChooserBlock",
                                (),
                                {"icon": "image"},
                            ),
                            3: (
                                "wagtail.blocks.BlockQuoteBlock",
                                (),
                                {"icon": "openquote"},
                            ),
                            4: (
                                "wagtail.embeds.blocks.EmbedBlock",
                                (),
                                {"icon": "media"},
                            ),
                            5: (
                                "wagtail.blocks.RawHTMLBlock",
                                (),
                                {"icon": "code", "label": "Raw HTML"},
                            ),
                            6: ("wagtail.blocks.CharBlock", (), {"required": True}),
                            7: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"required": False},
                            ),
                            8: ("wagtail.blocks.URLBlock", (), {"required": True}),
                            9: (
                                "wagtail.blocks.ChoiceBlock",
                                [],
                                {
                                    "choices": [
                                        ("primary", "Primary"),
                                        ("secondary", "Secondary"),
                                        ("success", "Success"),
                                        ("danger", "Danger"),
                                    ]
                                },
                            ),
                            10: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("title", 6),
                                        ("text", 7),
                                        ("button_text", 6),
                                        ("button_url", 8),
                                        ("button_style", 9),
                                    ]
                                ],
                                {"icon": "pick", "label": "Call to Action"},
                            ),
                            11: ("wagtail.blocks.RichTextBlock", (), {}),
                            12: (
                                "wagtail.blocks.StructBlock",
                                [[("left_column", 11), ("right_column", 11)]],
                                {"icon": "placeholder", "label": "Two Columns"},
                            ),
                            13: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("left_column", 11),
                                        ("middle_column", 11),
                                        ("right_column", 11),
                                    ]
                                ],
                                {"icon": "placeholder", "label": "Three Columns"},
                            ),
                        },
                        help_text="The main content of this page",
                        verbose_name="Page content",
                    ),
                ),
                (
                    "meta_description",
                    models.TextField(
                        blank=True,
                        help_text="Description for search engines (recommended: 120-160 characters)",
                        max_length=160,
                        verbose_name="Meta Description",
                    ),
                ),
                (
                    "search_keywords",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated keywords for search engines",
                        max_length=255,
                        verbose_name="Meta Keywords",
                    ),
                ),
                (
                    "featured",
                    models.BooleanField(
                        default=False,
                        help_text="Feature this page in listings and highlights",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Content Page",
            },
            bases=("wagtailcore.page",),
        ),
        migrations.CreateModel(
            name="VenueListingPage",
            fields=[
                (
                    "page_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="wagtailcore.page",
                    ),
                ),
                (
                    "intro",
                    models.TextField(
                        blank=True,
                        help_text="A short introduction to this page. This will appear in search results and page listings.",
                    ),
                ),
                (
                    "body",
                    wagtail.fields.StreamField(
                        [
                            ("heading", 0),
                            ("paragraph", 1),
                            ("image", 2),
                            ("quote", 3),
                            ("embed", 4),
                            ("raw_html", 5),
                            ("call_to_action", 10),
                            ("two_columns", 12),
                            ("three_columns", 13),
                        ],
                        blank=True,
                        block_lookup={
                            0: (
                                "wagtail.blocks.CharBlock",
                                (),
                                {"form_classname": "title", "icon": "title"},
                            ),
                            1: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"icon": "pilcrow"},
                            ),
                            2: (
                                "wagtail.images.blocks.ImageChooserBlock",
                                (),
                                {"icon": "image"},
                            ),
                            3: (
                                "wagtail.blocks.BlockQuoteBlock",
                                (),
                                {"icon": "openquote"},
                            ),
                            4: (
                                "wagtail.embeds.blocks.EmbedBlock",
                                (),
                                {"icon": "media"},
                            ),
                            5: (
                                "wagtail.blocks.RawHTMLBlock",
                                (),
                                {"icon": "code", "label": "Raw HTML"},
                            ),
                            6: ("wagtail.blocks.CharBlock", (), {"required": True}),
                            7: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"required": False},
                            ),
                            8: ("wagtail.blocks.URLBlock", (), {"required": True}),
                            9: (
                                "wagtail.blocks.ChoiceBlock",
                                [],
                                {
                                    "choices": [
                                        ("primary", "Primary"),
                                        ("secondary", "Secondary"),
                                        ("success", "Success"),
                                        ("danger", "Danger"),
                                    ]
                                },
                            ),
                            10: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("title", 6),
                                        ("text", 7),
                                        ("button_text", 6),
                                        ("button_url", 8),
                                        ("button_style", 9),
                                    ]
                                ],
                                {"icon": "pick", "label": "Call to Action"},
                            ),
                            11: ("wagtail.blocks.RichTextBlock", (), {}),
                            12: (
                                "wagtail.blocks.StructBlock",
                                [[("left_column", 11), ("right_column", 11)]],
                                {"icon": "placeholder", "label": "Two Columns"},
                            ),
                            13: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("left_column", 11),
                                        ("middle_column", 11),
                                        ("right_column", 11),
                                    ]
                                ],
                                {"icon": "placeholder", "label": "Three Columns"},
                            ),
                        },
                        help_text="The main content of this page",
                        verbose_name="Page content",
                    ),
                ),
                (
                    "meta_description",
                    models.TextField(
                        blank=True,
                        help_text="Description for search engines (recommended: 120-160 characters)",
                        max_length=160,
                        verbose_name="Meta Description",
                    ),
                ),
                (
                    "search_keywords",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated keywords for search engines",
                        max_length=255,
                        verbose_name="Meta Keywords",
                    ),
                ),
                (
                    "featured",
                    models.BooleanField(
                        default=False,
                        help_text="Feature this page in listings and highlights",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "venues_per_page",
                    models.PositiveIntegerField(
                        default=12, help_text="Number of venues to show per page"
                    ),
                ),
                (
                    "show_filters",
                    models.BooleanField(
                        default=True, help_text="Show filtering options on this page"
                    ),
                ),
            ],
            options={
                "verbose_name": "Venue Listing Page",
            },
            bases=("wagtailcore.page",),
        ),
        migrations.CreateModel(
            name="BlogPage",
            fields=[
                (
                    "page_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="wagtailcore.page",
                    ),
                ),
                (
                    "intro",
                    models.TextField(
                        blank=True,
                        help_text="A short introduction to this page. This will appear in search results and page listings.",
                    ),
                ),
                (
                    "body",
                    wagtail.fields.StreamField(
                        [
                            ("heading", 0),
                            ("paragraph", 1),
                            ("image", 2),
                            ("quote", 3),
                            ("embed", 4),
                            ("raw_html", 5),
                            ("call_to_action", 10),
                            ("two_columns", 12),
                            ("three_columns", 13),
                        ],
                        blank=True,
                        block_lookup={
                            0: (
                                "wagtail.blocks.CharBlock",
                                (),
                                {"form_classname": "title", "icon": "title"},
                            ),
                            1: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"icon": "pilcrow"},
                            ),
                            2: (
                                "wagtail.images.blocks.ImageChooserBlock",
                                (),
                                {"icon": "image"},
                            ),
                            3: (
                                "wagtail.blocks.BlockQuoteBlock",
                                (),
                                {"icon": "openquote"},
                            ),
                            4: (
                                "wagtail.embeds.blocks.EmbedBlock",
                                (),
                                {"icon": "media"},
                            ),
                            5: (
                                "wagtail.blocks.RawHTMLBlock",
                                (),
                                {"icon": "code", "label": "Raw HTML"},
                            ),
                            6: ("wagtail.blocks.CharBlock", (), {"required": True}),
                            7: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"required": False},
                            ),
                            8: ("wagtail.blocks.URLBlock", (), {"required": True}),
                            9: (
                                "wagtail.blocks.ChoiceBlock",
                                [],
                                {
                                    "choices": [
                                        ("primary", "Primary"),
                                        ("secondary", "Secondary"),
                                        ("success", "Success"),
                                        ("danger", "Danger"),
                                    ]
                                },
                            ),
                            10: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("title", 6),
                                        ("text", 7),
                                        ("button_text", 6),
                                        ("button_url", 8),
                                        ("button_style", 9),
                                    ]
                                ],
                                {"icon": "pick", "label": "Call to Action"},
                            ),
                            11: ("wagtail.blocks.RichTextBlock", (), {}),
                            12: (
                                "wagtail.blocks.StructBlock",
                                [[("left_column", 11), ("right_column", 11)]],
                                {"icon": "placeholder", "label": "Two Columns"},
                            ),
                            13: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("left_column", 11),
                                        ("middle_column", 11),
                                        ("right_column", 11),
                                    ]
                                ],
                                {"icon": "placeholder", "label": "Three Columns"},
                            ),
                        },
                        help_text="The main content of this page",
                        verbose_name="Page content",
                    ),
                ),
                (
                    "meta_description",
                    models.TextField(
                        blank=True,
                        help_text="Description for search engines (recommended: 120-160 characters)",
                        max_length=160,
                        verbose_name="Meta Description",
                    ),
                ),
                (
                    "search_keywords",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated keywords for search engines",
                        max_length=255,
                        verbose_name="Meta Keywords",
                    ),
                ),
                (
                    "featured",
                    models.BooleanField(
                        default=False,
                        help_text="Feature this page in listings and highlights",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "published_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="When this post was published",
                    ),
                ),
                (
                    "excerpt",
                    models.TextField(
                        help_text="Short excerpt for listings (max 300 characters)",
                        max_length=300,
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        help_text="Blog post author",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="blog_posts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "featured_image",
                    models.ForeignKey(
                        blank=True,
                        help_text="Featured image for this blog post",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="wagtailimages.image",
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Tags for this blog post",
                        to="cms_app.blogtag",
                    ),
                ),
            ],
            options={
                "verbose_name": "Blog Post",
                "ordering": ["-published_date"],
            },
            bases=("wagtailcore.page",),
        ),
        migrations.CreateModel(
            name="ContactFormField",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "sort_order",
                    models.IntegerField(blank=True, editable=False, null=True),
                ),
                (
                    "clean_name",
                    models.CharField(
                        blank=True,
                        default="",
                        help_text="Safe name of the form field, the label converted to ascii_snake_case",
                        max_length=255,
                        verbose_name="name",
                    ),
                ),
                (
                    "label",
                    models.CharField(
                        help_text="The label of the form field",
                        max_length=255,
                        verbose_name="label",
                    ),
                ),
                (
                    "field_type",
                    models.CharField(
                        choices=[
                            ("singleline", "Single line text"),
                            ("multiline", "Multi-line text"),
                            ("email", "Email"),
                            ("number", "Number"),
                            ("url", "URL"),
                            ("checkbox", "Checkbox"),
                            ("checkboxes", "Checkboxes"),
                            ("dropdown", "Drop down"),
                            ("multiselect", "Multiple select"),
                            ("radio", "Radio buttons"),
                            ("date", "Date"),
                            ("datetime", "Date/time"),
                            ("hidden", "Hidden field"),
                        ],
                        max_length=16,
                        verbose_name="field type",
                    ),
                ),
                (
                    "required",
                    models.BooleanField(default=True, verbose_name="required"),
                ),
                (
                    "choices",
                    models.TextField(
                        blank=True,
                        help_text="Comma or new line separated list of choices. Only applicable in checkboxes, radio and dropdown.",
                        verbose_name="choices",
                    ),
                ),
                (
                    "default_value",
                    models.TextField(
                        blank=True,
                        help_text="Default value. Comma or new line separated values supported for checkboxes.",
                        verbose_name="default value",
                    ),
                ),
                (
                    "help_text",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="help text"
                    ),
                ),
                (
                    "page",
                    modelcluster.fields.ParentalKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="form_fields",
                        to="cms_app.contactformpage",
                    ),
                ),
            ],
            options={
                "ordering": ["sort_order"],
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="HomePage",
            fields=[
                (
                    "page_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="wagtailcore.page",
                    ),
                ),
                (
                    "intro",
                    models.TextField(
                        blank=True,
                        help_text="A short introduction to this page. This will appear in search results and page listings.",
                    ),
                ),
                (
                    "body",
                    wagtail.fields.StreamField(
                        [
                            ("heading", 0),
                            ("paragraph", 1),
                            ("image", 2),
                            ("quote", 3),
                            ("embed", 4),
                            ("raw_html", 5),
                            ("call_to_action", 10),
                            ("two_columns", 12),
                            ("three_columns", 13),
                        ],
                        blank=True,
                        block_lookup={
                            0: (
                                "wagtail.blocks.CharBlock",
                                (),
                                {"form_classname": "title", "icon": "title"},
                            ),
                            1: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"icon": "pilcrow"},
                            ),
                            2: (
                                "wagtail.images.blocks.ImageChooserBlock",
                                (),
                                {"icon": "image"},
                            ),
                            3: (
                                "wagtail.blocks.BlockQuoteBlock",
                                (),
                                {"icon": "openquote"},
                            ),
                            4: (
                                "wagtail.embeds.blocks.EmbedBlock",
                                (),
                                {"icon": "media"},
                            ),
                            5: (
                                "wagtail.blocks.RawHTMLBlock",
                                (),
                                {"icon": "code", "label": "Raw HTML"},
                            ),
                            6: ("wagtail.blocks.CharBlock", (), {"required": True}),
                            7: (
                                "wagtail.blocks.RichTextBlock",
                                (),
                                {"required": False},
                            ),
                            8: ("wagtail.blocks.URLBlock", (), {"required": True}),
                            9: (
                                "wagtail.blocks.ChoiceBlock",
                                [],
                                {
                                    "choices": [
                                        ("primary", "Primary"),
                                        ("secondary", "Secondary"),
                                        ("success", "Success"),
                                        ("danger", "Danger"),
                                    ]
                                },
                            ),
                            10: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("title", 6),
                                        ("text", 7),
                                        ("button_text", 6),
                                        ("button_url", 8),
                                        ("button_style", 9),
                                    ]
                                ],
                                {"icon": "pick", "label": "Call to Action"},
                            ),
                            11: ("wagtail.blocks.RichTextBlock", (), {}),
                            12: (
                                "wagtail.blocks.StructBlock",
                                [[("left_column", 11), ("right_column", 11)]],
                                {"icon": "placeholder", "label": "Two Columns"},
                            ),
                            13: (
                                "wagtail.blocks.StructBlock",
                                [
                                    [
                                        ("left_column", 11),
                                        ("middle_column", 11),
                                        ("right_column", 11),
                                    ]
                                ],
                                {"icon": "placeholder", "label": "Three Columns"},
                            ),
                        },
                        help_text="The main content of this page",
                        verbose_name="Page content",
                    ),
                ),
                (
                    "meta_description",
                    models.TextField(
                        blank=True,
                        help_text="Description for search engines (recommended: 120-160 characters)",
                        max_length=160,
                        verbose_name="Meta Description",
                    ),
                ),
                (
                    "search_keywords",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated keywords for search engines",
                        max_length=255,
                        verbose_name="Meta Keywords",
                    ),
                ),
                (
                    "featured",
                    models.BooleanField(
                        default=False,
                        help_text="Feature this page in listings and highlights",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "hero_title",
                    models.CharField(
                        default="Welcome to CozyWish",
                        help_text="Main hero title",
                        max_length=255,
                    ),
                ),
                (
                    "hero_subtitle",
                    models.TextField(
                        default="Find and book cozy venues for your special occasions",
                        help_text="Hero subtitle/description",
                    ),
                ),
                (
                    "hero_cta_text",
                    models.CharField(
                        blank=True,
                        default="Find Venues",
                        max_length=50,
                        verbose_name="Hero CTA Text",
                    ),
                ),
                (
                    "hero_cta_url",
                    models.URLField(
                        blank=True, default="/venues/", verbose_name="Hero CTA URL"
                    ),
                ),
                (
                    "hero_image",
                    models.ForeignKey(
                        blank=True,
                        help_text="Hero background image",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="wagtailimages.image",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=("wagtailcore.page",),
        ),
        migrations.CreateModel(
            name="HomePageFeaturedVenue",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "sort_order",
                    models.IntegerField(blank=True, editable=False, null=True),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Optional custom description for this featured venue",
                        max_length=200,
                    ),
                ),
                (
                    "page",
                    modelcluster.fields.ParentalKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="featured_venues",
                        to="cms_app.homepage",
                    ),
                ),
                (
                    "venue",
                    models.ForeignKey(
                        help_text="Choose a venue to feature",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="venues_app.venue",
                    ),
                ),
            ],
            options={
                "ordering": ["sort_order"],
                "abstract": False,
            },
        ),
    ]
