from django.contrib import admin
from .models import BlogTag


# Note: Most Wagtail models are managed through the Wagtail admin interface
# BlogTag is registered as a snippet in models.py using @register_snippet
# so it will appear in the Wagtail admin automatically


# For the admin integration with the Django admin (for non-CMS models if needed)
# Note: Most Wagtail models are managed through the Wagtail admin, not Django admin

# Custom admin for blog tags in Django admin (if needed for some reason)
@admin.register(BlogTag)
class BlogTagAdmin(admin.ModelAdmin):
    """Django admin interface for blog tags (backup/fallback)"""
    list_display = ['name', 'slug', 'description']
    list_filter = ['name']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['name']
