from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model

from wagtail.models import Page, Orderable
from wagtail.fields import <PERSON><PERSON>extField, StreamField
from wagtail.admin.panels import FieldPanel, InlinePanel, MultiFieldPanel
from wagtail.snippets.models import register_snippet
from wagtail import blocks
from wagtail.images.blocks import ImageChooserBlock
from wagtail.embeds.blocks import EmbedBlock
from wagtail.search import index
from wagtail.contrib.forms.models import AbstractForm, AbstractFormField
from wagtail.contrib.forms.panels import FormSubmissionsPanel
from wagtail.contrib.settings.models import BaseGenericSetting, register_setting
from wagtail.admin.panels import TabbedInterface, ObjectList

from modelcluster.fields import Parental<PERSON>ey
from modelcluster.models import ClusterableModel

User = get_user_model()


# --- Content Blocks for StreamField ---
class ContentBlocks(blocks.StreamBlock):
    """Rich content blocks for flexible page content"""
    
    heading = blocks.CharBlock(classname="title", icon="title")
    paragraph = blocks.RichTextBlock(icon="pilcrow")
    image = ImageChooserBlock(icon="image")
    quote = blocks.BlockQuoteBlock(icon="openquote")
    embed = EmbedBlock(icon="media")
    raw_html = blocks.RawHTMLBlock(icon="code", label="Raw HTML")
    
    # Custom content blocks
    call_to_action = blocks.StructBlock([
        ('title', blocks.CharBlock(required=True)),
        ('text', blocks.RichTextBlock(required=False)),
        ('button_text', blocks.CharBlock(required=True)),
        ('button_url', blocks.URLBlock(required=True)),
        ('button_style', blocks.ChoiceBlock(choices=[
            ('primary', 'Primary'),
            ('secondary', 'Secondary'),
            ('success', 'Success'),
            ('danger', 'Danger'),
        ], default='primary')),
    ], icon="pick", label="Call to Action")
    
    two_columns = blocks.StructBlock([
        ('left_column', blocks.RichTextBlock()),
        ('right_column', blocks.RichTextBlock()),
    ], icon="placeholder", label="Two Columns")
    
    three_columns = blocks.StructBlock([
        ('left_column', blocks.RichTextBlock()),
        ('middle_column', blocks.RichTextBlock()),
        ('right_column', blocks.RichTextBlock()),
    ], icon="placeholder", label="Three Columns")


# --- Base Page ---
class BasePage(Page):
    """Base page model with common fields and functionality"""
    
    # Content
    intro = models.TextField(
        help_text="A short introduction to this page. This will appear in search results and page listings.",
        blank=True
    )
    
    body = StreamField(
        ContentBlocks(),
        verbose_name="Page content",
        blank=True,
        use_json_field=True,
        help_text="The main content of this page"
    )
    
    # SEO and Meta
    # Note: seo_title is already provided by Wagtail's Page model
    
    meta_description = models.TextField(
        "Meta Description",
        max_length=160,
        blank=True,
        help_text="Description for search engines (recommended: 120-160 characters)"
    )
    
    search_keywords = models.CharField(
        "Meta Keywords",
        max_length=255,
        blank=True,
        help_text="Comma-separated keywords for search engines"
    )
    
    # Publishing
    featured = models.BooleanField(
        default=False,
        help_text="Feature this page in listings and highlights"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Search
    search_fields = Page.search_fields + [
        index.SearchField('intro'),
        index.SearchField('body'),
        index.SearchField('search_keywords'),
    ]
    
    # Admin panels
    content_panels = Page.content_panels + [
        FieldPanel('intro'),
        FieldPanel('body'),
    ]
    
    promote_panels = Page.promote_panels + [
        MultiFieldPanel([
            FieldPanel('meta_description'),
            FieldPanel('search_keywords'),
        ], heading="SEO"),
        FieldPanel('featured'),
    ]
    
    settings_panels = Page.settings_panels + [
        MultiFieldPanel([
            FieldPanel('created_at', read_only=True),
            FieldPanel('updated_at', read_only=True),
        ], heading="Timestamps"),
    ]
    
    # Use tabbed interface
    edit_handler = TabbedInterface([
        ObjectList(content_panels, heading='Content'),
        ObjectList(promote_panels, heading='Promote'),
        ObjectList(settings_panels, heading='Settings'),
    ])
    
    class Meta:
        abstract = True


# --- Homepage ---
class HomePage(BasePage):
    """Homepage model with hero section and featured content"""
    
    # Hero section
    hero_title = models.CharField(
        max_length=255,
        default="Welcome to CozyWish",
        help_text="Main hero title"
    )
    
    hero_subtitle = models.TextField(
        default="Find and book cozy venues for your special occasions",
        help_text="Hero subtitle/description"
    )
    
    hero_image = models.ForeignKey(
        'wagtailimages.Image',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='+',
        help_text="Hero background image"
    )
    
    hero_cta_text = models.CharField(
        "Hero CTA Text",
        max_length=50,
        default="Find Venues",
        blank=True
    )
    
    hero_cta_url = models.URLField(
        "Hero CTA URL",
        default="/venues/",
        blank=True
    )
    
    # Content panels
    content_panels = Page.content_panels + [
        MultiFieldPanel([
            FieldPanel('hero_title'),
            FieldPanel('hero_subtitle'),
            FieldPanel('hero_image'),
            FieldPanel('hero_cta_text'),
            FieldPanel('hero_cta_url'),
        ], heading="Hero Section"),
        FieldPanel('intro'),
        FieldPanel('body'),
        InlinePanel('featured_venues', label="Featured Venues"),
    ]
    
    # We can only have one homepage
    parent_page_types = []
    subpage_types = ['cms_app.ContentPage', 'cms_app.VenueListingPage', 'cms_app.BlogIndexPage']


# --- Featured Venues (for homepage) ---
class HomePageFeaturedVenue(Orderable):
    """Featured venues on the homepage"""
    
    page = ParentalKey(HomePage, on_delete=models.CASCADE, related_name='featured_venues')
    venue = models.ForeignKey(
        'venues_app.Venue',
        on_delete=models.CASCADE,
        help_text="Choose a venue to feature"
    )
    description = models.TextField(
        max_length=200,
        blank=True,
        help_text="Optional custom description for this featured venue"
    )
    
    panels = [
        FieldPanel('venue'),
        FieldPanel('description'),
    ]
    
    def __str__(self):
        return f"Featured: {self.venue.name}"


# --- Content Page ---
class ContentPage(BasePage):
    """Standard content page for general information"""
    
    # Allow children
    subpage_types = ['cms_app.ContentPage']
    
    class Meta:
        verbose_name = "Content Page"


# --- Venue Listing Page ---
class VenueListingPage(BasePage):
    """Page that lists venues with filtering and search"""
    
    venues_per_page = models.PositiveIntegerField(
        default=12,
        help_text="Number of venues to show per page"
    )
    
    show_filters = models.BooleanField(
        default=True,
        help_text="Show filtering options on this page"
    )
    
    # Content panels
    content_panels = BasePage.content_panels + [
        MultiFieldPanel([
            FieldPanel('venues_per_page'),
            FieldPanel('show_filters'),
        ], heading="Listing Settings"),
    ]
    
    # Don't allow children
    subpage_types = []
    
    class Meta:
        verbose_name = "Venue Listing Page"


# --- Blog Index Page ---
class BlogIndexPage(BasePage):
    """Blog index page that lists blog posts"""
    
    posts_per_page = models.PositiveIntegerField(
        default=6,
        help_text="Number of blog posts to show per page"
    )
    
    # Content panels
    content_panels = BasePage.content_panels + [
        FieldPanel('posts_per_page'),
    ]
    
    # Allow blog posts as children
    subpage_types = ['cms_app.BlogPage']
    
    class Meta:
        verbose_name = "Blog Index Page"


# --- Blog Page ---
class BlogPage(BasePage):
    """Individual blog post"""
    
    # Author and date
    author = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='blog_posts',
        help_text="Blog post author"
    )
    
    published_date = models.DateTimeField(
        default=timezone.now,
        help_text="When this post was published"
    )
    
    # Blog-specific content
    excerpt = models.TextField(
        max_length=300,
        help_text="Short excerpt for listings (max 300 characters)"
    )
    
    featured_image = models.ForeignKey(
        'wagtailimages.Image',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='+',
        help_text="Featured image for this blog post"
    )
    
    # Tags
    tags = models.ManyToManyField(
        'cms_app.BlogTag',
        blank=True,
        help_text="Tags for this blog post"
    )
    
    # Search
    search_fields = BasePage.search_fields + [
        index.SearchField('excerpt'),
        index.SearchField('author'),
    ]
    
    # Content panels
    content_panels = Page.content_panels + [
        MultiFieldPanel([
            FieldPanel('author'),
            FieldPanel('published_date'),
            FieldPanel('featured_image'),
            FieldPanel('tags'),
        ], heading="Blog Settings"),
        FieldPanel('excerpt'),
        FieldPanel('body'),
    ]
    
    # Parent page types
    parent_page_types = ['cms_app.BlogIndexPage']
    subpage_types = []
    
    class Meta:
        verbose_name = "Blog Post"
        ordering = ['-published_date']


# --- Blog Tag (Snippet) ---
@register_snippet
class BlogTag(models.Model):
    """Tags for blog posts"""
    
    name = models.CharField(max_length=50, unique=True)
    slug = models.SlugField(max_length=50, unique=True)
    description = models.TextField(blank=True, help_text="Optional description for this tag")
    
    panels = [
        FieldPanel('name'),
        FieldPanel('slug'),
        FieldPanel('description'),
    ]
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = "Blog Tag"
        ordering = ['name']


# --- Contact Form Page ---
class ContactFormPage(AbstractForm):
    """Contact form page with form functionality"""
    
    intro = models.TextField(
        blank=True,
        help_text="Introduction text for the contact form"
    )
    
    thank_you_text = RichTextField(
        blank=True,
        help_text="Text to display after form submission"
    )
    
    # Content panels
    content_panels = AbstractForm.content_panels + [
        FieldPanel('intro'),
        InlinePanel('form_fields', label="Form fields"),
        FieldPanel('thank_you_text'),
    ]
    
    # Add form submissions panel
    edit_handler = TabbedInterface([
        ObjectList(content_panels, heading='Content'),
        ObjectList(AbstractForm.promote_panels, heading='Promote'),
        ObjectList(AbstractForm.settings_panels, heading='Settings'),
        FormSubmissionsPanel(),
    ])
    
    # Don't allow children
    subpage_types = []
    
    class Meta:
        verbose_name = "Contact Form Page"


# --- Contact Form Field ---
class ContactFormField(AbstractFormField):
    """Form field for contact form"""
    
    page = ParentalKey(ContactFormPage, on_delete=models.CASCADE, related_name='form_fields')


# --- Site Settings ---
@register_setting
class CMSSettings(BaseGenericSetting):
    """Global CMS settings"""
    
    # Site info
    site_tagline = models.CharField(
        max_length=255,
        default="Find and book cozy venues for your special occasions",
        help_text="Site tagline/description"
    )
    
    # Contact info
    contact_email = models.EmailField(
        default="<EMAIL>",
        help_text="Main contact email address"
    )
    
    contact_phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Main contact phone number"
    )
    
    # Social media
    facebook_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)
    
    # Analytics
    google_analytics_id = models.CharField(
        max_length=20,
        blank=True,
        help_text="Google Analytics tracking ID (e.g. GA-XXXXXXXX-X)"
    )
    
    # Footer
    footer_text = RichTextField(
        blank=True,
        help_text="Footer text/copyright notice"
    )
    
    panels = [
        MultiFieldPanel([
            FieldPanel('site_tagline'),
            FieldPanel('contact_email'),
            FieldPanel('contact_phone'),
        ], heading="Site Information"),
        MultiFieldPanel([
            FieldPanel('facebook_url'),
            FieldPanel('twitter_url'),
            FieldPanel('instagram_url'),
            FieldPanel('linkedin_url'),
        ], heading="Social Media"),
        MultiFieldPanel([
            FieldPanel('google_analytics_id'),
        ], heading="Analytics"),
        FieldPanel('footer_text'),
    ]
    
    class Meta:
        verbose_name = "CMS Settings"
