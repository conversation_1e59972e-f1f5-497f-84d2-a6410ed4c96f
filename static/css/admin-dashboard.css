/* Modern Admin Dashboard Styles */
/* Compatible with Django Unfold Theme */

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background: #fafafa;
    min-height: 100vh;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.dashboard-timestamp {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.metric-header h3 {
    font-size: 1rem;
    font-weight: 500;
    color: #5a6c7d;
    margin: 0;
}

.metric-header i {
    font-size: 1.5rem;
    opacity: 0.6;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
    line-height: 1.2;
}

.metric-change {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.change-positive {
    color: #27ae60;
    font-weight: 600;
}

.change-negative {
    color: #e74c3c;
    font-weight: 600;
}

.change-neutral {
    color: #f39c12;
    font-weight: 600;
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 40px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-container.full-width {
    grid-column: 1 / -1;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: 8px;
}

.chart-controls .btn {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-controls .btn:hover {
    background: #f8f9fa;
    border-color: #007cba;
}

.chart-controls .btn.active {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

.chart-wrapper {
    height: 300px;
    position: relative;
}

.chart {
    width: 100%;
    height: 100%;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 40px;
}

.quick-actions h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.action-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    text-decoration: none;
    color: #2c3e50;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: #007cba;
}

.action-card i {
    font-size: 1.2rem;
    color: #007cba;
}

.action-card span {
    font-weight: 500;
}

/* Recent Activity */
.recent-activity {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.activity-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    transition: background 0.2s ease;
}

.activity-item:hover {
    background: #f8f9fa;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e3f2fd;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-icon i {
    color: #007cba;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 4px;
}

.activity-meta {
    font-size: 0.85rem;
    color: #7f8c8d;
}

.activity-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-completed {
    background: #cce5ff;
    color: #004085;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.activity-rating {
    display: flex;
    gap: 2px;
}

.activity-rating i {
    font-size: 0.8rem;
    color: #ddd;
}

.activity-rating i.filled {
    color: #ffc107;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: 8px;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 12px;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .recent-activity {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .metric-value {
        font-size: 2rem;
    }
    
    .chart-wrapper {
        height: 250px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dashboard-container {
        background: #1a1a1a;
        color: #e0e0e0;
    }
    
    .dashboard-header,
    .metric-card,
    .chart-container,
    .activity-section,
    .action-card {
        background: #2d2d2d;
        color: #e0e0e0;
    }
    
    .dashboard-title,
    .metric-header h3,
    .chart-header h3,
    .activity-section h3,
    .activity-title {
        color: #e0e0e0;
    }
    
    .metric-value {
        color: #ffffff;
    }
    
    .activity-item:hover {
        background: #3a3a3a;
    }
    
    .chart-controls .btn {
        background: #3a3a3a;
        border-color: #555;
        color: #e0e0e0;
    }
    
    .chart-controls .btn:hover {
        background: #4a4a4a;
        border-color: #007cba;
    }
}

/* Print Styles */
@media print {
    .dashboard-container {
        background: white;
        box-shadow: none;
    }
    
    .chart-controls {
        display: none;
    }
    
    .action-card {
        display: none;
    }
    
    .metric-card,
    .chart-container,
    .activity-section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
} 