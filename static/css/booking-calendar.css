/**
 * Modern Booking Calendar Styles
 * Clean, responsive design with smooth animations and intuitive UX
 */

/* Base Calendar Container */
.booking-calendar {
    display: grid;
    grid-template-columns: 1fr 300px;
    grid-template-rows: auto 1fr;
    gap: 1rem;
    height: 100vh;
    min-height: 600px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Calendar Header */
.calendar-header {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.calendar-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.calendar-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.calendar-controls .btn-group .btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    transition: all 0.3s ease;
}

.calendar-controls .btn-group .btn:hover,
.calendar-controls .btn-group .btn.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.calendar-controls .btn-primary {
    background: #ff6b6b;
    border-color: #ff6b6b;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.calendar-controls .btn-primary:hover {
    background: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

/* Calendar Body */
.calendar-body {
    padding: 1.5rem;
    overflow-y: auto;
    background: #f8f9fa;
}

/* Month View */
.calendar-month-view {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.month-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: #f1f3f4;
    border-bottom: 2px solid #e9ecef;
}

.day-header {
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.month-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e9ecef;
}

.calendar-day {
    background: white;
    min-height: 120px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border: 2px solid transparent;
}

.calendar-day:hover {
    background: #f8f9fa;
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-day.today {
    background: #e3f2fd;
    border-color: #2196f3;
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #adb5bd;
}

.calendar-day.selected {
    background: #fff3e0;
    border-color: #ff9800;
}

.day-number {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 4px;
    color: #495057;
}

.day-appointments {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* Week View */
.calendar-week-view {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.week-header {
    display: grid;
    grid-template-columns: 80px repeat(7, 1fr);
    background: #f1f3f4;
    border-bottom: 2px solid #e9ecef;
}

.time-column-header {
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    color: #495057;
    border-right: 1px solid #e9ecef;
}

.week-header .day-header {
    padding: 1rem;
    text-align: center;
    border-right: 1px solid #e9ecef;
}

.week-header .day-header.today {
    background: #e3f2fd;
    color: #1976d2;
}

.day-name {
    font-size: 0.75rem;
    text-transform: uppercase;
    color: #6c757d;
    margin-bottom: 4px;
}

.day-date {
    font-size: 1.2rem;
    font-weight: 600;
}

.week-body {
    max-height: 600px;
    overflow-y: auto;
}

.time-row {
    display: grid;
    grid-template-columns: 80px repeat(7, 1fr);
    border-bottom: 1px solid #e9ecef;
    min-height: 60px;
}

.time-label {
    padding: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    color: #6c757d;
    border-right: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.time-slot {
    border-right: 1px solid #e9ecef;
    padding: 4px;
    position: relative;
    min-height: 60px;
    transition: background-color 0.2s ease;
}

.time-slot:hover {
    background: #f8f9fa;
}

.time-slot.drop-zone {
    background: #e8f5e8;
    border: 2px dashed #4caf50;
}

/* Day View */
.calendar-day-view {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.day-title {
    padding: 1.5rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    background: #f1f3f4;
    border-bottom: 2px solid #e9ecef;
    text-align: center;
}

.day-schedule {
    max-height: 600px;
    overflow-y: auto;
}

.hour-block {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    min-height: 80px;
}

.hour-label {
    width: 80px;
    padding: 1rem;
    text-align: center;
    font-size: 0.875rem;
    color: #6c757d;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hour-content {
    flex: 1;
    padding: 8px;
    position: relative;
    transition: background-color 0.2s ease;
}

.hour-content:hover {
    background: #f8f9fa;
}

/* Appointment Cards */
.appointment-card {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.appointment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.appointment-card.selected {
    box-shadow: 0 0 0 3px #ff9800;
}

.appointment-card.compact {
    padding: 4px 6px;
    font-size: 0.75rem;
}

.appointment-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

/* Appointment Status Colors */
.appointment-card.pending {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
}

.appointment-card.confirmed {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.appointment-card.cancelled {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.appointment-card.completed {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.appointment-time {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.appointment-title {
    font-weight: 600;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.appointment-details {
    font-size: 0.75rem;
    opacity: 0.9;
}

.appointment-customer {
    margin-bottom: 2px;
}

.appointment-duration {
    font-size: 0.7rem;
    opacity: 0.8;
}

.appointment-status {
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 0.625rem;
    padding: 2px 4px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.2);
    text-transform: uppercase;
    font-weight: 600;
}

/* Calendar Sidebar */
.calendar-sidebar {
    background: white;
    border-left: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
    overflow-y: auto;
}

/* Appointment Details */
.appointment-detail-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.appointment-detail-card h4 {
    margin-bottom: 1rem;
    color: #495057;
    font-weight: 600;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-row label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.875rem;
}

.detail-row .status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.detail-row .status.pending {
    background: #fff3cd;
    color: #856404;
}

.detail-row .status.confirmed {
    background: #d4edda;
    color: #155724;
}

.detail-row .status.cancelled {
    background: #f8d7da;
    color: #721c24;
}

.detail-row .status.completed {
    background: #e2e3e5;
    color: #495057;
}

.detail-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.detail-actions .btn {
    flex: 1;
    font-size: 0.875rem;
}

/* Appointment List */
.appointment-list-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.appointment-list-header h5 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.appointment-list-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.appointment-list-item {
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
}

.appointment-list-item:hover {
    background: #e9ecef;
    transform: translateX(4px);
}

.appointment-list-item.selected {
    background: #fff3e0;
    border-color: #ff9800;
}

.appointment-list-item .appointment-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.appointment-list-item .appointment-title {
    font-weight: 600;
    margin-bottom: 2px;
}

.appointment-list-item .appointment-customer {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.appointment-list-item .appointment-status {
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 600;
    text-transform: uppercase;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification.success {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.notification.error {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.notification.info {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.notification.warning {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .booking-calendar {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .calendar-sidebar {
        border-left: none;
        border-top: 1px solid #e9ecef;
        max-height: 300px;
    }
    
    .calendar-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .calendar-navigation {
        order: 2;
    }
    
    .calendar-controls {
        order: 1;
    }
}

@media (max-width: 768px) {
    .calendar-controls {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .calendar-controls .btn-group {
        width: 100%;
    }
    
    .calendar-controls .btn-group .btn {
        flex: 1;
    }
    
    .time-row {
        grid-template-columns: 60px repeat(7, 1fr);
    }
    
    .time-column-header {
        width: 60px;
    }
    
    .time-label {
        font-size: 0.75rem;
    }
    
    .appointment-card {
        font-size: 0.75rem;
        padding: 6px;
    }
    
    .calendar-day {
        min-height: 80px;
        padding: 4px;
    }
}

@media (max-width: 576px) {
    .booking-calendar {
        height: 100vh;
        border-radius: 0;
        margin: 0;
    }
    
    .calendar-header {
        padding: 1rem;
    }
    
    .calendar-body {
        padding: 1rem;
    }
    
    .calendar-sidebar {
        padding: 1rem;
    }
    
    .calendar-title {
        font-size: 1.25rem;
    }
    
    .month-grid {
        gap: 0;
    }
    
    .calendar-day {
        min-height: 60px;
        padding: 2px;
    }
    
    .day-number {
        font-size: 1rem;
    }
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility */
.calendar-day:focus,
.appointment-card:focus,
.appointment-list-item:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .booking-calendar {
        height: auto;
        box-shadow: none;
    }
    
    .calendar-header {
        background: white;
        color: black;
        box-shadow: none;
    }
    
    .calendar-sidebar {
        display: none;
    }
    
    .appointment-card {
        background: white;
        color: black;
        border: 1px solid #ccc;
    }
} 