/**
 * CozyWish Service Worker
 * Provides offline functionality and caching for PWA
 */

const CACHE_NAME = 'cozywish-v1.0.0';
const STATIC_CACHE_NAME = 'cozywish-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'cozywish-dynamic-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
    '/',
    '/static/css/main.css',
    '/static/js/main.js',
    '/static/js/vendor.js',
    '/static/js/alpine-components.js',
    '/static/images/logo.png',
    '/static/images/icons/icon-192x192.png',
    '/static/images/icons/icon-512x512.png',
    '/offline/',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js',
    'https://unpkg.com/alpinejs@3.13.3/dist/cdn.min.js',
    'https://unpkg.com/htmx.org@1.9.10'
];

// Routes to cache dynamically
const DYNAMIC_CACHE_ROUTES = [
    '/venues/',
    '/venues/search/',
    '/api/venues/',
    '/api/search/'
];

// Install event - cache static assets
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE_NAME)
            .then(cache => {
                console.log('Service Worker: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Service Worker: Static assets cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Failed to cache static assets', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== DYNAMIC_CACHE_NAME &&
                            cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    event.respondWith(
        caches.match(request)
            .then(cachedResponse => {
                // Return cached version if available
                if (cachedResponse) {
                    return cachedResponse;
                }
                
                // Otherwise fetch from network
                return fetch(request)
                    .then(response => {
                        // Don't cache non-successful responses
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // Clone the response
                        const responseToCache = response.clone();
                        
                        // Cache dynamic content
                        if (shouldCacheDynamically(request)) {
                            caches.open(DYNAMIC_CACHE_NAME)
                                .then(cache => {
                                    cache.put(request, responseToCache);
                                });
                        }
                        
                        return response;
                    })
                    .catch(() => {
                        // Return offline page for navigation requests
                        if (request.destination === 'document') {
                            return caches.match('/offline/');
                        }
                        
                        // Return placeholder for images
                        if (request.destination === 'image') {
                            return caches.match('/static/images/placeholder.png');
                        }
                        
                        // Return empty response for other requests
                        return new Response('', {
                            status: 408,
                            statusText: 'Request Timeout'
                        });
                    });
            })
    );
});

// Background sync for form submissions
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync triggered', event.tag);
    
    if (event.tag === 'venue-booking') {
        event.waitUntil(syncVenueBookings());
    }
    
    if (event.tag === 'contact-form') {
        event.waitUntil(syncContactForms());
    }
});

// Push notifications
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'New notification from CozyWish',
        icon: '/static/images/icons/icon-192x192.png',
        badge: '/static/images/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View Details',
                icon: '/static/images/icons/view-96x96.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/static/images/icons/close-96x96.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('CozyWish', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/venues/')
        );
    } else if (event.action === 'close') {
        // Just close the notification
        return;
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Helper functions
function shouldCacheDynamically(request) {
    const url = new URL(request.url);
    
    // Cache API responses
    if (url.pathname.startsWith('/api/')) {
        return true;
    }
    
    // Cache venue pages
    if (url.pathname.startsWith('/venues/')) {
        return true;
    }
    
    // Cache search results
    if (url.pathname.includes('/search/')) {
        return true;
    }
    
    // Cache images
    if (request.destination === 'image') {
        return true;
    }
    
    return false;
}

async function syncVenueBookings() {
    try {
        // Get pending bookings from IndexedDB
        const pendingBookings = await getPendingBookings();
        
        for (const booking of pendingBookings) {
            try {
                const response = await fetch('/api/bookings/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': booking.csrfToken
                    },
                    body: JSON.stringify(booking.data)
                });
                
                if (response.ok) {
                    await removePendingBooking(booking.id);
                    console.log('Service Worker: Booking synced successfully');
                }
            } catch (error) {
                console.error('Service Worker: Failed to sync booking', error);
            }
        }
    } catch (error) {
        console.error('Service Worker: Failed to sync venue bookings', error);
    }
}

async function syncContactForms() {
    try {
        // Get pending contact forms from IndexedDB
        const pendingForms = await getPendingContactForms();
        
        for (const form of pendingForms) {
            try {
                const response = await fetch('/api/contact/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': form.csrfToken
                    },
                    body: JSON.stringify(form.data)
                });
                
                if (response.ok) {
                    await removePendingContactForm(form.id);
                    console.log('Service Worker: Contact form synced successfully');
                }
            } catch (error) {
                console.error('Service Worker: Failed to sync contact form', error);
            }
        }
    } catch (error) {
        console.error('Service Worker: Failed to sync contact forms', error);
    }
}

// IndexedDB helpers (simplified - would need full implementation)
async function getPendingBookings() {
    // Implementation would use IndexedDB to get pending bookings
    return [];
}

async function removePendingBooking(id) {
    // Implementation would remove booking from IndexedDB
}

async function getPendingContactForms() {
    // Implementation would use IndexedDB to get pending forms
    return [];
}

async function removePendingContactForm(id) {
    // Implementation would remove form from IndexedDB
}
