/**
 * Modern Image Gallery Components
 * CSS Grid-based galleries with Alpine.js integration
 */

// Gallery Container
.modern-gallery {
    position: relative;
    
    &__grid {
        display: grid;
        gap: 1rem;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        
        @media (min-width: 768px) {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }
        
        @media (min-width: 1200px) {
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        }
    }
    
    &__item {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
        background: #f8f9fa;
        aspect-ratio: 4/3;
        cursor: pointer;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        
        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        &:hover .modern-gallery__overlay {
            opacity: 1;
        }
    }
    
    &__image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
        
        &.lazy {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        &.loaded {
            opacity: 1;
        }
        
        &:hover {
            transform: scale(1.05);
        }
    }
    
    &__overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.7) 100%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: flex-end;
        padding: 1rem;
    }
    
    &__caption {
        color: white;
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    &__loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
        background: #f8f9fa;
        border-radius: 8px;
        
        .spinner-border {
            color: var(--bs-primary);
        }
    }
}

// Masonry Layout Variant
.modern-gallery--masonry {
    .modern-gallery__grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        grid-auto-rows: 10px;
    }
    
    .modern-gallery__item {
        aspect-ratio: unset;
        
        &:nth-child(3n+1) {
            grid-row-end: span 25;
        }
        
        &:nth-child(3n+2) {
            grid-row-end: span 30;
        }
        
        &:nth-child(3n+3) {
            grid-row-end: span 35;
        }
    }
}

// Lightbox Modal
.gallery-lightbox {
    .modal-dialog {
        max-width: 90vw;
        max-height: 90vh;
        margin: 5vh auto;
    }
    
    .modal-content {
        background: transparent;
        border: none;
    }
    
    .modal-body {
        padding: 0;
        position: relative;
    }
    
    &__image {
        width: 100%;
        height: auto;
        max-height: 80vh;
        object-fit: contain;
        border-radius: 8px;
    }
    
    &__nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.5);
        border: none;
        color: white;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.3s ease;
        
        &:hover {
            background: rgba(0, 0, 0, 0.7);
            color: white;
        }
        
        &--prev {
            left: 20px;
        }
        
        &--next {
            right: 20px;
        }
    }
    
    &__close {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.5);
        border: none;
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:hover {
            background: rgba(0, 0, 0, 0.7);
            color: white;
        }
    }
    
    &__caption {
        position: absolute;
        bottom: 20px;
        left: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
    }
    
    &__counter {
        position: absolute;
        top: 20px;
        left: 20px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
    }
}

// Thumbnail Navigation
.gallery-thumbnails {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    overflow-x: auto;
    padding: 0.5rem 0;
    
    &__item {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
        border: 2px solid transparent;
        transition: border-color 0.3s ease;
        
        &--active {
            border-color: var(--bs-primary);
        }
        
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}

// Loading States
.gallery-skeleton {
    .modern-gallery__grid {
        display: grid;
        gap: 1rem;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .skeleton-item {
        aspect-ratio: 4/3;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 8px;
    }
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

// Responsive Design
@media (max-width: 768px) {
    .modern-gallery {
        &__grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
        }
    }
    
    .gallery-lightbox {
        &__nav {
            width: 40px;
            height: 40px;
            
            &--prev {
                left: 10px;
            }
            
            &--next {
                right: 10px;
            }
        }
        
        &__close {
            top: 10px;
            right: 10px;
            width: 35px;
            height: 35px;
        }
        
        &__caption {
            bottom: 10px;
            left: 10px;
            right: 10px;
            padding: 0.75rem;
        }
        
        &__counter {
            top: 10px;
            left: 10px;
            padding: 0.25rem 0.75rem;
        }
    }
}
