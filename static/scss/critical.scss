/**
 * Critical CSS for above-the-fold content
 * This CSS should be inlined in the head for fastest rendering
 */

/* Reset and base styles */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    margin: 0;
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Critical layout styles */
.container {
    width: 100%;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
    margin-right: auto;
    margin-left: auto;
}

@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
        padding-right: 1rem;
        padding-left: 1rem;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
}

@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
}

/* Critical navigation styles */
.navbar {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    text-decoration: none;
    white-space: nowrap;
    color: #42241A;
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
}

.navbar-nav {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

@media (min-width: 992px) {
    .navbar-nav {
        flex-direction: row;
    }
}

.nav-link {
    display: block;
    padding: 0.5rem 1rem;
    color: #525252;
    text-decoration: none;
    transition: color 0.15s ease-in-out;
}

.nav-link:hover {
    color: #42241A;
}

/* Critical button styles */
.btn {
    display: inline-block;
    font-weight: 500;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #42241A;
    border-color: #42241A;
}

.btn-primary:hover {
    color: #fff;
    background-color: #2F160F;
    border-color: #2F160F;
}

/* Critical hero section styles */
.hero-section {
    background: linear-gradient(135deg, #FFF9F4 0%, #F5E6D3 100%);
    padding: 4rem 0;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.hero-content {
    text-align: center;
}

.hero-title {
    font-family: 'Poppins', sans-serif;
    font-size: 3rem;
    font-weight: 700;
    color: #2F160F;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #525252;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

@media (max-width: 768px) {
    .hero-subtitle {
        font-size: 1.1rem;
    }
}

/* Critical loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Critical image styles */
img {
    max-width: 100%;
    height: auto;
    vertical-align: middle;
}

.lazy-loading {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-loaded {
    opacity: 1;
}

.lazy-error {
    opacity: 0.5;
    filter: grayscale(100%);
}

/* Critical utility classes */
.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

.justify-content-center {
    justify-content: center !important;
}

.align-items-center {
    align-items: center !important;
}

.text-center {
    text-align: center !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: 0.25rem !important;
}

.mb-2 {
    margin-bottom: 0.5rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mb-5 {
    margin-bottom: 3rem !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mt-1 {
    margin-top: 0.25rem !important;
}

.mt-2 {
    margin-top: 0.5rem !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

.mt-4 {
    margin-top: 1.5rem !important;
}

.mt-5 {
    margin-top: 3rem !important;
}

/* Critical responsive utilities */
@media (max-width: 575.98px) {
    .d-sm-none {
        display: none !important;
    }
    .d-sm-block {
        display: block !important;
    }
}

@media (min-width: 576px) {
    .d-sm-block {
        display: block !important;
    }
    .d-sm-none {
        display: none !important;
    }
}

@media (max-width: 767.98px) {
    .d-md-none {
        display: none !important;
    }
}

@media (min-width: 768px) {
    .d-md-block {
        display: block !important;
    }
    .d-md-none {
        display: none !important;
    }
}

/* Critical performance optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}
