/**
 * CozyWish Messages - Modern Alpine.js Integration
 * Handles Bootstrap toasts and tooltips with Alpine.js compatibility
 */

// Initialize Bootstrap components
function initializeBootstrapComponents() {
    // Initialize toasts
    const toastElements = document.querySelectorAll('.toast');
    toastElements.forEach(function(el) {
        const toast = new bootstrap.Toast(el, {
            autohide: true,
            delay: 5000
        });
        toast.show();
    });

    // Initialize tooltips
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltipTriggerList.forEach(function(el) {
        new bootstrap.Tooltip(el);
    });
}

// Alpine.js toast component
function toastManager() {
    return {
        toasts: [],

        show(message, type = 'info', duration = 5000) {
            const id = Date.now();
            const toast = {
                id,
                message,
                type,
                duration,
                visible: true
            };

            this.toasts.push(toast);

            if (duration > 0) {
                setTimeout(() => {
                    this.hide(id);
                }, duration);
            }
        },

        hide(id) {
            const index = this.toasts.findIndex(t => t.id === id);
            if (index > -1) {
                this.toasts[index].visible = false;
                setTimeout(() => {
                    this.toasts.splice(index, 1);
                }, 300); // Allow for fade animation
            }
        },

        getTypeClass(type) {
            const typeMap = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            };
            return typeMap[type] || 'alert-info';
        }
    }
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeBootstrapComponents);

// Re-initialize after HTMX swaps
document.addEventListener('htmx:afterSwap', initializeBootstrapComponents);

// Export for global use
window.toastManager = toastManager;
