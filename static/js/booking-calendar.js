/**
 * Modern Booking Calendar Component
 * Interactive calendar with drag-and-drop scheduling, booking management, and real-time updates
 */

class BookingCalendar {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            apiEndpoint: '/api/booking-calendar/',
            weekStartsOn: 0, // Sunday = 0, Monday = 1
            timeSlotDuration: 30, // minutes
            workingHours: { start: 9, end: 17 }, // 9 AM to 5 PM
            enableDragDrop: true,
            enableModifications: true,
            locale: 'en-US',
            ...options
        };

        this.currentDate = new Date();
        this.currentView = 'month'; // month, week, day
        this.appointments = [];
        this.selectedAppointment = null;
        this.draggedAppointment = null;

        this.init();
    }

    init() {
        this.createCalendarStructure();
        this.bindEvents();
        this.loadAppointments();
        this.render();
    }

    createCalendarStructure() {
        this.container.innerHTML = `
            <div class="booking-calendar">
                <div class="calendar-header">
                    <div class="calendar-navigation">
                        <button class="btn btn-sm btn-outline-primary" id="prevPeriod">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <h2 class="calendar-title" id="calendarTitle"></h2>
                        <button class="btn btn-sm btn-outline-primary" id="nextPeriod">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="calendar-controls">
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-secondary" data-view="month">Month</button>
                            <button class="btn btn-sm btn-outline-secondary" data-view="week">Week</button>
                            <button class="btn btn-sm btn-outline-secondary" data-view="day">Day</button>
                        </div>
                        <button class="btn btn-sm btn-primary" id="addAppointment">
                            <i class="fas fa-plus"></i> Add Appointment
                        </button>
                    </div>
                </div>
                <div class="calendar-body" id="calendarBody"></div>
                <div class="calendar-sidebar" id="calendarSidebar">
                    <div class="appointment-details" id="appointmentDetails"></div>
                    <div class="appointment-list" id="appointmentList"></div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Navigation
        document.getElementById('prevPeriod').addEventListener('click', () => this.navigatePrevious());
        document.getElementById('nextPeriod').addEventListener('click', () => this.navigateNext());

        // View switching
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchView(e.target.dataset.view));
        });

        // Add appointment
        document.getElementById('addAppointment').addEventListener('click', () => this.showAppointmentModal());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    async loadAppointments() {
        try {
            const response = await fetch(`${this.options.apiEndpoint}appointments/`, {
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                this.appointments = await response.json();
                this.render();
            }
        } catch (error) {
            console.error('Error loading appointments:', error);
            this.showNotification('Error loading appointments', 'error');
        }
    }

    render() {
        this.updateTitle();
        this.renderCalendarView();
        this.renderAppointmentList();
        this.updateViewButtons();
    }

    updateTitle() {
        const titleElement = document.getElementById('calendarTitle');
        const options = { year: 'numeric', month: 'long' };
        
        switch (this.currentView) {
            case 'month':
                titleElement.textContent = this.currentDate.toLocaleDateString(this.options.locale, options);
                break;
            case 'week':
                const weekStart = this.getWeekStart(this.currentDate);
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekEnd.getDate() + 6);
                titleElement.textContent = `${weekStart.toLocaleDateString(this.options.locale, { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString(this.options.locale, { month: 'short', day: 'numeric' })}`;
                break;
            case 'day':
                titleElement.textContent = this.currentDate.toLocaleDateString(this.options.locale, { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
                break;
        }
    }

    renderCalendarView() {
        const calendarBody = document.getElementById('calendarBody');
        
        switch (this.currentView) {
            case 'month':
                this.renderMonthView(calendarBody);
                break;
            case 'week':
                this.renderWeekView(calendarBody);
                break;
            case 'day':
                this.renderDayView(calendarBody);
                break;
        }
    }

    renderMonthView(container) {
        const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
        const monthEnd = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);
        const calendarStart = this.getWeekStart(monthStart);
        const calendarEnd = new Date(monthEnd);
        calendarEnd.setDate(calendarEnd.getDate() + (6 - calendarEnd.getDay()));

        let html = '<div class="calendar-month-view">';
        
        // Days of week header
        html += '<div class="month-header">';
        const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        daysOfWeek.forEach(day => {
            html += `<div class="day-header">${day}</div>`;
        });
        html += '</div>';

        // Calendar grid
        html += '<div class="month-grid">';
        let currentDay = new Date(calendarStart);
        
        while (currentDay <= calendarEnd) {
            const isCurrentMonth = currentDay.getMonth() === this.currentDate.getMonth();
            const isToday = this.isToday(currentDay);
            const dayAppointments = this.getAppointmentsForDate(currentDay);
            
            html += `
                <div class="calendar-day ${isCurrentMonth ? 'current-month' : 'other-month'} ${isToday ? 'today' : ''}" 
                     data-date="${currentDay.toISOString().split('T')[0]}">
                    <div class="day-number">${currentDay.getDate()}</div>
                    <div class="day-appointments">
                        ${dayAppointments.map(apt => this.renderAppointmentCard(apt, 'compact')).join('')}
                    </div>
                </div>
            `;
            
            currentDay.setDate(currentDay.getDate() + 1);
        }
        
        html += '</div></div>';
        container.innerHTML = html;
        
        this.bindDayClickEvents();
        this.bindAppointmentEvents();
    }

    renderWeekView(container) {
        const weekStart = this.getWeekStart(this.currentDate);
        const days = [];
        
        for (let i = 0; i < 7; i++) {
            const day = new Date(weekStart);
            day.setDate(day.getDate() + i);
            days.push(day);
        }

        let html = '<div class="calendar-week-view">';
        
        // Header with day names and dates
        html += '<div class="week-header">';
        html += '<div class="time-column-header">Time</div>';
        days.forEach(day => {
            const isToday = this.isToday(day);
            html += `
                <div class="day-header ${isToday ? 'today' : ''}">
                    <div class="day-name">${day.toLocaleDateString(this.options.locale, { weekday: 'short' })}</div>
                    <div class="day-date">${day.getDate()}</div>
                </div>
            `;
        });
        html += '</div>';

        // Time slots
        html += '<div class="week-body">';
        for (let hour = this.options.workingHours.start; hour < this.options.workingHours.end; hour++) {
            html += `
                <div class="time-row">
                    <div class="time-label">${this.formatTime(hour)}:00</div>
                    ${days.map(day => `
                        <div class="time-slot" data-date="${day.toISOString().split('T')[0]}" data-hour="${hour}">
                            ${this.renderTimeSlotAppointments(day, hour)}
                        </div>
                    `).join('')}
                </div>
            `;
        }
        html += '</div></div>';
        
        container.innerHTML = html;
        this.bindTimeSlotEvents();
        this.bindAppointmentEvents();
    }

    renderDayView(container) {
        const dayAppointments = this.getAppointmentsForDate(this.currentDate);
        
        let html = '<div class="calendar-day-view">';
        html += `<div class="day-title">${this.currentDate.toLocaleDateString(this.options.locale, { 
            weekday: 'long', 
            month: 'long', 
            day: 'numeric' 
        })}</div>`;
        
        html += '<div class="day-schedule">';
        for (let hour = this.options.workingHours.start; hour < this.options.workingHours.end; hour++) {
            html += `
                <div class="hour-block">
                    <div class="hour-label">${this.formatTime(hour)}:00</div>
                    <div class="hour-content" data-hour="${hour}">
                        ${this.renderTimeSlotAppointments(this.currentDate, hour)}
                    </div>
                </div>
            `;
        }
        html += '</div></div>';
        
        container.innerHTML = html;
        this.bindTimeSlotEvents();
        this.bindAppointmentEvents();
    }

    renderAppointmentCard(appointment, size = 'full') {
        const startTime = new Date(appointment.start_time);
        const endTime = new Date(appointment.end_time);
        const duration = this.formatDuration(startTime, endTime);
        
        return `
            <div class="appointment-card ${size} ${appointment.status}" 
                 data-id="${appointment.id}" 
                 draggable="${this.options.enableDragDrop}">
                <div class="appointment-time">
                    ${this.formatTime(startTime.getHours())}:${String(startTime.getMinutes()).padStart(2, '0')}
                </div>
                <div class="appointment-title">${appointment.title}</div>
                ${size === 'full' ? `
                    <div class="appointment-details">
                        <div class="appointment-customer">${appointment.customer_name}</div>
                        <div class="appointment-duration">${duration}</div>
                    </div>
                ` : ''}
                <div class="appointment-status ${appointment.status}">${appointment.status}</div>
            </div>
        `;
    }

    renderTimeSlotAppointments(date, hour) {
        const dateStr = date.toISOString().split('T')[0];
        const appointments = this.appointments.filter(apt => {
            const aptDate = new Date(apt.start_time);
            return aptDate.toISOString().split('T')[0] === dateStr && 
                   aptDate.getHours() === hour;
        });
        
        return appointments.map(apt => this.renderAppointmentCard(apt)).join('');
    }

    bindDayClickEvents() {
        document.querySelectorAll('.calendar-day').forEach(day => {
            day.addEventListener('click', (e) => {
                if (e.target.classList.contains('calendar-day')) {
                    const date = new Date(e.target.dataset.date);
                    this.selectDate(date);
                }
            });
        });
    }

    bindTimeSlotEvents() {
        document.querySelectorAll('.time-slot, .hour-content').forEach(slot => {
            slot.addEventListener('click', (e) => {
                if (e.target.classList.contains('time-slot') || e.target.classList.contains('hour-content')) {
                    const date = e.target.dataset.date ? new Date(e.target.dataset.date) : this.currentDate;
                    const hour = parseInt(e.target.dataset.hour);
                    this.selectTimeSlot(date, hour);
                }
            });
        });
    }

    bindAppointmentEvents() {
        document.querySelectorAll('.appointment-card').forEach(card => {
            card.addEventListener('click', (e) => {
                e.stopPropagation();
                const appointmentId = card.dataset.id;
                this.selectAppointment(appointmentId);
            });

            if (this.options.enableDragDrop) {
                card.addEventListener('dragstart', (e) => {
                    this.draggedAppointment = card.dataset.id;
                    e.dataTransfer.effectAllowed = 'move';
                });

                card.addEventListener('dragend', () => {
                    this.draggedAppointment = null;
                });
            }
        });

        // Drop zones
        document.querySelectorAll('.time-slot, .hour-content, .calendar-day').forEach(dropZone => {
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                if (this.draggedAppointment) {
                    const date = e.target.dataset.date ? new Date(e.target.dataset.date) : this.currentDate;
                    const hour = e.target.dataset.hour ? parseInt(e.target.dataset.hour) : null;
                    this.moveAppointment(this.draggedAppointment, date, hour);
                }
            });
        });
    }

    // Navigation methods
    navigatePrevious() {
        switch (this.currentView) {
            case 'month':
                this.currentDate.setMonth(this.currentDate.getMonth() - 1);
                break;
            case 'week':
                this.currentDate.setDate(this.currentDate.getDate() - 7);
                break;
            case 'day':
                this.currentDate.setDate(this.currentDate.getDate() - 1);
                break;
        }
        this.render();
    }

    navigateNext() {
        switch (this.currentView) {
            case 'month':
                this.currentDate.setMonth(this.currentDate.getMonth() + 1);
                break;
            case 'week':
                this.currentDate.setDate(this.currentDate.getDate() + 7);
                break;
            case 'day':
                this.currentDate.setDate(this.currentDate.getDate() + 1);
                break;
        }
        this.render();
    }

    switchView(view) {
        this.currentView = view;
        this.render();
    }

    updateViewButtons() {
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.view === this.currentView) {
                btn.classList.add('active');
            }
        });
    }

    // Appointment management
    async selectAppointment(appointmentId) {
        const appointment = this.appointments.find(apt => apt.id === appointmentId);
        if (appointment) {
            this.selectedAppointment = appointment;
            this.showAppointmentDetails(appointment);
            this.highlightAppointment(appointmentId);
        }
    }

    showAppointmentDetails(appointment) {
        const detailsContainer = document.getElementById('appointmentDetails');
        detailsContainer.innerHTML = `
            <div class="appointment-detail-card">
                <h4>${appointment.title}</h4>
                <div class="detail-row">
                    <label>Customer:</label>
                    <span>${appointment.customer_name}</span>
                </div>
                <div class="detail-row">
                    <label>Date:</label>
                    <span>${new Date(appointment.start_time).toLocaleDateString()}</span>
                </div>
                <div class="detail-row">
                    <label>Time:</label>
                    <span>${this.formatTime(new Date(appointment.start_time).getHours())}:${String(new Date(appointment.start_time).getMinutes()).padStart(2, '0')}</span>
                </div>
                <div class="detail-row">
                    <label>Status:</label>
                    <span class="status ${appointment.status}">${appointment.status}</span>
                </div>
                <div class="detail-actions">
                    <button class="btn btn-sm btn-primary" onclick="bookingCalendar.editAppointment('${appointment.id}')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-success" onclick="bookingCalendar.confirmAppointment('${appointment.id}')">
                        <i class="fas fa-check"></i> Confirm
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="bookingCalendar.cancelAppointment('${appointment.id}')">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                </div>
            </div>
        `;
    }

    highlightAppointment(appointmentId) {
        // Remove existing highlights
        document.querySelectorAll('.appointment-card.selected').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Add highlight to selected appointment
        document.querySelectorAll(`[data-id="${appointmentId}"]`).forEach(card => {
            card.classList.add('selected');
        });
    }

    async moveAppointment(appointmentId, newDate, newHour) {
        const appointment = this.appointments.find(apt => apt.id === appointmentId);
        if (!appointment) return;

        const newStartTime = new Date(newDate);
        if (newHour !== null) {
            newStartTime.setHours(newHour, 0, 0, 0);
        }

        try {
            const response = await fetch(`${this.options.apiEndpoint}appointments/${appointmentId}/move/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    start_time: newStartTime.toISOString(),
                    end_time: new Date(newStartTime.getTime() + (new Date(appointment.end_time) - new Date(appointment.start_time))).toISOString()
                })
            });

            if (response.ok) {
                this.loadAppointments();
                this.showNotification('Appointment moved successfully', 'success');
            } else {
                this.showNotification('Error moving appointment', 'error');
            }
        } catch (error) {
            console.error('Error moving appointment:', error);
            this.showNotification('Error moving appointment', 'error');
        }
    }

    // Utility methods
    getWeekStart(date) {
        const start = new Date(date);
        const day = start.getDay();
        const diff = start.getDate() - day + (day === 0 ? -6 : 1);
        return new Date(start.setDate(diff));
    }

    isToday(date) {
        const today = new Date();
        return date.toDateString() === today.toDateString();
    }

    getAppointmentsForDate(date) {
        const dateStr = date.toISOString().split('T')[0];
        return this.appointments.filter(apt => {
            const aptDate = new Date(apt.start_time);
            return aptDate.toISOString().split('T')[0] === dateStr;
        });
    }

    formatTime(hour) {
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour} ${ampm}`;
    }

    formatDuration(startTime, endTime) {
        const diffMs = endTime - startTime;
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (diffHours > 0) {
            return `${diffHours}h ${diffMinutes}m`;
        } else {
            return `${diffMinutes}m`;
        }
    }

    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Add to DOM
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    handleKeyboard(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    this.navigatePrevious();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.navigateNext();
                    break;
                case '1':
                    e.preventDefault();
                    this.switchView('month');
                    break;
                case '2':
                    e.preventDefault();
                    this.switchView('week');
                    break;
                case '3':
                    e.preventDefault();
                    this.switchView('day');
                    break;
            }
        }
    }

    // Public API methods
    async confirmAppointment(appointmentId) {
        try {
            const response = await fetch(`${this.options.apiEndpoint}appointments/${appointmentId}/confirm/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                this.loadAppointments();
                this.showNotification('Appointment confirmed', 'success');
            }
        } catch (error) {
            console.error('Error confirming appointment:', error);
            this.showNotification('Error confirming appointment', 'error');
        }
    }

    async cancelAppointment(appointmentId) {
        if (confirm('Are you sure you want to cancel this appointment?')) {
            try {
                const response = await fetch(`${this.options.apiEndpoint}appointments/${appointmentId}/cancel/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': this.getCSRFToken(),
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    this.loadAppointments();
                    this.showNotification('Appointment cancelled', 'success');
                }
            } catch (error) {
                console.error('Error cancelling appointment:', error);
                this.showNotification('Error cancelling appointment', 'error');
            }
        }
    }

    editAppointment(appointmentId) {
        // This would open a modal or navigate to edit page
        console.log('Edit appointment:', appointmentId);
    }

    showAppointmentModal(date = null, hour = null) {
        // This would show a modal for creating new appointments
        console.log('Show appointment modal for:', date, hour);
    }

    renderAppointmentList() {
        const listContainer = document.getElementById('appointmentList');
        const today = new Date();
        const upcomingAppointments = this.appointments
            .filter(apt => new Date(apt.start_time) >= today)
            .sort((a, b) => new Date(a.start_time) - new Date(b.start_time))
            .slice(0, 10);

        listContainer.innerHTML = `
            <div class="appointment-list-header">
                <h5>Upcoming Appointments</h5>
            </div>
            <div class="appointment-list-items">
                ${upcomingAppointments.map(apt => `
                    <div class="appointment-list-item" data-id="${apt.id}">
                        <div class="appointment-time">
                            ${new Date(apt.start_time).toLocaleDateString()}
                            ${this.formatTime(new Date(apt.start_time).getHours())}:${String(new Date(apt.start_time).getMinutes()).padStart(2, '0')}
                        </div>
                        <div class="appointment-title">${apt.title}</div>
                        <div class="appointment-customer">${apt.customer_name}</div>
                        <div class="appointment-status ${apt.status}">${apt.status}</div>
                    </div>
                `).join('')}
            </div>
        `;

        // Bind click events for list items
        document.querySelectorAll('.appointment-list-item').forEach(item => {
            item.addEventListener('click', () => {
                this.selectAppointment(item.dataset.id);
            });
        });
    }
}

// Initialize calendar when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('bookingCalendar')) {
        window.bookingCalendar = new BookingCalendar('bookingCalendar', {
            apiEndpoint: '/api/booking-calendar/'
        });
    }
}); 