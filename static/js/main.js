/**
 * CozyWish Main JavaScript Entry Point
 * Modern ES6+ JavaScript with Alpine.js and HTMX integration
 */

// Import Bootstrap JavaScript
import 'bootstrap';

// Import Alpine.js
import Alpine from 'alpinejs';

// Import HTMX
import 'htmx.org';

// Import custom modules
import './modules/messages';
import './modules/forms';
import './modules/notifications';
import './modules/search';
import './modules/galleries';
import './modules/performance';

// Import Alpine.js components
import './alpine-components';

// Initialize Alpine.js
window.Alpine = Alpine;
Alpine.start();

// Global application initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎉 CozyWish application initialized with modern UI framework');
    
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    
    // Initialize Bootstrap popovers
    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]');
    const popoverList = [...popoverTriggerList].map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl));
    
    // HTMX configuration
    if (typeof htmx !== 'undefined') {
        // Configure HTMX
        htmx.config.getCacheBusterParam = true;
        htmx.config.defaultSwapStyle = 'outerHTML';
        htmx.config.defaultSwapDelay = 0;
        htmx.config.defaultSettleDelay = 20;
        
        // Add CSRF token to all HTMX requests
        document.body.addEventListener('htmx:configRequest', function(evt) {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
            if (csrfToken) {
                evt.detail.headers['X-CSRFToken'] = csrfToken;
            }
        });
        
        // Global loading indicators
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            const target = evt.target;
            if (target.hasAttribute('hx-indicator')) {
                const indicator = document.querySelector(target.getAttribute('hx-indicator'));
                if (indicator) indicator.style.display = 'block';
            }
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const target = evt.target;
            if (target.hasAttribute('hx-indicator')) {
                const indicator = document.querySelector(target.getAttribute('hx-indicator'));
                if (indicator) indicator.style.display = 'none';
            }
        });
        
        // Re-initialize Alpine.js components after HTMX swaps
        document.body.addEventListener('htmx:afterSwap', function(evt) {
            Alpine.initTree(evt.detail.target);
        });
    }
    
    // Performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log(`⚡ Page load time: ${Math.round(perfData.loadEventEnd - perfData.fetchStart)}ms`);
            }, 0);
        });
    }
    
    // Enhanced Service Worker registration for PWA
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('/static/sw.js')
                .then(function(registration) {
                    console.log('🔧 Service Worker registered:', registration.scope);

                    // Check for updates
                    registration.addEventListener('updatefound', () => {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                showUpdateNotification();
                            }
                        });
                    });
                })
                .catch(function(error) {
                    console.log('❌ Service Worker registration failed:', error);
                });
        });
    }

    // PWA Install Prompt
    let deferredPrompt;
    let installButton;

    window.addEventListener('beforeinstallprompt', (e) => {
        console.log('PWA install prompt triggered');

        // Prevent the mini-infobar from appearing on mobile
        e.preventDefault();

        // Stash the event so it can be triggered later
        deferredPrompt = e;

        // Show install button
        showInstallButton();
    });

    window.addEventListener('appinstalled', (evt) => {
        console.log('PWA was installed');
        hideInstallButton();
    });

    function showInstallButton() {
        // Create install button if it doesn't exist
        if (!installButton) {
            installButton = document.createElement('button');
            installButton.className = 'btn btn-primary pwa-install-btn';
            installButton.innerHTML = '<i class="fas fa-download me-2"></i>Install App';
            installButton.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1050;
                border-radius: 50px;
                padding: 12px 24px;
                box-shadow: 0 4px 12px rgba(66, 36, 26, 0.3);
                background: #42241A;
                border: none;
                color: white;
                font-weight: 500;
                transition: all 0.3s ease;
            `;

            installButton.addEventListener('click', installPWA);
            document.body.appendChild(installButton);
        }

        installButton.style.display = 'block';
    }

    function hideInstallButton() {
        if (installButton) {
            installButton.style.display = 'none';
        }
    }

    async function installPWA() {
        if (!deferredPrompt) {
            return;
        }

        // Show the install prompt
        deferredPrompt.prompt();

        // Wait for the user to respond to the prompt
        const { outcome } = await deferredPrompt.userChoice;

        console.log(`User response to the install prompt: ${outcome}`);

        // Clear the deferredPrompt
        deferredPrompt = null;
        hideInstallButton();
    }

    function showUpdateNotification() {
        // Create update notification
        const notification = document.createElement('div');
        notification.className = 'alert alert-info alert-dismissible fade show pwa-update-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1060;
            max-width: 350px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;

        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-sync-alt me-2"></i>
                <div class="flex-grow-1">
                    <strong>Update Available</strong><br>
                    <small>A new version is ready.</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="updatePWA()">
                    Update
                </button>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.appendChild(notification);
    }

    function updatePWA() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistration().then(registration => {
                if (registration && registration.waiting) {
                    registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                    window.location.reload();
                }
            });
        }
    }

    // PWA Status Detection
    function checkPWAStatus() {
        const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        const isInStandaloneMode = ('standalone' in window.navigator) && (window.navigator.standalone);

        if (isStandalone || (isIOS && isInStandaloneMode)) {
            document.body.classList.add('pwa-mode');
            console.log('Running as PWA');
        }
    }

    // Initialize PWA features
    checkPWAStatus();

    // Expose functions globally
    window.installPWA = installPWA;
    window.updatePWA = updatePWA;
});

// Global error handling
window.addEventListener('error', function(event) {
    console.error('🚨 Global error:', event.error);
});

window.addEventListener('unhandledrejection', function(event) {
    console.error('🚨 Unhandled promise rejection:', event.reason);
});

// Export for global access
window.CozyWish = {
    version: '1.0.0',
    initialized: true
};
