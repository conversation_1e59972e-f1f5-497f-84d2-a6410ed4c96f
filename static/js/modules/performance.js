/**
 * CozyWish Performance Optimization Module
 * Handles lazy loading, critical resource loading, and performance monitoring
 */

// Lazy Loading Implementation
class LazyLoader {
    constructor(options = {}) {
        this.options = {
            rootMargin: '50px',
            threshold: 0.1,
            loadingClass: 'lazy-loading',
            loadedClass: 'lazy-loaded',
            errorClass: 'lazy-error',
            ...options
        };
        
        this.observer = null;
        this.init();
    }
    
    init() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(
                this.handleIntersection.bind(this),
                {
                    rootMargin: this.options.rootMargin,
                    threshold: this.options.threshold
                }
            );
            
            this.observeElements();
        } else {
            // Fallback for older browsers
            this.loadAllImages();
        }
    }
    
    observeElements() {
        // Observe images with data-src
        document.querySelectorAll('img[data-src]').forEach(img => {
            this.observer.observe(img);
        });
        
        // Observe background images with data-bg
        document.querySelectorAll('[data-bg]').forEach(element => {
            this.observer.observe(element);
        });
        
        // Observe iframes with data-src
        document.querySelectorAll('iframe[data-src]').forEach(iframe => {
            this.observer.observe(iframe);
        });
    }
    
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.loadElement(entry.target);
                this.observer.unobserve(entry.target);
            }
        });
    }
    
    loadElement(element) {
        element.classList.add(this.options.loadingClass);
        
        if (element.tagName === 'IMG') {
            this.loadImage(element);
        } else if (element.tagName === 'IFRAME') {
            this.loadIframe(element);
        } else if (element.dataset.bg) {
            this.loadBackgroundImage(element);
        }
    }
    
    loadImage(img) {
        const src = img.dataset.src;
        const srcset = img.dataset.srcset;
        
        if (src) {
            img.onload = () => {
                img.classList.remove(this.options.loadingClass);
                img.classList.add(this.options.loadedClass);
                img.removeAttribute('data-src');
                if (srcset) img.removeAttribute('data-srcset');
            };
            
            img.onerror = () => {
                img.classList.remove(this.options.loadingClass);
                img.classList.add(this.options.errorClass);
                console.warn('Failed to load image:', src);
            };
            
            if (srcset) {
                img.srcset = srcset;
            }
            img.src = src;
        }
    }
    
    loadIframe(iframe) {
        const src = iframe.dataset.src;
        
        if (src) {
            iframe.onload = () => {
                iframe.classList.remove(this.options.loadingClass);
                iframe.classList.add(this.options.loadedClass);
                iframe.removeAttribute('data-src');
            };
            
            iframe.src = src;
        }
    }
    
    loadBackgroundImage(element) {
        const bg = element.dataset.bg;
        
        if (bg) {
            const img = new Image();
            img.onload = () => {
                element.style.backgroundImage = `url(${bg})`;
                element.classList.remove(this.options.loadingClass);
                element.classList.add(this.options.loadedClass);
                element.removeAttribute('data-bg');
            };
            
            img.onerror = () => {
                element.classList.remove(this.options.loadingClass);
                element.classList.add(this.options.errorClass);
                console.warn('Failed to load background image:', bg);
            };
            
            img.src = bg;
        }
    }
    
    loadAllImages() {
        // Fallback for browsers without IntersectionObserver
        document.querySelectorAll('img[data-src]').forEach(img => {
            this.loadImage(img);
        });
        
        document.querySelectorAll('[data-bg]').forEach(element => {
            this.loadBackgroundImage(element);
        });
        
        document.querySelectorAll('iframe[data-src]').forEach(iframe => {
            this.loadIframe(iframe);
        });
    }
    
    // Method to observe new elements added dynamically
    observeNewElements(container = document) {
        if (this.observer) {
            container.querySelectorAll('img[data-src], [data-bg], iframe[data-src]').forEach(element => {
                this.observer.observe(element);
            });
        }
    }
}

// Critical Resource Preloader
class ResourcePreloader {
    constructor() {
        this.preloadedResources = new Set();
        this.init();
    }
    
    init() {
        this.preloadCriticalResources();
        this.setupPreloadOnHover();
    }
    
    preloadCriticalResources() {
        // Preload critical fonts
        this.preloadFont('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        this.preloadFont('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap');
        
        // Preload critical images
        const criticalImages = [
            '/static/images/logo.png',
            '/static/images/hero-bg.jpg',
            '/static/images/icons/icon-192x192.png'
        ];
        
        criticalImages.forEach(src => this.preloadImage(src));
    }
    
    preloadFont(href) {
        if (this.preloadedResources.has(href)) return;
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        link.onload = () => {
            link.rel = 'stylesheet';
        };
        
        document.head.appendChild(link);
        this.preloadedResources.add(href);
    }
    
    preloadImage(src) {
        if (this.preloadedResources.has(src)) return;
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        
        document.head.appendChild(link);
        this.preloadedResources.add(src);
    }
    
    preloadScript(src) {
        if (this.preloadedResources.has(src)) return;
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'script';
        link.href = src;
        
        document.head.appendChild(link);
        this.preloadedResources.add(src);
    }
    
    setupPreloadOnHover() {
        // Preload resources when user hovers over links
        document.addEventListener('mouseover', (e) => {
            const link = e.target.closest('a[href]');
            if (link && link.hostname === window.location.hostname) {
                this.preloadPage(link.href);
            }
        });
    }
    
    preloadPage(url) {
        if (this.preloadedResources.has(url)) return;
        
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        
        document.head.appendChild(link);
        this.preloadedResources.add(url);
    }
}

// Performance Monitor
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }
    
    init() {
        if ('performance' in window) {
            this.measurePageLoad();
            this.measureResourceTiming();
            this.setupPerformanceObserver();
        }
    }
    
    measurePageLoad() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                
                this.metrics.pageLoad = {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    totalTime: navigation.loadEventEnd - navigation.fetchStart,
                    firstByte: navigation.responseStart - navigation.requestStart,
                    domInteractive: navigation.domInteractive - navigation.fetchStart
                };
                
                this.reportMetrics();
            }, 0);
        });
    }
    
    measureResourceTiming() {
        const resources = performance.getEntriesByType('resource');
        
        this.metrics.resources = {
            totalResources: resources.length,
            slowResources: resources.filter(r => r.duration > 1000),
            largeResources: resources.filter(r => r.transferSize > 100000)
        };
    }
    
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // Observe Largest Contentful Paint
            const lcpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.lcp = lastEntry.startTime;
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            
            // Observe First Input Delay
            const fidObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    this.metrics.fid = entry.processingStart - entry.startTime;
                });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
            
            // Observe Cumulative Layout Shift
            const clsObserver = new PerformanceObserver((list) => {
                let clsValue = 0;
                list.getEntries().forEach(entry => {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                });
                this.metrics.cls = clsValue;
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });
        }
    }
    
    reportMetrics() {
        console.log('Performance Metrics:', this.metrics);
        
        // Send to analytics if available
        if (window.gtag) {
            gtag('event', 'page_performance', {
                event_category: 'Performance',
                custom_map: {
                    metric1: 'page_load_time',
                    metric2: 'lcp',
                    metric3: 'fid'
                },
                metric1: this.metrics.pageLoad?.totalTime || 0,
                metric2: this.metrics.lcp || 0,
                metric3: this.metrics.fid || 0
            });
        }
    }
}

// Initialize performance optimizations
let lazyLoader, resourcePreloader, performanceMonitor;

function initializePerformance() {
    lazyLoader = new LazyLoader();
    resourcePreloader = new ResourcePreloader();
    performanceMonitor = new PerformanceMonitor();
    
    console.log('🚀 Performance optimizations initialized');
}

// Export for use in other modules
export {
    LazyLoader,
    ResourcePreloader,
    PerformanceMonitor,
    initializePerformance
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePerformance);
} else {
    initializePerformance();
}
