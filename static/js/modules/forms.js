/**
 * CozyWish Forms Module
 * Modern form handling with loading states and validation
 */

// Form loading states
export function initializeFormLoading() {
    document.querySelectorAll('form').forEach(function (form) {
        // Skip search forms to prevent unwanted spinners
        if (form.action && form.action.includes('venue_search')) {
            return;
        }

        form.addEventListener('submit', function () {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.disabled) {
                submitBtn.disabled = true;
                
                // Create spinner element
                const spinner = document.createElement('span');
                spinner.className = 'spinner-border spinner-border-sm ms-2';
                spinner.setAttribute('role', 'status');
                spinner.setAttribute('aria-hidden', 'true');
                
                // Store original text
                submitBtn.dataset.originalText = submitBtn.innerHTML;
                submitBtn.appendChild(spinner);
            }
        });
    });
}

// Button spinner functionality
export function initializeButtonSpinners() {
    document.querySelectorAll('.spinner-button').forEach(function(btn) {
        btn.addEventListener('click', function () {
            if (btn.dataset.spinnerActive) return;
            
            btn.dataset.spinnerActive = 'true';
            
            const spinner = document.createElement('span');
            spinner.className = 'spinner-border spinner-border-sm ms-2';
            spinner.setAttribute('role', 'status');
            spinner.setAttribute('aria-hidden', 'true');
            
            btn.appendChild(spinner);
            
            if (btn.tagName === 'BUTTON') {
                btn.disabled = true;
            }
        });
    });
}

// Form validation helpers
export function validateForm(form) {
    const errors = {};
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            errors[field.name] = 'This field is required';
        }
    });
    
    // Email validation
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            errors[field.name] = 'Please enter a valid email address';
        }
    });
    
    return errors;
}

// Email validation helper
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    initializeFormLoading();
    initializeButtonSpinners();
});
