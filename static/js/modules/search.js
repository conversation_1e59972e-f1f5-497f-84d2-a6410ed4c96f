/**
 * CozyWish Search Module
 * Modern search functionality replacing jQuery implementations
 */

// Initialize search functionality
export function initializeSearch() {
    // Sidebar search
    initializeSidebarSearch();
    
    // Booking search
    initializeBookingSearch();
    
    // Homepage search
    initializeHomepageSearch();
}

// Sidebar search functionality
function initializeSidebarSearch() {
    const input = document.getElementById('sidebarSearch');
    if (!input) return;
    
    const items = document.querySelectorAll('#adminSidebar li.nav-item');
    
    input.addEventListener('input', function () {
        const query = this.value.toLowerCase();
        
        items.forEach(function (li) {
            const text = li.textContent.toLowerCase();
            li.style.display = text.includes(query) ? '' : 'none';
        });
    });
}

// Booking search functionality
function initializeBookingSearch() {
    const searchInput = document.getElementById('booking-search');
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function() {
        const term = this.value.toLowerCase();
        const bookingCards = document.querySelectorAll('#bookings-results .card, #booking-results .card');
        
        bookingCards.forEach(card => {
            const text = card.textContent.toLowerCase();
            card.style.display = text.includes(term) ? '' : 'none';
        });
    });
}

// Homepage search functionality
function initializeHomepageSearch() {
    const searchForm = document.getElementById('homepage-search-form');
    if (!searchForm) return;
    
    const searchInput = searchForm.querySelector('input[type="search"]');
    const suggestionsContainer = document.createElement('div');
    suggestionsContainer.className = 'search-suggestions position-absolute w-100 bg-white border rounded-bottom shadow-sm';
    suggestionsContainer.style.display = 'none';
    suggestionsContainer.style.zIndex = '1000';
    
    searchInput.parentNode.appendChild(suggestionsContainer);
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            hideSuggestions();
            return;
        }
        
        searchTimeout = setTimeout(() => {
            fetchSearchSuggestions(query, suggestionsContainer);
        }, 300);
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(event) {
        if (!searchInput.contains(event.target) && !suggestionsContainer.contains(event.target)) {
            hideSuggestions();
        }
    });
    
    function hideSuggestions() {
        suggestionsContainer.style.display = 'none';
    }
}

// Fetch search suggestions
async function fetchSearchSuggestions(query, container) {
    try {
        const response = await fetch(`/api/search/suggestions/?q=${encodeURIComponent(query)}`);
        
        if (!response.ok) {
            throw new Error('Search request failed');
        }
        
        const suggestions = await response.json();
        displaySuggestions(suggestions, container);
    } catch (error) {
        console.error('Search suggestions error:', error);
        container.style.display = 'none';
    }
}

// Display search suggestions
function displaySuggestions(suggestions, container) {
    if (!suggestions || suggestions.length === 0) {
        container.style.display = 'none';
        return;
    }
    
    container.innerHTML = suggestions.map(suggestion => `
        <div class="search-suggestion p-2 border-bottom cursor-pointer" data-url="${suggestion.url}">
            <div class="d-flex align-items-center">
                <i class="${suggestion.icon || 'fas fa-search'} me-2 text-muted"></i>
                <div>
                    <div class="fw-medium">${suggestion.title}</div>
                    ${suggestion.description ? `<small class="text-muted">${suggestion.description}</small>` : ''}
                </div>
            </div>
        </div>
    `).join('');
    
    container.style.display = 'block';
    
    // Add click handlers
    container.querySelectorAll('.search-suggestion').forEach(item => {
        item.addEventListener('click', function() {
            const url = this.dataset.url;
            if (url) {
                window.location.href = url;
            }
        });
    });
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeSearch);
