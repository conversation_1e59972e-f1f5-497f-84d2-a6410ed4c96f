/**
 * CozyWish Notifications Module
 * Modern notification system with Alpine.js integration
 */

// Notification types
export const NOTIFICATION_TYPES = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
};

// Create notification element
export function createNotification(message, type = NOTIFICATION_TYPES.INFO, duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${getBootstrapClass(type)} alert-dismissible fade show`;
    notification.setAttribute('role', 'alert');
    
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="${getIconClass(type)} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // Auto-remove after duration
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }
    
    return notification;
}

// Get Bootstrap class for notification type
function getBootstrapClass(type) {
    const classMap = {
        [NOTIFICATION_TYPES.SUCCESS]: 'success',
        [NOTIFICATION_TYPES.ERROR]: 'danger',
        [NOTIFICATION_TYPES.WARNING]: 'warning',
        [NOTIFICATION_TYPES.INFO]: 'info'
    };
    return classMap[type] || 'info';
}

// Get icon class for notification type
function getIconClass(type) {
    const iconMap = {
        [NOTIFICATION_TYPES.SUCCESS]: 'fas fa-check-circle',
        [NOTIFICATION_TYPES.ERROR]: 'fas fa-exclamation-circle',
        [NOTIFICATION_TYPES.WARNING]: 'fas fa-exclamation-triangle',
        [NOTIFICATION_TYPES.INFO]: 'fas fa-info-circle'
    };
    return iconMap[type] || 'fas fa-info-circle';
}

// Show notification in container
export function showNotification(message, type = NOTIFICATION_TYPES.INFO, duration = 5000) {
    let container = document.querySelector('.notification-container');
    
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1100';
        document.body.appendChild(container);
    }
    
    const notification = createNotification(message, type, duration);
    container.appendChild(notification);
    
    // Trigger Bootstrap alert animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
}

// Global notification function
window.showNotification = showNotification;

// Export for module use
export { showNotification as default };
