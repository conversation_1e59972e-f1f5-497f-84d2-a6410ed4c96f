/**
 * CozyWish Modern Image Galleries
 * Alpine.js-based image gallery with lazy loading and lightbox
 */

// Modern Gallery Alpine.js Component
window.modernGallery = function(initialImages = []) {
    return {
        images: initialImages,
        loading: true,
        lightboxOpen: false,
        currentIndex: 0,
        imageLoaded: false,
        showThumbnails: false,

        init() {
            // Initialize lazy loading
            this.initializeLazyLoading();

            // Set loading to false after a short delay
            setTimeout(() => {
                this.loading = false;
            }, 300);

            // Keyboard navigation
            this.setupKeyboardNavigation();
        },

        get currentImage() {
            return this.images[this.currentIndex];
        },

        openLightbox(index) {
            this.currentIndex = index;
            this.lightboxOpen = true;
            this.imageLoaded = false;
            document.body.style.overflow = 'hidden';

            // Preload adjacent images
            this.preloadAdjacentImages();
        },

        closeLightbox() {
            this.lightboxOpen = false;
            document.body.style.overflow = '';
        },

        nextImage() {
            if (this.currentIndex < this.images.length - 1) {
                this.currentIndex++;
                this.imageLoaded = false;
                this.preloadAdjacentImages();
            }
        },

        previousImage() {
            if (this.currentIndex > 0) {
                this.currentIndex--;
                this.imageLoaded = false;
                this.preloadAdjacentImages();
            }
        },

        goToImage(index) {
            this.currentIndex = index;
            this.imageLoaded = false;
            this.preloadAdjacentImages();
        },

        setupKeyboardNavigation() {
            document.addEventListener('keydown', (e) => {
                if (this.lightboxOpen) {
                    switch(e.key) {
                        case 'ArrowLeft':
                            e.preventDefault();
                            this.previousImage();
                            break;
                        case 'ArrowRight':
                            e.preventDefault();
                            this.nextImage();
                            break;
                        case 'Escape':
                            e.preventDefault();
                            this.closeLightbox();
                            break;
                    }
                }
            });
        },

        initializeLazyLoading() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.classList.remove('lazy');
                                img.classList.add('loaded');
                                imageObserver.unobserve(img);
                            }
                        }
                    });
                }, {
                    rootMargin: '50px'
                });

                this.$nextTick(() => {
                    this.$el.querySelectorAll('img[data-src]').forEach(img => {
                        imageObserver.observe(img);
                    });
                });
            } else {
                // Fallback for browsers without IntersectionObserver
                this.$nextTick(() => {
                    this.$el.querySelectorAll('img[data-src]').forEach(img => {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                    });
                });
            }
        },

        preloadAdjacentImages() {
            // Preload previous and next images for smoother navigation
            const preloadIndexes = [
                this.currentIndex - 1,
                this.currentIndex + 1
            ].filter(index => index >= 0 && index < this.images.length);

            preloadIndexes.forEach(index => {
                const image = this.images[index];
                if (image && image.url) {
                    const img = new Image();
                    img.src = image.url;
                }
            });
        },

        // Gallery management methods
        addImage(image) {
            this.images.push(image);
        },

        removeImage(index) {
            this.images.splice(index, 1);
            if (this.currentIndex >= this.images.length) {
                this.currentIndex = Math.max(0, this.images.length - 1);
            }
        },

        updateImage(index, image) {
            this.images[index] = { ...this.images[index], ...image };
        },

        // Utility methods
        toggleThumbnails() {
            this.showThumbnails = !this.showThumbnails;
        },

        getImageAspectRatio(image) {
            return image.width && image.height ? image.width / image.height : 4/3;
        }
    }
};

// Initialize all gallery functionality (legacy support)
export function initializeGalleries() {
    initializeLazyLoading();
    initializeImageModals();
    initializeImageCarousels();
}

// Lazy loading for images
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('loaded');
                    observer.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback for older browsers
        document.querySelectorAll('img[data-src]').forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            img.classList.add('loaded');
        });
    }
}

// Image modal functionality
function initializeImageModals() {
    const images = document.querySelectorAll('.gallery-image, .venue-image');
    
    images.forEach(img => {
        img.addEventListener('click', function() {
            openImageModal(this.src, this.alt);
        });
    });
}

// Open image in modal
function openImageModal(src, alt) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('imageModal');
    
    if (!modal) {
        modal = createImageModal();
        document.body.appendChild(modal);
    }
    
    const modalImg = modal.querySelector('.modal-image');
    const modalCaption = modal.querySelector('.modal-caption');
    
    modalImg.src = src;
    modalImg.alt = alt;
    modalCaption.textContent = alt;
    
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

// Create image modal element
function createImageModal() {
    const modal = document.createElement('div');
    modal.id = 'imageModal';
    modal.className = 'modal fade';
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-labelledby', 'imageModalLabel');
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img class="modal-image img-fluid" src="" alt="">
                    <p class="modal-caption mt-2 text-muted"></p>
                </div>
            </div>
        </div>
    `;
    
    return modal;
}

// Initialize image carousels
function initializeImageCarousels() {
    const carousels = document.querySelectorAll('.image-carousel');
    
    carousels.forEach(carousel => {
        const images = carousel.querySelectorAll('img');
        
        if (images.length > 1) {
            createCarouselControls(carousel);
        }
    });
}

// Create carousel controls
function createCarouselControls(carousel) {
    const images = carousel.querySelectorAll('img');
    let currentIndex = 0;
    
    // Create navigation buttons
    const prevBtn = document.createElement('button');
    prevBtn.className = 'carousel-btn carousel-prev';
    prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
    prevBtn.setAttribute('aria-label', 'Previous image');
    
    const nextBtn = document.createElement('button');
    nextBtn.className = 'carousel-btn carousel-next';
    nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
    nextBtn.setAttribute('aria-label', 'Next image');
    
    // Create indicators
    const indicators = document.createElement('div');
    indicators.className = 'carousel-indicators';
    
    images.forEach((_, index) => {
        const indicator = document.createElement('button');
        indicator.className = `carousel-indicator ${index === 0 ? 'active' : ''}`;
        indicator.setAttribute('aria-label', `Go to image ${index + 1}`);
        indicator.addEventListener('click', () => goToImage(index));
        indicators.appendChild(indicator);
    });
    
    // Add event listeners
    prevBtn.addEventListener('click', () => {
        currentIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;
        updateCarousel();
    });
    
    nextBtn.addEventListener('click', () => {
        currentIndex = (currentIndex + 1) % images.length;
        updateCarousel();
    });
    
    function goToImage(index) {
        currentIndex = index;
        updateCarousel();
    }
    
    function updateCarousel() {
        images.forEach((img, index) => {
            img.style.display = index === currentIndex ? 'block' : 'none';
        });
        
        indicators.querySelectorAll('.carousel-indicator').forEach((indicator, index) => {
            indicator.classList.toggle('active', index === currentIndex);
        });
    }
    
    // Append controls to carousel
    carousel.appendChild(prevBtn);
    carousel.appendChild(nextBtn);
    carousel.appendChild(indicators);
    
    // Initialize first image
    updateCarousel();
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeGalleries);
