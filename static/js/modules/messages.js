/**
 * CozyWish Messages Module
 * Modern message handling with Alpine.js and Bootstrap integration
 */

// Initialize Bootstrap toasts
export function initializeToasts() {
    const toastElements = document.querySelectorAll('.toast');
    toastElements.forEach(function(el) {
        const toast = new bootstrap.Toast(el, {
            autohide: true,
            delay: 5000
        });
        toast.show();
    });
}

// Show notification function
export function showNotification(message, type = 'info', duration = 5000) {
    const event = new CustomEvent('show-notification', {
        detail: { message, type, duration }
    });
    window.dispatchEvent(event);
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeToasts);
