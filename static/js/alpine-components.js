/**
 * CozyWish Alpine.js Components
 * Modern reactive components replacing jQuery functionality
 */

import Alpine from 'alpinejs';

// Search Component
Alpine.data('search', () => ({
    query: '',
    results: [],
    loading: false,
    showResults: false,
    
    async performSearch() {
        if (this.query.length < 2) {
            this.results = [];
            this.showResults = false;
            return;
        }
        
        this.loading = true;
        
        try {
            const response = await fetch(`/api/search/?q=${encodeURIComponent(this.query)}`);
            this.results = await response.json();
            this.showResults = true;
        } catch (error) {
            console.error('Search error:', error);
            this.results = [];
        } finally {
            this.loading = false;
        }
    },
    
    selectResult(result) {
        this.query = result.name;
        this.showResults = false;
        // Trigger navigation or form submission
        window.location.href = result.url;
    },
    
    clearSearch() {
        this.query = '';
        this.results = [];
        this.showResults = false;
    }
}));

// Image Gallery Component
Alpine.data('imageGallery', () => ({
    currentIndex: 0,
    images: [],
    showModal: false,
    loading: true,
    
    init() {
        // Initialize images from data attribute or fetch from API
        const galleryElement = this.$el;
        const imagesData = galleryElement.dataset.images;
        
        if (imagesData) {
            this.images = JSON.parse(imagesData);
        }
        
        this.loading = false;
    },
    
    openModal(index) {
        this.currentIndex = index;
        this.showModal = true;
        document.body.style.overflow = 'hidden';
    },
    
    closeModal() {
        this.showModal = false;
        document.body.style.overflow = '';
    },
    
    nextImage() {
        this.currentIndex = (this.currentIndex + 1) % this.images.length;
    },
    
    prevImage() {
        this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;
    },
    
    goToImage(index) {
        this.currentIndex = index;
    }
}));

// Form Handler Component
Alpine.data('formHandler', () => ({
    loading: false,
    errors: {},
    success: false,
    
    async submitForm(event) {
        event.preventDefault();
        
        this.loading = true;
        this.errors = {};
        this.success = false;
        
        const form = event.target;
        const formData = new FormData(form);
        
        try {
            const response = await fetch(form.action, {
                method: form.method,
                body: formData,
                headers: {
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                }
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.success = true;
                if (result.redirect) {
                    window.location.href = result.redirect;
                }
            } else {
                this.errors = result.errors || {};
            }
        } catch (error) {
            console.error('Form submission error:', error);
            this.errors = { general: 'An error occurred. Please try again.' };
        } finally {
            this.loading = false;
        }
    },
    
    hasError(field) {
        return this.errors[field] && this.errors[field].length > 0;
    },
    
    getError(field) {
        return this.errors[field] ? this.errors[field][0] : '';
    }
}));

// Notification Component
Alpine.data('notifications', () => ({
    notifications: [],
    
    init() {
        // Listen for notification events
        window.addEventListener('show-notification', (event) => {
            this.addNotification(event.detail);
        });
    },
    
    addNotification(notification) {
        const id = Date.now();
        const newNotification = {
            id,
            type: notification.type || 'info',
            message: notification.message,
            duration: notification.duration || 5000
        };
        
        this.notifications.push(newNotification);
        
        // Auto-remove after duration
        if (newNotification.duration > 0) {
            setTimeout(() => {
                this.removeNotification(id);
            }, newNotification.duration);
        }
    },
    
    removeNotification(id) {
        this.notifications = this.notifications.filter(n => n.id !== id);
    }
}));

// Dropdown Component
Alpine.data('dropdown', () => ({
    open: false,
    
    toggle() {
        this.open = !this.open;
    },
    
    close() {
        this.open = false;
    },
    
    init() {
        // Close dropdown when clicking outside
        this.$watch('open', (value) => {
            if (value) {
                document.addEventListener('click', this.closeOnClickOutside);
            } else {
                document.removeEventListener('click', this.closeOnClickOutside);
            }
        });
    },
    
    closeOnClickOutside(event) {
        if (!this.$el.contains(event.target)) {
            this.open = false;
        }
    }
}));

// Modal Component
Alpine.data('modal', () => ({
    show: false,
    
    open() {
        this.show = true;
        document.body.style.overflow = 'hidden';
    },
    
    close() {
        this.show = false;
        document.body.style.overflow = '';
    },
    
    init() {
        // Close modal on escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.show) {
                this.close();
            }
        });
    }
}));

// Tabs Component
Alpine.data('tabs', () => ({
    activeTab: '',
    
    init() {
        // Set first tab as active by default
        const firstTab = this.$el.querySelector('[data-tab]');
        if (firstTab) {
            this.activeTab = firstTab.dataset.tab;
        }
    },
    
    setActiveTab(tab) {
        this.activeTab = tab;
    },
    
    isActive(tab) {
        return this.activeTab === tab;
    }
}));

// Accordion Component
Alpine.data('accordion', () => ({
    openItems: [],
    
    toggle(item) {
        if (this.openItems.includes(item)) {
            this.openItems = this.openItems.filter(i => i !== item);
        } else {
            this.openItems.push(item);
        }
    },
    
    isOpen(item) {
        return this.openItems.includes(item);
    }
}));
