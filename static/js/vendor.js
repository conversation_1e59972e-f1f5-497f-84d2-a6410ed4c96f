/**
 * CozyWish Vendor Libraries
 * Third-party library imports and configurations
 */

// Import Choices.js for enhanced select elements
import Choices from 'choices.js';

// Import Glide.js for carousels and sliders
import { Glide, GlideSlide } from '@glidejs/glide';

// Import Leaflet for maps
import L from 'leaflet';

// Configure and export vendor libraries
window.Choices = Choices;
window.Glide = Glide;
window.L = L;

// Initialize vendor libraries with default configurations
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Choices.js for select elements
    const selectElements = document.querySelectorAll('.enhanced-select');
    selectElements.forEach(element => {
        new Choices(element, {
            searchEnabled: true,
            itemSelectText: '',
            classNames: {
                containerOuter: 'choices',
                containerInner: 'choices__inner',
                input: 'choices__input',
                inputCloned: 'choices__input--cloned',
                list: 'choices__list',
                listItems: 'choices__list--multiple',
                listSingle: 'choices__list--single',
                listDropdown: 'choices__list--dropdown',
                item: 'choices__item',
                itemSelectable: 'choices__item--selectable',
                itemDisabled: 'choices__item--disabled',
                itemChoice: 'choices__item--choice',
                placeholder: 'choices__placeholder',
                group: 'choices__group',
                groupHeading: 'choices__heading',
                button: 'choices__button',
                activeState: 'is-active',
                focusState: 'is-focused',
                openState: 'is-open',
                disabledState: 'is-disabled',
                highlightedState: 'is-highlighted',
                selectedState: 'is-selected',
                flippedState: 'is-flipped',
                loadingState: 'is-loading',
                noResults: 'has-no-results',
                noChoices: 'has-no-choices'
            }
        });
    });
    
    // Initialize Glide.js for image galleries
    const glideElements = document.querySelectorAll('.glide');
    glideElements.forEach(element => {
        new Glide(element, {
            type: 'carousel',
            startAt: 0,
            perView: 3,
            gap: 20,
            autoplay: 4000,
            hoverpause: true,
            breakpoints: {
                1024: {
                    perView: 2
                },
                768: {
                    perView: 1
                }
            }
        }).mount();
    });
    
    // Initialize Leaflet maps
    const mapElements = document.querySelectorAll('.leaflet-map');
    mapElements.forEach(element => {
        const lat = parseFloat(element.dataset.lat) || 51.505;
        const lng = parseFloat(element.dataset.lng) || -0.09;
        const zoom = parseInt(element.dataset.zoom) || 13;
        
        const map = L.map(element).setView([lat, lng], zoom);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // Add marker if coordinates are provided
        if (element.dataset.lat && element.dataset.lng) {
            L.marker([lat, lng]).addTo(map);
        }
    });
});

// Export configurations for external use
export const vendorConfig = {
    choices: {
        searchEnabled: true,
        itemSelectText: '',
        shouldSort: false
    },
    glide: {
        type: 'carousel',
        startAt: 0,
        perView: 3,
        gap: 20,
        autoplay: 4000,
        hoverpause: true
    },
    leaflet: {
        defaultZoom: 13,
        tileLayer: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        attribution: '© OpenStreetMap contributors'
    }
};
