# --- Django Imports ---
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.http import HttpResponse
from django.urls import include, path

# --- Wagtail Imports ---
from wagtail.admin import urls as wagtailadmin_urls
from wagtail import urls as wagtail_urls
from wagtail.documents import urls as wagtaildocs_urls

# --- Local App Imports ---
from admin_app.views.common import home_view


def favicon_view(request):
    """Return empty response for favicon.ico to prevent 404 errors."""
    return HttpResponse(status=204)  # No Content


# --- URL Configuration ---
urlpatterns = [
    path("admin/", admin.site.urls),  # Django admin dashboard
    path("cms-admin/", include(wagtailadmin_urls)),  # Wagtail CMS admin
    path("documents/", include(wagtaildocs_urls)),  # Wagtail documents
    path("admin-panel/", include("admin_app.urls")),  # Custom admin panel
    path("accounts/", include("accounts_app.urls")),  # User authentication and profiles
    path("auth/", include("allauth.urls")),  # Django-allauth authentication URLs
    path("avatar/", include("avatar.urls")),  # Django Avatar URLs
    path("venues/", include("venues_app.urls")),  # Venue management and search
    path("discounts/", include("discount_app.urls")),  # Discount management
    path("bookings/", include("booking_cart_app.urls")),  # Booking and cart management
    path("payments/", include("payments_app.urls")),  # Payment processing and refunds
    path(
        "dashboard/", include("dashboard_app.urls")
    ),  # Customer and provider dashboards
    path("reviews/", include("review_app.urls")),  # Review and rating management
    path(
        "notifications/", include("notifications_app.urls")
    ),  # Notification management
    path("utility/", include("utility_app.urls")),  # Public landing pages and utilities
    path("favicon.ico", favicon_view),  # Favicon handler to prevent 404 errors
    
    # Wagtail CMS pages (should be last to catch all remaining URLs)
    path("", include(wagtail_urls)),  # Wagtail CMS pages
]


# --- Django Pictures Configuration ---
# Django Pictures placeholder URLs (always include for system checks)
try:
    from pictures.conf import get_settings as get_pictures_settings
    if get_pictures_settings().USE_PLACEHOLDERS:
        urlpatterns += [
            path("_pictures/", include("pictures.urls")),
        ]
except ImportError:
    pass

# --- Media and Static Files Configuration ---
if settings.DEBUG:
    # Serve media files (user uploads) during development
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

    # Serve static files (CSS, JS, images) during development
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)


# --- Error Handlers ---
handler404 = "utils.error_handlers.custom_404_view"
handler500 = "utils.error_handlers.custom_500_view"
handler403 = "utils.error_handlers.custom_403_view"
handler400 = "utils.error_handlers.custom_400_view"
