"""
Temporary settings file for migration generation.
This excludes problematic admin apps that prevent migrations.
"""
import os
from .base import *

# Remove problematic admin apps for migration generation
TEMP_INSTALLED_APPS = []
for app in INSTALLED_APPS:
    if app not in ['unfold', 'unfold.contrib.filters', 'unfold.contrib.forms', 
                   'unfold.contrib.inlines', 'unfold.contrib.import_export',
                   'admin_tools', 'admin_tools.theming', 'admin_tools.menu',
                   'admin_tools.dashboard', 'import_export', 'jazzmin']:
        TEMP_INSTALLED_APPS.append(app)

INSTALLED_APPS = TEMP_INSTALLED_APPS

# Disable problematic middleware
TEMP_MIDDLEWARE = []
for middleware in MIDDLEWARE:
    if middleware not in ['admin_app.middleware.AdminCSPMiddleware']:
        TEMP_MIDDLEWARE.append(middleware)

MIDDLEWARE = TEMP_MIDDLEWARE

# Add recurrence app
INSTALLED_APPS.append('recurrence') 