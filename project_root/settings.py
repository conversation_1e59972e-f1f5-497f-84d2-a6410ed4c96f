# --- Standard Library Imports ---
import os
import sys
from pathlib import Path

import dj_database_url
import environ

# --- Local App Imports ---
from config.logging import LOGGING

# --- Environment Configuration ---
env = environ.Env(
    # Set casting and default values
    DEBUG=(bool, False),
    SECRET_KEY=(str, ""),
    LOG_LEVEL=(str, "INFO"),
    PLATFORM_FEE_RATE=(float, 0.05),
    DASHBOARD_CACHE_TIMEOUT=(int, 300),
    NOTIFICATION_CACHE_TIMEOUT=(int, 60),
    EMAIL_PORT=(int, 587),
    EMAIL_USE_TLS=(bool, True),
    FORCE_EMAIL_BACKEND=(bool, False),
)

# --- Base Directory ---
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# --- Read Environment File ---
environ.Env.read_env(BASE_DIR / ".env")


# --- Environment Validation ---
def validate_environment():
    """Validate required environment variables."""
    required_vars = ["SECRET_KEY"]
    missing_vars = []

    for var in required_vars:
        if not env(var, default=None):
            missing_vars.append(var)

    if missing_vars:
        raise environ.ImproperlyConfigured(
            f"Missing required environment variables: {', '.join(missing_vars)}"
        )


# Validate environment on import
validate_environment()

# --- Logs Configuration ---
LOG_LEVEL = env("LOG_LEVEL").upper()

# --- Core Configuration ---
SECRET_KEY = env("SECRET_KEY")
DEBUG = env("DEBUG")
PLATFORM_FEE_RATE = env("PLATFORM_FEE_RATE")
DASHBOARD_CACHE_TIMEOUT = env("DASHBOARD_CACHE_TIMEOUT")
NOTIFICATION_CACHE_TIMEOUT = env("NOTIFICATION_CACHE_TIMEOUT")
ENABLE_TEST_VIEW = DEBUG


# --- Allowed Hosts & CSRF ---
ALLOWED_HOSTS = [".cozywish.com", "cozywish.onrender.com"]
RENDER_EXTERNAL_HOSTNAME = os.environ.get("RENDER_EXTERNAL_HOSTNAME")
if RENDER_EXTERNAL_HOSTNAME:
    ALLOWED_HOSTS.append(RENDER_EXTERNAL_HOSTNAME)
if DEBUG:
    ALLOWED_HOSTS.extend(["localhost", "127.0.0.1", "testserver"])

CSRF_TRUSTED_ORIGINS = ["https://www.cozywish.com", "https://cozywish.onrender.com"]


# --- Installed Applications ---
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.humanize",  # For humanize template tags
    "django.contrib.sites",  # Required for actstream
    # Third-party apps
    "storages",
    "widget_tweaks",
    "crispy_forms",
    "crispy_bootstrap5",  # Upgraded from crispy_bootstrap4
    "formtools",
    "django_htmx",
    "django_cleanup",
    "compressor",  # Static file compression
    "avatar",  # Profile picture management
    "phonenumber_field",  # International phone number support

    # Location Management
    "cities_light",  # Modern city/location data
    "address",  # Address handling
    # "geoposition",  # GPS coordinates - incompatible with Django 5.2
    # "leaflet",  # Interactive maps - requires GDAL system dependency

    # Search and Filtering
    "django_filters",  # Advanced filtering
    "dal",  # Django autocomplete light
    "dal_select2",  # Select2 integration
    "taggit",  # Tag management
    "watson",  # Full-text search

    # Advanced Features
    "actstream",  # Activity stream tracking
    "star_ratings",  # Star rating system
    "appointment",  # Advanced booking system

    # Project apps
    "accounts_app",
    "utility_app",
    "venues_app",
    "discount_app",
    "booking_cart_app",
    "payments_app",
    "dashboard_app",
    "review_app",
    "notifications_app",
    "admin_app",
    "utils",
]


# --- Middleware ---
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "admin_app.middleware.AdminCSPMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django_htmx.middleware.HtmxMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "dashboard_app.middleware.DashboardAccessMiddleware",
]


# --- URL Configuration ---
ROOT_URLCONF = "project_root.urls"
WSGI_APPLICATION = "project_root.wsgi.application"


# --- Templates ---
TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "admin_app.context_processors.recent_admin_links",
                "booking_cart_app.context_processors.cart_context",
                "notifications_app.context_processors.notifications_context",
                "dashboard_app.context_processors.provider_context",
            ],
        },
    },
]


# --- Database Configuration ---
DATABASE_URL = env("DATABASE_URL", default=None)
if DATABASE_URL:
    DATABASES = {"default": dj_database_url.parse(DATABASE_URL, conn_max_age=600)}
else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        }
    }


# --- Test Database Configuration ---
if "test" in sys.argv:
    DATABASES["default"] = {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
    }


# --- Password Validation ---
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"
    },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]


# --- Internationalization ---
LANGUAGE_CODE = "en-us"
TIME_ZONE = "UTC"
USE_I18N = True
USE_TZ = True


# --- Testing Flag ---
TESTING = "test" in sys.argv


# --- Static Files Configuration ---
STATIC_URL = "/static/"
STATIC_ROOT = BASE_DIR / "staticfiles"
STATICFILES_DIRS = [BASE_DIR / "static"]

# Add compressor finder to staticfiles finders
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
    "compressor.finders.CompressorFinder",
]

# --- Django Compressor Configuration ---
COMPRESS_ENABLED = not DEBUG  # Enable compression in production
COMPRESS_OFFLINE = not DEBUG  # Pre-compress files in production
COMPRESS_CSS_FILTERS = [
    "compressor.filters.css_default.CssAbsoluteFilter",
    "compressor.filters.cssmin.rCSSMinFilter",
]
COMPRESS_JS_FILTERS = [
    "compressor.filters.jsmin.rJSMinFilter",
]

# Compressor storage and URL settings
COMPRESS_STORAGE = "compressor.storage.CompressorFileStorage"
COMPRESS_URL = STATIC_URL
COMPRESS_ROOT = STATIC_ROOT

# Cache settings for compressor
COMPRESS_CACHE_BACKEND = "default"
COMPRESS_CACHE_KEY_FUNCTION = "compressor.cache.simple_cachekey"

# Output directory for compressed files
COMPRESS_OUTPUT_DIR = "CACHE"

# Rebuild compressed files when source files change
COMPRESS_REBUILD_TIMEOUT = 2592000  # 30 days

# Advanced compression settings
COMPRESS_PRECOMPILERS = (
    ("text/coffeescript", "coffee --compile --stdio"),
    ("text/less", "lessc {infile} {outfile}"),
    ("text/x-sass", "sass {infile} {outfile}"),
    ("text/x-scss", "sass --scss {infile} {outfile}"),
)

# Compression parser settings
COMPRESS_PARSER = "compressor.parser.AutoSelectParser"

# File extension handling
COMPRESS_CSS_HASHING_METHOD = "mtime"
COMPRESS_JS_HASHING_METHOD = "mtime"

# Offline compression settings
COMPRESS_OFFLINE_CONTEXT = {
    "STATIC_URL": STATIC_URL,
}

# Compression manifest settings
COMPRESS_OFFLINE_MANIFEST = "manifest.json"

if not DEBUG:
    STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = False  # Disable in production for performance

    # Advanced WhiteNoise settings for production
    WHITENOISE_MAX_AGE = 31536000  # 1 year cache for static files
    WHITENOISE_SKIP_COMPRESS_EXTENSIONS = ["jpg", "jpeg", "png", "gif", "webp", "zip", "gz", "tgz", "bz2", "tbz", "xz", "br"]
    WHITENOISE_IMMUTABLE_FILE_TEST = lambda path, url: True  # Mark all files as immutable for better caching
else:
    # Development settings
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = True


# --- Media Files Configuration ---
if DEBUG:
    MEDIA_URL = "/media/"
    MEDIA_ROOT = BASE_DIR / "media"
    # Use default file system storage for development
    STORAGES = {
        "default": {
            "BACKEND": "django.core.files.storage.FileSystemStorage",
        },
        "staticfiles": {
            "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
        },
    }
else:
    # AWS S3 Configuration
    AWS_ACCESS_KEY_ID = env("AWS_ACCESS_KEY_ID", default=None)
    AWS_SECRET_ACCESS_KEY = env("AWS_SECRET_ACCESS_KEY", default=None)
    AWS_STORAGE_BUCKET_NAME = env("AWS_STORAGE_BUCKET_NAME", default=None)
    AWS_S3_REGION_NAME = env("AWS_S3_REGION_NAME", default="us-east-1")
    AWS_S3_CUSTOM_DOMAIN = env("AWS_S3_CUSTOM_DOMAIN", default=None)

    if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY and AWS_STORAGE_BUCKET_NAME:
        # Django 4.2+ STORAGES configuration (replaces DEFAULT_FILE_STORAGE)
        STORAGES = {
            "default": {
                "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
                "OPTIONS": {
                    "access_key": AWS_ACCESS_KEY_ID,
                    "secret_key": AWS_SECRET_ACCESS_KEY,
                    "bucket_name": AWS_STORAGE_BUCKET_NAME,
                    "region_name": AWS_S3_REGION_NAME,
                    "custom_domain": AWS_S3_CUSTOM_DOMAIN,
                    "file_overwrite": False,
                    "default_acl": None,
                    "signature_version": "s3v4",
                    "addressing_style": "virtual",
                    "use_ssl": True,
                    "verify": True,
                    "object_parameters": {
                        "CacheControl": "max-age=86400",
                    },
                    "querystring_auth": True,
                    "querystring_expire": 3600,  # 1 hour
                },
            },
            "staticfiles": {
                "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
            },
        }

        # Legacy AWS settings for backward compatibility (some packages may still use these)
        # Note: DEFAULT_FILE_STORAGE is deprecated in Django 4.2+ in favor of STORAGES
        AWS_S3_FILE_OVERWRITE = False
        AWS_DEFAULT_ACL = None
        AWS_S3_VERIFY = True
        AWS_S3_USE_SSL = True
        AWS_S3_SIGNATURE_VERSION = "s3v4"
        AWS_S3_ADDRESSING_STYLE = "virtual"
        AWS_QUERYSTRING_AUTH = True
        AWS_QUERYSTRING_EXPIRE = 3600  # 1 hour

        # Performance and caching
        AWS_S3_OBJECT_PARAMETERS = {
            "CacheControl": "max-age=86400",
        }

        # URL configuration
        if AWS_S3_CUSTOM_DOMAIN:
            MEDIA_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/"
        else:
            MEDIA_URL = f"https://{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com/"

        # Ensure URLs don't have double slashes
        if not MEDIA_URL.endswith("/"):
            MEDIA_URL += "/"

    else:
        # Log missing credentials for debugging
        missing_creds = []
        if not AWS_ACCESS_KEY_ID:
            missing_creds.append("AWS_ACCESS_KEY_ID")
        if not AWS_SECRET_ACCESS_KEY:
            missing_creds.append("AWS_SECRET_ACCESS_KEY")
        if not AWS_STORAGE_BUCKET_NAME:
            missing_creds.append("AWS_STORAGE_BUCKET_NAME")

        error_msg = f"AWS S3 credentials are required in production. Missing: {', '.join(missing_creds)}"
        print(f"WARNING: {error_msg}")  # Log to console instead of raising exception

        # Fallback to local storage with warning
        MEDIA_URL = "/media/"
        MEDIA_ROOT = BASE_DIR / "media"
        STORAGES = {
            "default": {
                "BACKEND": "django.core.files.storage.FileSystemStorage",
            },
            "staticfiles": {
                "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
            },
        }


# --- Email Configuration (SendGrid) ---
EMAIL_HOST_PASSWORD = config("EMAIL_HOST_PASSWORD", default="")
FORCE_EMAIL_BACKEND = config("FORCE_EMAIL_BACKEND", default=False, cast=bool)

# Email backend logic:
# 1. If FORCE_EMAIL_BACKEND is True, use SMTP even in debug mode
# 2. If EMAIL_HOST_PASSWORD is set and we're not in test mode, use SMTP
# 3. Otherwise, use console backend for development
if TESTING:
    # Always use console backend for tests
    EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"
elif FORCE_EMAIL_BACKEND or (EMAIL_HOST_PASSWORD and not DEBUG):
    # Use SMTP backend for production or when forced
    EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
elif EMAIL_HOST_PASSWORD and DEBUG:
    # In development with SendGrid configured, ask user what they prefer
    EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
else:
    # Default to console backend for development without email config
    EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

EMAIL_HOST = env("EMAIL_HOST", default="smtp.sendgrid.net")
EMAIL_PORT = env("EMAIL_PORT")
EMAIL_USE_TLS = env("EMAIL_USE_TLS")
EMAIL_HOST_USER = env("EMAIL_HOST_USER", default="apikey")
DEFAULT_FROM_EMAIL = env("DEFAULT_FROM_EMAIL", default="<EMAIL>")
SERVER_EMAIL = env("SERVER_EMAIL", default="<EMAIL>")
EMAIL_TIMEOUT = 30


# --- User Model and Primary Key ---
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"
AUTH_USER_MODEL = "accounts_app.CustomUser"


# --- Authentication URLs ---
LOGIN_URL = "/accounts/customer/login/"
LOGOUT_REDIRECT_URL = "/"


# --- Cache Configuration ---
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "LOCATION": "cozywish-cache",
        "TIMEOUT": 300,
        "OPTIONS": {
            "MAX_ENTRIES": 1000,
            "CULL_FREQUENCY": 3,
        },
    }
}

# --- Celery Configuration ---
CELERY_BROKER_URL = env("CELERY_BROKER_URL", default="redis://localhost:6379/0")
CELERY_RESULT_BACKEND = env("CELERY_RESULT_BACKEND", default="redis://localhost:6379/0")
if "test" in sys.argv:
    CELERY_TASK_ALWAYS_EAGER = True
    CELERY_TASK_EAGER_PROPAGATES = True


# --- Crispy Forms Configuration ---
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# --- Django Avatar Configuration ---
AVATAR_STORAGE_DIR = "avatars"
AVATAR_RESIZE = 300
AVATAR_MAX_AVATARS_PER_USER = 1
AVATAR_MAX_SIZE = 1024 * 1024 * 5  # 5MB
AVATAR_THUMB_FORMAT = "JPEG"
AVATAR_THUMB_QUALITY = 95
AVATAR_DEFAULT_URL = "img/default-avatar.png"

# --- Phone Number Field Configuration ---
PHONENUMBER_DEFAULT_REGION = "US"
PHONENUMBER_DEFAULT_FORMAT = "NATIONAL"

# --- HTMX Configuration ---
# HTMX settings for dynamic interactions
HTMX_CSRF_HEADER_NAME = "X-CSRFToken"

# --- Django Form Tools Configuration ---
# Session-based form wizard storage
FORM_RENDERER = "django.forms.renderers.TemplatesSetting"


# --- Production Security Settings ---
if not DEBUG:
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    X_FRAME_OPTIONS = "DENY"


# --- Forms URL Field Configuration ---
FORMS_URLFIELD_ASSUME_HTTPS = True


# --- Location Management Configuration ---
# Django Cities Light
CITIES_LIGHT_TRANSLATION_LANGUAGES = ["en"]
CITIES_LIGHT_INCLUDE_COUNTRIES = ["US"]  # Focus on US locations
CITIES_LIGHT_INCLUDE_CITY_TYPES = ["PPL", "PPLA", "PPLA2", "PPLA3", "PPLA4", "PPLC"]

# Django Leaflet - Commented out due to GDAL dependency
# LEAFLET_CONFIG = {
#     "DEFAULT_CENTER": (39.8283, -98.5795),  # Center of US
#     "DEFAULT_ZOOM": 4,
#     "MIN_ZOOM": 3,
#     "MAX_ZOOM": 18,
#     "TILES": [
#         ("OpenStreetMap", "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
#             "attribution": "&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors"
#         }),
#     ],
#     "SPATIAL_EXTENT": [-180, -90, 180, 90],
#     "SRID": 4326,
#     "PLUGINS": {
#         "forms": {
#             "auto-include": True
#         }
#     }
# }

# Django Taggit
TAGGIT_CASE_INSENSITIVE = True
TAGGIT_TAGS_FROM_STRING = "taggit.utils.parse_tags"
TAGGIT_STRING_FROM_TAGS = "taggit.utils.edit_string_for_tags"

# Django Watson Search
WATSON_POSTGRESQL_SEARCH_CONFIG = "english"


# --- Advanced Features Configuration ---

# Sites Framework (required for actstream)
SITE_ID = 1

# Activity Stream Settings
ACTSTREAM_SETTINGS = {
    'MANAGER': 'actstream.managers.ActionManager',
    'FETCH_RELATIONS': True,
    'USE_PREFETCH': True,
}

# Star Ratings Settings
STAR_RATINGS_ANONYMOUS = True  # Allow anonymous ratings
STAR_RATINGS_RANGE = 5  # 1-5 star rating
STAR_RATINGS_STAR_HEIGHT = 32  # Star height in pixels
STAR_RATINGS_STAR_WIDTH = 32  # Star width in pixels
STAR_RATINGS_RERATE = True  # Allow users to change their rating
STAR_RATINGS_RERATE_SAME_DELETE = True  # Delete rating if same rating clicked

# Django Appointment Settings
APPOINTMENT_WEBSITE_NAME = "CozyWish"
APPOINTMENT_ADMIN_BASE_TEMPLATE = "admin/base.html"
APPOINTMENT_BASE_TEMPLATE = "base.html"
APPOINTMENT_SLOT_DURATION = 30  # Default slot duration in minutes
APPOINTMENT_LEAD_TIME = 24  # Minimum hours before appointment
APPOINTMENT_FINISH_TIME = 24  # Maximum hours for appointment
APPOINTMENT_BUFFER_TIME = 15  # Buffer time between appointments in minutes


"""
## Production Environment Variables
AWS_ACCESS_KEY_ID
AWS_S3_CUSTOM_DOMAIN
AWS_S3_REGION_NAME
AWS_SECRET_ACCESS_KEY
AWS_STORAGE_BUCKET_NAME
DATABASE_URL
DEBUG
EMAIL_HOST
EMAIL_HOST_PASSWORD
EMAIL_HOST_USER
EMAIL_PORT
EMAIL_USE_TLS
LOG_LEVEL
SECRET_KEY
WEB_CONCURRENCY
"""
