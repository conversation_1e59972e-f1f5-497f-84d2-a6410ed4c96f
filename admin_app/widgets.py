"""
Custom admin dashboard widgets for enhanced admin interface.
Provides statistics, charts, and quick actions for the admin dashboard.
"""

import json
from datetime import datetime, timedelta
from django.contrib.admin import AdminSite
from django.db.models import Count, Sum, Avg
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile
from booking_cart_app.models import Booking, Cart
from venues_app.models import Venue, Service
from admin_app.models import SystemHealthLog


class BaseWidget:
    """Base class for admin dashboard widgets."""
    
    def __init__(self, title, template_name=None, css_classes=None):
        self.title = title
        self.template_name = template_name or 'admin_app/widgets/base_widget.html'
        self.css_classes = css_classes or []
    
    def get_context_data(self):
        """Override this method to provide widget-specific data."""
        return {}
    
    def render(self):
        """Render the widget HTML."""
        context = {
            'widget_title': self.title,
            'widget_classes': ' '.join(self.css_classes),
            **self.get_context_data()
        }
        return render_to_string(self.template_name, context)


class UserStatsWidget(BaseWidget):
    """Widget displaying user statistics."""
    
    def __init__(self):
        super().__init__(
            title=_("User Statistics"),
            template_name='admin_app/widgets/user_stats.html',
            css_classes=['user-stats-widget']
        )
    
    def get_context_data(self):
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        total_users = CustomUser.objects.count()
        active_users = CustomUser.objects.filter(is_active=True).count()
        new_users_week = CustomUser.objects.filter(date_joined__gte=week_ago).count()
        new_users_month = CustomUser.objects.filter(date_joined__gte=month_ago).count()
        
        customers = CustomerProfile.objects.count()
        providers = ServiceProviderProfile.objects.count()
        
        return {
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': total_users - active_users,
            'new_users_week': new_users_week,
            'new_users_month': new_users_month,
            'customers': customers,
            'providers': providers,
            'user_growth_rate': self.calculate_growth_rate(new_users_month, month_ago),
        }
    
    def calculate_growth_rate(self, new_users, period_start):
        """Calculate user growth rate."""
        previous_period_users = CustomUser.objects.filter(
            date_joined__lt=period_start,
            date_joined__gte=period_start - timedelta(days=30)
        ).count()
        
        if previous_period_users == 0:
            return 100 if new_users > 0 else 0
        
        return ((new_users - previous_period_users) / previous_period_users) * 100


class VenueStatsWidget(BaseWidget):
    """Widget displaying venue and service statistics."""
    
    def __init__(self):
        super().__init__(
            title=_("Venue & Service Statistics"),
            template_name='admin_app/widgets/venue_stats.html',
            css_classes=['venue-stats-widget']
        )
    
    def get_context_data(self):
        total_venues = Venue.objects.count()
        active_venues = Venue.objects.filter(is_active=True).count()
        featured_venues = Venue.objects.filter(is_featured=True).count()
        
        total_services = Service.objects.count()
        active_services = Service.objects.filter(is_active=True).count()
        
        # Top venue categories
        top_categories = Venue.objects.values(
            'venue_type__name'
        ).annotate(
            count=Count('id')
        ).order_by('-count')[:5]
        
        return {
            'total_venues': total_venues,
            'active_venues': active_venues,
            'inactive_venues': total_venues - active_venues,
            'featured_venues': featured_venues,
            'total_services': total_services,
            'active_services': active_services,
            'top_categories': list(top_categories),
        }


class BookingStatsWidget(BaseWidget):
    """Widget displaying booking and revenue statistics."""
    
    def __init__(self):
        super().__init__(
            title=_("Booking & Revenue Statistics"),
            template_name='admin_app/widgets/booking_stats.html',
            css_classes=['booking-stats-widget']
        )
    
    def get_context_data(self):
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        total_bookings = Booking.objects.count()
        confirmed_bookings = Booking.objects.filter(status='confirmed').count()
        pending_bookings = Booking.objects.filter(status='pending').count()
        cancelled_bookings = Booking.objects.filter(status='cancelled').count()
        
        recent_bookings = Booking.objects.filter(booking_date__gte=week_ago).count()
        monthly_bookings = Booking.objects.filter(booking_date__gte=month_ago).count()
        
        # Revenue calculations
        total_revenue = Booking.objects.filter(
            status='confirmed'
        ).aggregate(Sum('total_price'))['total_price__sum'] or 0
        
        monthly_revenue = Booking.objects.filter(
            status='confirmed',
            booking_date__gte=month_ago
        ).aggregate(Sum('total_price'))['total_price__sum'] or 0
        
        avg_booking_value = Booking.objects.filter(
            status='confirmed'
        ).aggregate(Avg('total_price'))['total_price__avg'] or 0
        
        # Active carts
        active_carts = Cart.objects.filter(
            expires_at__gt=timezone.now()
        ).count()
        
        return {
            'total_bookings': total_bookings,
            'confirmed_bookings': confirmed_bookings,
            'pending_bookings': pending_bookings,
            'cancelled_bookings': cancelled_bookings,
            'recent_bookings': recent_bookings,
            'monthly_bookings': monthly_bookings,
            'total_revenue': total_revenue,
            'monthly_revenue': monthly_revenue,
            'avg_booking_value': avg_booking_value,
            'active_carts': active_carts,
            'conversion_rate': self.calculate_conversion_rate(),
        }
    
    def calculate_conversion_rate(self):
        """Calculate booking conversion rate."""
        total_carts = Cart.objects.count()
        completed_bookings = Booking.objects.filter(status='confirmed').count()
        
        if total_carts == 0:
            return 0
        
        return (completed_bookings / total_carts) * 100


class SystemHealthWidget(BaseWidget):
    """Widget displaying system health and monitoring information."""
    
    def __init__(self):
        super().__init__(
            title=_("System Health"),
            template_name='admin_app/widgets/system_health.html',
            css_classes=['system-health-widget']
        )
    
    def get_context_data(self):
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        
        # Recent system events
        recent_errors = SystemHealthLog.objects.filter(
            severity='error',
            timestamp__gte=week_ago
        ).count()
        
        recent_warnings = SystemHealthLog.objects.filter(
            severity='warning',
            timestamp__gte=week_ago
        ).count()
        
        recent_info = SystemHealthLog.objects.filter(
            severity='info',
            timestamp__gte=week_ago
        ).count()
        
        # Latest events
        latest_events = SystemHealthLog.objects.order_by('-timestamp')[:5]
        
        # System status
        system_status = 'healthy'
        if recent_errors > 10:
            system_status = 'critical'
        elif recent_errors > 5 or recent_warnings > 20:
            system_status = 'warning'
        
        return {
            'recent_errors': recent_errors,
            'recent_warnings': recent_warnings,
            'recent_info': recent_info,
            'latest_events': latest_events,
            'system_status': system_status,
            'total_events': recent_errors + recent_warnings + recent_info,
        }


class QuickActionsWidget(BaseWidget):
    """Widget providing quick action links for common admin tasks."""
    
    def __init__(self):
        super().__init__(
            title=_("Quick Actions"),
            template_name='admin_app/widgets/quick_actions.html',
            css_classes=['quick-actions-widget']
        )
    
    def get_context_data(self):
        actions = [
            {
                'title': _('Add New Venue'),
                'url': reverse('admin:venues_app_venue_add'),
                'icon': 'fas fa-plus-circle',
                'color': 'primary'
            },
            {
                'title': _('View Pending Bookings'),
                'url': reverse('admin:booking_cart_app_booking_changelist') + '?status=pending',
                'icon': 'fas fa-clock',
                'color': 'warning'
            },
            {
                'title': _('User Management'),
                'url': reverse('admin:accounts_app_customuser_changelist'),
                'icon': 'fas fa-users',
                'color': 'info'
            },
            {
                'title': _('System Health'),
                'url': reverse('admin:admin_app_systemhealthlog_changelist'),
                'icon': 'fas fa-heartbeat',
                'color': 'success'
            },
            {
                'title': _('Export Data'),
                'url': '/admin-panel/export/',
                'icon': 'fas fa-download',
                'color': 'secondary'
            },
            {
                'title': _('View Analytics'),
                'url': '/admin-panel/analytics/',
                'icon': 'fas fa-chart-bar',
                'color': 'primary'
            },
        ]
        
        return {'actions': actions}


class RecentActivityWidget(BaseWidget):
    """Widget showing recent admin activity."""
    
    def __init__(self):
        super().__init__(
            title=_("Recent Activity"),
            template_name='admin_app/widgets/recent_activity.html',
            css_classes=['recent-activity-widget']
        )
    
    def get_context_data(self):
        # Get recent bookings
        recent_bookings = Booking.objects.select_related(
            'customer', 'venue'
        ).order_by('-booking_date')[:5]
        
        # Get recent user registrations
        recent_users = CustomUser.objects.order_by('-date_joined')[:5]
        
        # Get recent venues
        recent_venues = Venue.objects.select_related(
            'service_provider'
        ).order_by('-created_at')[:5]
        
        return {
            'recent_bookings': recent_bookings,
            'recent_users': recent_users,
            'recent_venues': recent_venues,
        }


class ChartWidget(BaseWidget):
    """Base widget for chart displays."""
    
    def __init__(self, title, chart_type='line', chart_id=None):
        self.chart_type = chart_type
        self.chart_id = chart_id or f"chart_{title.lower().replace(' ', '_')}"
        
        super().__init__(
            title=title,
            template_name='admin_app/widgets/chart_widget.html',
            css_classes=['chart-widget']
        )
    
    def get_chart_data(self):
        """Override this method to provide chart data."""
        return {
            'labels': [],
            'datasets': []
        }
    
    def get_context_data(self):
        return {
            'chart_type': self.chart_type,
            'chart_id': self.chart_id,
            'chart_data': json.dumps(self.get_chart_data()),
        }


class BookingTrendsChart(ChartWidget):
    """Chart widget showing booking trends over time."""
    
    def __init__(self):
        super().__init__(
            title=_("Booking Trends (Last 30 Days)"),
            chart_type='line',
            chart_id='booking_trends_chart'
        )
    
    def get_chart_data(self):
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        # Get daily booking counts
        bookings = Booking.objects.filter(
            booking_date__range=[start_date, end_date]
        ).extra(
            select={'day': 'date(booking_date)'}
        ).values('day').annotate(
            count=Count('id')
        ).order_by('day')
        
        labels = []
        data = []
        
        # Fill in missing days with 0
        current_date = start_date
        booking_dict = {item['day']: item['count'] for item in bookings}
        
        while current_date <= end_date:
            labels.append(current_date.strftime('%m/%d'))
            data.append(booking_dict.get(current_date, 0))
            current_date += timedelta(days=1)
        
        return {
            'labels': labels,
            'datasets': [{
                'label': _('Bookings'),
                'data': data,
                'borderColor': '#42241A',
                'backgroundColor': 'rgba(66, 36, 26, 0.1)',
                'borderWidth': 2,
                'fill': True,
                'tension': 0.4
            }]
        }


class RevenueChart(ChartWidget):
    """Chart widget showing revenue trends."""
    
    def __init__(self):
        super().__init__(
            title=_("Revenue Trends (Last 30 Days)"),
            chart_type='bar',
            chart_id='revenue_chart'
        )
    
    def get_chart_data(self):
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        # Get daily revenue
        revenue = Booking.objects.filter(
            booking_date__range=[start_date, end_date],
            status='confirmed'
        ).extra(
            select={'day': 'date(booking_date)'}
        ).values('day').annotate(
            revenue=Sum('total_price')
        ).order_by('day')
        
        labels = []
        data = []
        
        # Fill in missing days with 0
        current_date = start_date
        revenue_dict = {item['day']: float(item['revenue'] or 0) for item in revenue}
        
        while current_date <= end_date:
            labels.append(current_date.strftime('%m/%d'))
            data.append(revenue_dict.get(current_date, 0))
            current_date += timedelta(days=1)
        
        return {
            'labels': labels,
            'datasets': [{
                'label': _('Revenue ($)'),
                'data': data,
                'backgroundColor': '#42241A',
                'borderColor': '#42241A',
                'borderWidth': 1
            }]
        }


# Widget registry for easy access
DASHBOARD_WIDGETS = {
    'user_stats': UserStatsWidget,
    'venue_stats': VenueStatsWidget,
    'booking_stats': BookingStatsWidget,
    'system_health': SystemHealthWidget,
    'quick_actions': QuickActionsWidget,
    'recent_activity': RecentActivityWidget,
    'booking_trends_chart': BookingTrendsChart,
    'revenue_chart': RevenueChart,
}


def get_widget(widget_name):
    """Get a widget instance by name."""
    widget_class = DASHBOARD_WIDGETS.get(widget_name)
    if widget_class:
        return widget_class()
    return None


def get_all_widgets():
    """Get all available widget instances."""
    return [widget_class() for widget_class in DASHBOARD_WIDGETS.values()]
