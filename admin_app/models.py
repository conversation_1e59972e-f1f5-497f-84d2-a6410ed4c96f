"""Database models for admin-managed content and configuration."""

# --- Standard Library Imports ---
import os
import uuid
from datetime import timedelta

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.core.validators import FileExtensionValidator
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _


def get_static_page_media_path(instance, filename):
    """Generate upload path for static page media files."""
    ext = filename.split(".")[-1]
    filename = f"static_page_{uuid.uuid4().hex}.{ext}"
    return os.path.join("admin_app", "static_pages", filename)


def get_blog_featured_image_path(instance, filename):
    """Generate upload path for blog featured images."""
    ext = filename.split(".")[-1]
    filename = f"blog_{instance.slug}_{uuid.uuid4().hex}.{ext}"
    return os.path.join("admin_app", "blog_images", filename)


def get_homepage_block_image_path(instance, filename):
    """Generate upload path for homepage block images."""
    ext = filename.split(".")[-1]
    filename = f"homepage_{instance.block_type}_{uuid.uuid4().hex}.{ext}"
    return os.path.join("admin_app", "homepage_blocks", filename)


def get_media_file_path(instance, filename):
    """Generate upload path for general media files."""
    ext = filename.split(".")[-1]
    filename = f"media_{uuid.uuid4().hex}.{ext}"
    return os.path.join("admin_app", "media_files", filename)


class StaticPage(models.Model):
    """Model for managing static pages like About, Terms, Privacy Policy, etc."""

    STATUS_CHOICES = [
        ("draft", _("Draft")),
        ("published", _("Published")),
        ("archived", _("Archived")),
    ]

    title = models.CharField(
        max_length=200, help_text=_("Page title displayed in browser and navigation")
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text=_("URL-friendly version of the title (auto-generated)"),
    )
    content = models.TextField(
        help_text=_("Rich content with support for HTML, images, and videos")
    )
    meta_title = models.CharField(
        max_length=60,
        blank=True,
        help_text=_("SEO title tag (max 60 characters, auto-generated if empty)"),
    )
    meta_description = models.CharField(
        max_length=160,
        blank=True,
        help_text=_(
            "SEO meta description (max 160 characters, auto-generated if empty)"
        ),
    )
    meta_keywords = models.CharField(
        max_length=255, blank=True, help_text=_("SEO keywords, comma-separated")
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default="draft",
        help_text=_("Publication status of the page"),
    )
    featured_image = models.ImageField(
        upload_to=get_static_page_media_path,
        blank=True,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=["jpg", "jpeg", "png"])],
        help_text=_("Featured image for the page (JPG/PNG only, max 5MB)"),
    )
    is_featured = models.BooleanField(
        default=False, help_text=_("Display this page prominently in navigation")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_static_pages",
    )
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="updated_static_pages",
    )

    class Meta:
        ordering = ["title"]
        verbose_name = _("Static Page")
        verbose_name_plural = _("Static Pages")

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        """Return the URL for this static page."""
        return reverse("admin_app:static_page_edit", kwargs={"slug": self.slug})

    def save(self, *args, **kwargs):
        """Auto-generate slug and SEO metadata if not provided."""
        if not self.slug:
            self.slug = slugify(self.title)

        # Auto-generate meta title if not provided
        if not self.meta_title:
            self.meta_title = self.title[:60]

        # Auto-generate meta description if not provided
        if not self.meta_description and self.content:
            # Extract first 160 characters from content, removing HTML tags
            import re

            clean_content = re.sub(r"<[^>]+>", "", self.content)
            self.meta_description = clean_content[:160].strip()

        super().save(*args, **kwargs)


class BlogCategory(models.Model):
    """Model for blog post categories with flat structure."""

    name = models.CharField(max_length=100, unique=True, help_text=_("Category name"))
    slug = models.SlugField(
        max_length=100,
        unique=True,
        help_text=_("URL-friendly version of the name (auto-generated)"),
    )
    description = models.TextField(
        blank=True, help_text=_("Optional description of the category")
    )
    is_active = models.BooleanField(
        default=True, help_text=_("Whether this category is available for new posts")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Blog Category")
        verbose_name_plural = _("Blog Categories")
        ordering = ["name"]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        """Return the URL for this blog category."""
        return reverse("admin_app:blog_category_list")

    def save(self, *args, **kwargs):
        """Auto-generate slug if not provided."""
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    @property
    def post_count(self):
        """Return the number of published posts in this category."""
        return self.blog_posts.filter(status="published").count()


class BlogPost(models.Model):
    """Model for blog posts with rich content and media support."""

    STATUS_CHOICES = [
        ("draft", _("Draft")),
        ("published", _("Published")),
        ("archived", _("Archived")),
    ]

    title = models.CharField(max_length=200, help_text=_("Blog post title"))
    slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text=_("URL-friendly version of the title (auto-generated)"),
    )
    content = models.TextField(
        help_text=_("Rich content with support for HTML, images, and videos")
    )
    excerpt = models.TextField(
        blank=True,
        max_length=300,
        help_text=_("Short description for previews (max 300 characters)"),
    )
    category = models.ForeignKey(
        BlogCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="blog_posts",
        help_text=_("Blog post category"),
    )
    featured_image = models.ImageField(
        upload_to=get_blog_featured_image_path,
        blank=True,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=["jpg", "jpeg", "png"])],
        help_text=_("Featured image for the blog post (JPG/PNG only, max 5MB)"),
    )
    meta_title = models.CharField(
        max_length=60,
        blank=True,
        help_text=_("SEO title tag (max 60 characters, auto-generated if empty)"),
    )
    meta_description = models.CharField(
        max_length=160,
        blank=True,
        help_text=_(
            "SEO meta description (max 160 characters, auto-generated if empty)"
        ),
    )
    meta_keywords = models.CharField(
        max_length=255, blank=True, help_text=_("SEO keywords, comma-separated")
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default="draft",
        help_text=_("Publication status of the post"),
    )
    is_featured = models.BooleanField(
        default=False, help_text=_("Display this post prominently on the blog page")
    )
    published_at = models.DateTimeField(
        null=True, blank=True, help_text=_("Date and time when the post was published")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="authored_blog_posts",
    )
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="updated_blog_posts",
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = _("Blog Post")
        verbose_name_plural = _("Blog Posts")

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        """Return the URL for this blog post."""
        return reverse("admin_app:blog_post_edit", kwargs={"slug": self.slug})

    def save(self, *args, **kwargs):
        """Auto-generate slug, SEO metadata, and set published date."""
        if not self.slug:
            self.slug = slugify(self.title)

        # Auto-generate meta title if not provided
        if not self.meta_title:
            self.meta_title = self.title[:60]

        # Auto-generate meta description if not provided
        if not self.meta_description:
            if self.excerpt:
                self.meta_description = self.excerpt[:160]
            elif self.content:
                # Extract first 160 characters from content, removing HTML tags
                import re

                clean_content = re.sub(r"<[^>]+>", "", self.content)
                self.meta_description = clean_content[:160].strip()

        # Set published_at when status changes to published
        if self.status == "published" and not self.published_at:
            self.published_at = timezone.now()
        elif self.status != "published":
            self.published_at = None

        super().save(*args, **kwargs)


class HomepageBlock(models.Model):
    """Model for managing editable homepage sections and blocks."""

    BLOCK_TYPE_CHOICES = [
        ("hero", _("Hero Section")),
        ("how_it_works", _("How It Works")),
        ("top_deals", _("Top Deals")),
        ("testimonials", _("Testimonials")),
        ("features", _("Features")),
        ("call_to_action", _("Call to Action")),
        ("custom", _("Custom Block")),
    ]

    block_type = models.CharField(
        max_length=50,
        choices=BLOCK_TYPE_CHOICES,
        unique=True,
        help_text=_("Type of homepage block"),
    )
    title = models.CharField(max_length=200, help_text=_("Block title/heading"))
    subtitle = models.CharField(
        max_length=300, blank=True, help_text=_("Optional subtitle or description")
    )
    content = models.TextField(blank=True, help_text=_("Rich content for the block"))
    image = models.ImageField(
        upload_to=get_homepage_block_image_path,
        blank=True,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=["jpg", "jpeg", "png"])],
        help_text=_("Block image (JPG/PNG only, max 5MB)"),
    )
    button_text = models.CharField(
        max_length=100, blank=True, help_text=_("Text for call-to-action button")
    )
    button_url = models.URLField(
        blank=True, help_text=_("URL for call-to-action button")
    )
    is_active = models.BooleanField(
        default=True, help_text=_("Whether this block is displayed on the homepage")
    )
    display_order = models.PositiveIntegerField(
        default=0, help_text=_("Order in which blocks appear on the homepage")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="updated_homepage_blocks",
    )

    class Meta:
        ordering = ["display_order", "block_type"]
        verbose_name = _("Homepage Block")
        verbose_name_plural = _("Homepage Blocks")

    def __str__(self):
        return f"{self.get_block_type_display()} - {self.title}"


class MediaFile(models.Model):
    """Model for managing uploaded media files in the admin media library."""

    FILE_TYPE_CHOICES = [
        ("image", _("Image")),
        ("document", _("Document")),
        ("video", _("Video")),
        ("other", _("Other")),
    ]

    title = models.CharField(
        max_length=200, help_text=_("Descriptive title for the media file")
    )
    file = models.FileField(
        upload_to=get_media_file_path,
        help_text=_("Upload media file (images, documents, videos)"),
    )
    file_type = models.CharField(
        max_length=20,
        choices=FILE_TYPE_CHOICES,
        default="image",
        help_text=_("Type of media file"),
    )
    description = models.TextField(
        blank=True, help_text=_("Optional description of the media file")
    )
    alt_text = models.CharField(
        max_length=255,
        blank=True,
        help_text=_("Alternative text for images (accessibility)"),
    )
    tags = models.CharField(
        max_length=500,
        blank=True,
        help_text=_("Comma-separated tags for organizing media files"),
    )
    is_public = models.BooleanField(
        default=True, help_text=_("Whether this file can be accessed publicly")
    )
    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="uploaded_media_files",
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = _("Media File")
        verbose_name_plural = _("Media Files")

    def __str__(self):
        return self.title

    @property
    def file_url(self):
        """Return the URL of the uploaded file."""
        return self.file.url if self.file else None

    @property
    def file_size(self):
        """Return the size of the uploaded file in bytes."""
        if self.file and hasattr(self.file, "size"):
            return self.file.size
        return 0

    @property
    def file_size_formatted(self):
        """Return the file size in a human-readable format."""
        size = self.file_size
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"

    @property
    def file_extension(self):
        """Return the file extension."""
        if self.file:
            return os.path.splitext(self.file.name)[1].lower()
        return ""

    def save(self, *args, **kwargs):
        """Auto-detect file type based on extension."""
        if self.file:
            ext = self.file_extension
            if ext in [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"]:
                self.file_type = "image"
            elif ext in [".pdf", ".doc", ".docx", ".txt", ".rtf"]:
                self.file_type = "document"
            elif ext in [".mp4", ".avi", ".mov", ".wmv", ".flv"]:
                self.file_type = "video"
            else:
                self.file_type = "other"

        super().save(*args, **kwargs)


class BulkActionLog(models.Model):
    """Model for tracking bulk administrative actions performed on users and content."""

    ACTION_TYPE_CHOICES = [
        ("user_activation", _("User Activation")),
        ("user_deactivation", _("User Deactivation")),
        ("user_deletion", _("User Deletion")),
        ("provider_approval", _("Provider Approval")),
        ("provider_rejection", _("Provider Rejection")),
        ("content_publish", _("Content Publishing")),
        ("content_unpublish", _("Content Unpublishing")),
        ("content_deletion", _("Content Deletion")),
        ("other", _("Other Action")),
    ]

    action_type = models.CharField(
        max_length=50,
        choices=ACTION_TYPE_CHOICES,
        help_text=_("Type of bulk action performed"),
    )
    description = models.TextField(
        help_text=_("Detailed description of the action performed")
    )
    affected_count = models.PositiveIntegerField(
        default=0, help_text=_("Number of items affected by this action")
    )
    affected_model = models.CharField(
        max_length=100,
        blank=True,
        help_text=_("Model name of affected items (e.g., CustomUser, BlogPost)"),
    )
    affected_ids = models.TextField(
        blank=True, help_text=_("Comma-separated list of affected item IDs")
    )
    executed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="executed_bulk_actions",
        help_text=_("Admin user who executed this action"),
    )
    executed_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text=_("IP address from which the action was executed"),
    )
    user_agent = models.TextField(blank=True, help_text=_("Browser user agent string"))

    class Meta:
        ordering = ["-executed_at"]
        verbose_name = _("Bulk Action Log")
        verbose_name_plural = _("Bulk Action Logs")

    def __str__(self):
        return f"{self.get_action_type_display()} - {self.affected_count} items - {self.executed_at}"

    def get_affected_ids_list(self):
        """Return the affected IDs as a list."""
        if self.affected_ids:
            return [id.strip() for id in self.affected_ids.split(",") if id.strip()]
        return []

    def set_affected_ids_list(self, ids_list):
        """Set the affected IDs from a list."""
        self.affected_ids = ",".join(str(id) for id in ids_list)
        self.affected_count = len(ids_list)


class SystemHealthLog(models.Model):
    """Model for tracking system health events and monitoring."""

    EVENT_TYPE_CHOICES = [
        ("error", _("Error")),
        ("warning", _("Warning")),
        ("info", _("Information")),
        ("login_attempt", _("Login Attempt")),
        ("failed_login", _("Failed Login")),
        ("security_alert", _("Security Alert")),
        ("uptime", _("System Uptime")),
        ("downtime", _("System Downtime")),
        ("performance", _("Performance Issue")),
        ("maintenance", _("Maintenance")),
    ]

    SEVERITY_CHOICES = [
        ("low", _("Low")),
        ("medium", _("Medium")),
        ("high", _("High")),
        ("critical", _("Critical")),
    ]

    event_type = models.CharField(
        max_length=50, choices=EVENT_TYPE_CHOICES, help_text=_("Type of system event")
    )
    severity = models.CharField(
        max_length=20,
        choices=SEVERITY_CHOICES,
        default="low",
        help_text=_("Severity level of the event"),
    )
    title = models.CharField(
        max_length=200, help_text=_("Brief title describing the event")
    )
    description = models.TextField(help_text=_("Detailed description of the event"))
    affected_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="system_health_events",
        help_text=_("User affected by this event (if applicable)"),
    )
    ip_address = models.GenericIPAddressField(
        null=True, blank=True, help_text=_("IP address related to the event")
    )
    user_agent = models.TextField(blank=True, help_text=_("Browser user agent string"))
    additional_data = models.JSONField(
        default=dict, blank=True, help_text=_("Additional event data in JSON format")
    )
    is_resolved = models.BooleanField(
        default=False, help_text=_("Whether this event has been resolved")
    )
    resolved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="resolved_system_events",
        help_text=_("Admin who resolved this event"),
    )
    resolved_at = models.DateTimeField(
        null=True, blank=True, help_text=_("When this event was resolved")
    )
    resolution_notes = models.TextField(
        blank=True, help_text=_("Notes about how the event was resolved")
    )
    recorded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-recorded_at"]
        verbose_name = _("System Health Log")
        verbose_name_plural = _("System Health Logs")

    def __str__(self):
        return f"{self.get_event_type_display()} - {self.title} - {self.recorded_at}"

    def resolve(self, admin_user, notes=""):
        """Mark this event as resolved."""
        self.is_resolved = True
        self.resolved_by = admin_user
        self.resolved_at = timezone.now()
        self.resolution_notes = notes
        self.save()
        return True


class BulkActionLog(models.Model):
    """Model to track bulk operations performed in admin interface."""

    ACTION_TYPE_CHOICES = [
        ('bulk_activate', _('Bulk Activate')),
        ('bulk_deactivate', _('Bulk Deactivate')),
        ('bulk_export_csv', _('Bulk Export CSV')),
        ('bulk_validate_data', _('Bulk Validate Data')),
        ('bulk_send_welcome_email', _('Bulk Send Welcome Email')),
        ('bulk_reset_passwords', _('Bulk Reset Passwords')),
        ('bulk_verify_emails', _('Bulk Verify Emails')),
        ('bulk_feature_venues', _('Bulk Feature Venues')),
        ('bulk_unfeature_venues', _('Bulk Unfeature Venues')),
        ('bulk_update_pricing', _('Bulk Update Pricing')),
        ('bulk_confirm_bookings', _('Bulk Confirm Bookings')),
        ('bulk_cancel_bookings', _('Bulk Cancel Bookings')),
        ('bulk_send_reminders', _('Bulk Send Reminders')),
    ]

    admin_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='bulk_actions_performed',
        help_text=_("Admin user who performed the bulk action")
    )
    action_type = models.CharField(
        max_length=50,
        choices=ACTION_TYPE_CHOICES,
        help_text=_("Type of bulk action performed")
    )
    model_name = models.CharField(
        max_length=100,
        help_text=_("Name of the model affected by the bulk action")
    )
    affected_count = models.PositiveIntegerField(
        help_text=_("Number of objects affected by the bulk action")
    )
    timestamp = models.DateTimeField(
        auto_now_add=True,
        help_text=_("When the bulk action was performed")
    )
    details = models.JSONField(
        default=dict,
        blank=True,
        help_text=_("Additional details about the bulk action in JSON format")
    )
    success = models.BooleanField(
        default=True,
        help_text=_("Whether the bulk action completed successfully")
    )
    error_message = models.TextField(
        blank=True,
        help_text=_("Error message if the bulk action failed")
    )

    class Meta:
        ordering = ['-timestamp']
        verbose_name = _('Bulk Action Log')
        verbose_name_plural = _('Bulk Action Logs')

    def __str__(self):
        return f"{self.get_action_type_display()} on {self.affected_count} {self.model_name}(s) by {self.admin_user.username}"


class SiteConfiguration(models.Model):
    """Model for site-wide configuration and settings."""

    # Site Information
    site_name = models.CharField(
        max_length=100, default="CozyWish", help_text=_("Name of the website")
    )
    site_tagline = models.CharField(
        max_length=200, blank=True, help_text=_("Short tagline or slogan for the site")
    )
    site_description = models.TextField(
        blank=True, help_text=_("Description of the website for SEO")
    )

    # Contact Information
    contact_email = models.EmailField(
        default="<EMAIL>", help_text=_("Primary contact email address")
    )
    contact_phone = models.CharField(
        max_length=20, blank=True, help_text=_("Primary contact phone number")
    )
    contact_address = models.TextField(
        blank=True, help_text=_("Physical address of the business")
    )

    # Social Media Links
    facebook_url = models.URLField(blank=True, help_text=_("Facebook page URL"))
    twitter_url = models.URLField(blank=True, help_text=_("Twitter profile URL"))
    instagram_url = models.URLField(blank=True, help_text=_("Instagram profile URL"))
    linkedin_url = models.URLField(blank=True, help_text=_("LinkedIn profile URL"))

    # SEO Settings
    default_meta_title = models.CharField(
        max_length=60,
        blank=True,
        help_text=_("Default meta title for pages (max 60 characters)"),
    )
    default_meta_description = models.CharField(
        max_length=160,
        blank=True,
        help_text=_("Default meta description for pages (max 160 characters)"),
    )
    default_meta_keywords = models.CharField(
        max_length=255,
        blank=True,
        help_text=_("Default meta keywords, comma-separated"),
    )

    # Analytics and Tracking
    google_analytics_id = models.CharField(
        max_length=50,
        blank=True,
        help_text=_("Google Analytics tracking ID (e.g., GA-XXXXXXXXX-X)"),
    )
    facebook_pixel_id = models.CharField(
        max_length=50, blank=True, help_text=_("Facebook Pixel ID for tracking")
    )

    # Maintenance Mode
    maintenance_mode = models.BooleanField(
        default=False,
        help_text=_("Enable maintenance mode to show maintenance page to visitors"),
    )
    maintenance_message = models.TextField(
        blank=True, help_text=_("Message to display during maintenance mode")
    )

    # System Settings
    allow_user_registration = models.BooleanField(
        default=True, help_text=_("Allow new users to register on the site")
    )
    require_email_verification = models.BooleanField(
        default=True, help_text=_("Require email verification for new user accounts")
    )

    # Metadata
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="site_config_updates",
    )

    class Meta:
        verbose_name = _("Site Configuration")
        verbose_name_plural = _("Site Configuration")

    def __str__(self):
        return f"{self.site_name} Configuration"

    def save(self, *args, **kwargs):
        """Ensure only one instance exists (singleton pattern)."""
        self.__class__.objects.exclude(pk=self.pk).delete()
        super().save(*args, **kwargs)

    @classmethod
    def get_instance(cls):
        """Get or create the singleton instance."""
        instance, created = cls.objects.get_or_create(pk=1)
        return instance


class Announcement(models.Model):
    """Model for site-wide announcements and banners."""

    TYPE_CHOICES = [
        ("info", _("Information")),
        ("success", _("Success")),
        ("warning", _("Warning")),
        ("danger", _("Danger")),
        ("promotion", _("Promotion")),
    ]

    DISPLAY_LOCATION_CHOICES = [
        ("top_banner", _("Top Banner")),
        ("homepage", _("Homepage")),
        ("dashboard", _("Dashboard")),
        ("all_pages", _("All Pages")),
    ]

    title = models.CharField(
        max_length=200, unique=True, help_text=_("Announcement title")
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        blank=True,
        help_text=_("URL-friendly version of the title (auto-generated)"),
    )
    content = models.TextField(help_text=_("Announcement content/message"))
    announcement_type = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        default="info",
        help_text=_("Type of announcement (affects styling)"),
    )
    display_location = models.CharField(
        max_length=20,
        choices=DISPLAY_LOCATION_CHOICES,
        default="top_banner",
        help_text=_("Where to display this announcement"),
    )
    is_active = models.BooleanField(
        default=True, help_text=_("Whether this announcement is currently active")
    )
    is_dismissible = models.BooleanField(
        default=True, help_text=_("Whether users can dismiss this announcement")
    )
    start_date = models.DateTimeField(
        default=timezone.now, help_text=_("When to start displaying this announcement")
    )
    end_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_("When to stop displaying this announcement (optional)"),
    )
    target_user_roles = models.CharField(
        max_length=100,
        blank=True,
        help_text=_("Comma-separated user roles to show this to (empty = all users)"),
    )
    priority = models.PositiveIntegerField(
        default=0, help_text=_("Display priority (higher numbers shown first)")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_announcements",
    )
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="updated_announcements",
    )

    class Meta:
        ordering = ["-priority", "-created_at"]
        verbose_name = _("Announcement")
        verbose_name_plural = _("Announcements")

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        """Return the URL for this announcement."""
        return reverse("admin_app:announcement_detail", kwargs={"slug": self.slug})

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    @property
    def is_current(self):
        """Check if the announcement is currently active and within date range."""
        if not self.is_active:
            return False

        now = timezone.now()
        if now < self.start_date:
            return False

        if self.end_date and now > self.end_date:
            return False

        return True

    def is_visible_to_user(self, user):
        """Check if this announcement should be visible to the given user."""
        if not self.is_current:
            return False

        # If no target roles specified, show to all users
        if not self.target_user_roles:
            return True

        # Check if user's role is in target roles
        target_roles = [role.strip() for role in self.target_user_roles.split(",")]
        user_role = getattr(user, "role", None)

        return user_role in target_roles
