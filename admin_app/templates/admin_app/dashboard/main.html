{% extends 'admin_app/base.html' %}
{% load i18n static %}

{% block title %}Admin Dashboard{% endblock %}

{% block extrahead %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<link rel="stylesheet" href="{% static 'admin_app/css/dashboard-widgets.css' %}">
{% endblock %}

{% block admin_content %}
<div class="modern-dashboard">
    <div class="dashboard-header">
        <h1>{% trans "Admin Dashboard" %}</h1>
        <p>{% trans "Welcome to the CozyWish admin panel. Here's your business overview." %}</p>
    </div>

    {% if site_config.maintenance_mode %}
    <div class="alert alert-warning mb-4">
        <i class="fas fa-exclamation-triangle"></i>
        {{ site_config.maintenance_message|default:"The site is currently under maintenance." }}
    </div>
    {% endif %}
    <!-- Dashboard Widgets Grid -->
    <div class="dashboard-widgets-grid">
        <!-- Row 1: Statistics Widgets -->
        <div class="widget-row">
            <div class="widget-col">
                {{ dashboard_widgets.user_stats.render|safe }}
            </div>
            <div class="widget-col">
                {{ dashboard_widgets.venue_stats.render|safe }}
            </div>
            <div class="widget-col">
                {{ dashboard_widgets.booking_stats.render|safe }}
            </div>
            <div class="widget-col">
                {{ dashboard_widgets.system_health.render|safe }}
            </div>
        </div>

        <!-- Row 2: Charts -->
        <div class="widget-row">
            <div class="widget-col-large">
                {{ dashboard_widgets.booking_trends_chart.render|safe }}
            </div>
            <div class="widget-col-large">
                {{ dashboard_widgets.revenue_chart.render|safe }}
            </div>
        </div>

        <!-- Row 3: Actions and Activity -->
        <div class="widget-row">
            <div class="widget-col">
                {{ dashboard_widgets.quick_actions.render|safe }}
            </div>
            <div class="widget-col-large">
                {{ dashboard_widgets.recent_activity.render|safe }}
            </div>
        </div>
    </div>

    {% if recent_events %}
    <!-- Legacy System Events (can be removed once system_health widget is working) -->
    <div class="legacy-events" style="display: none;">
        <h2 class="h5">Recent System Events</h2>
        <ul class="list-group">
            {% for event in recent_events %}
            <li class="list-group-item">
                <strong>{{ event.event_type|title }}:</strong> {{ event.title }}
                <br><small class="text-muted">{{ event.timestamp }}</small>
            </li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
</div>

<style>
.modern-dashboard {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.dashboard-header {
    background: linear-gradient(135deg, #42241A 0%, #6B3E2A 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 600;
}

.dashboard-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.dashboard-widgets-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.widget-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    align-items: start;
}

.widget-col {
    min-width: 300px;
}

.widget-col-large {
    min-width: 400px;
}

.admin-widget {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #42241A;
    transition: transform 0.2s ease;
}

.admin-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.widget-header h3 {
    margin: 0 0 20px 0;
    color: #42241A;
    font-size: 1.25rem;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 2px solid #f1f2f6;
}

.widget-content {
    color: #2c3e50;
}

@media (max-width: 768px) {
    .widget-row {
        grid-template-columns: 1fr;
    }

    .widget-col,
    .widget-col-large {
        min-width: auto;
    }

    .dashboard-header {
        padding: 20px;
    }

    .dashboard-header h1 {
        font-size: 2rem;
    }
}
</style>
{% endblock %}
