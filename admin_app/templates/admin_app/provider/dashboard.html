{% extends "admin/base_site.html" %}
{% load i18n static admin_urls %}

{% block title %}{% trans "Provider Dashboard" %} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'admin/css/dashboard.css' %}">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .provider-dashboard {
        padding: 20px;
        background: #f8f9fa;
    }
    
    .dashboard-header {
        background: linear-gradient(135deg, #42241A 0%, #6B3E2A 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .dashboard-header h1 {
        margin: 0 0 10px 0;
        font-size: 2.5rem;
        font-weight: 600;
    }
    
    .dashboard-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #42241A;
        transition: transform 0.2s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .stat-card h3 {
        margin: 0 0 10px 0;
        color: #42241A;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
    }
    
    .stat-card .value {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
    }
    
    .stat-card .change {
        font-size: 0.85rem;
        margin-top: 5px;
    }
    
    .change.positive {
        color: #27ae60;
    }
    
    .change.negative {
        color: #e74c3c;
    }
    
    .dashboard-section {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .section-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f1f2f6;
    }
    
    .section-header h2 {
        margin: 0;
        color: #42241A;
        font-size: 1.5rem;
        font-weight: 600;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }
    
    .venue-performance-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }
    
    .venue-performance-table th,
    .venue-performance-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }
    
    .venue-performance-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #42241A;
    }
    
    .venue-performance-table tr:hover {
        background: #f8f9fa;
    }
    
    .recent-bookings {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .booking-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        transition: background 0.2s ease;
    }
    
    .booking-item:hover {
        background: #f8f9fa;
    }
    
    .booking-info h4 {
        margin: 0 0 5px 0;
        color: #2c3e50;
        font-size: 1rem;
    }
    
    .booking-info p {
        margin: 0;
        color: #7f8c8d;
        font-size: 0.85rem;
    }
    
    .booking-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-confirmed {
        background: #d4edda;
        color: #155724;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
    }
    
    .status-cancelled {
        background: #f8d7da;
        color: #721c24;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }
    
    .action-btn {
        display: inline-block;
        padding: 12px 20px;
        background: #42241A;
        color: white;
        text-decoration: none;
        border-radius: 8px;
        text-align: center;
        font-weight: 600;
        transition: background 0.2s ease;
    }
    
    .action-btn:hover {
        background: #6B3E2A;
        color: white;
        text-decoration: none;
    }
    
    .action-btn.secondary {
        background: #6c757d;
    }
    
    .action-btn.secondary:hover {
        background: #5a6268;
    }
</style>
{% endblock %}

{% block content %}
<div class="provider-dashboard">
    <div class="dashboard-header">
        <h1>{% trans "Provider Dashboard" %}</h1>
        <p>{% trans "Welcome back" %}{% if provider %}, {{ provider.display_name }}{% endif %}! {% trans "Here's your business overview." %}</p>
    </div>
    
    {% if provider %}
    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <h3>{% trans "Total Venues" %}</h3>
            <div class="value">{{ total_venues }}</div>
            <div class="change positive">{{ active_venues }} {% trans "active" %}</div>
        </div>
        
        <div class="stat-card">
            <h3>{% trans "Total Services" %}</h3>
            <div class="value">{{ total_services }}</div>
        </div>
        
        <div class="stat-card">
            <h3>{% trans "Total Bookings" %}</h3>
            <div class="value">{{ total_bookings }}</div>
        </div>
        
        <div class="stat-card">
            <h3>{% trans "Monthly Revenue" %}</h3>
            <div class="value">${{ revenue_summary.this_month|floatformat:0 }}</div>
            <div class="change positive">{% trans "This month" %}</div>
        </div>
    </div>
    
    <!-- Charts Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2>{% trans "Booking Trends" %}</h2>
        </div>
        <div class="chart-container">
            <canvas id="bookingTrendsChart"></canvas>
        </div>
    </div>
    
    <!-- Venue Performance -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2>{% trans "Venue Performance" %}</h2>
        </div>
        <table class="venue-performance-table">
            <thead>
                <tr>
                    <th>{% trans "Venue" %}</th>
                    <th>{% trans "Bookings" %}</th>
                    <th>{% trans "Revenue" %}</th>
                    <th>{% trans "Avg Rating" %}</th>
                    <th>{% trans "Reviews" %}</th>
                </tr>
            </thead>
            <tbody>
                {% for item in venue_performance %}
                <tr>
                    <td>{{ item.venue.venue_name }}</td>
                    <td>{{ item.total_bookings }}</td>
                    <td>${{ item.revenue|floatformat:2 }}</td>
                    <td>{{ item.avg_rating|floatformat:1 }}/5</td>
                    <td>{{ item.review_count }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5">{% trans "No venues found." %}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Recent Bookings -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2>{% trans "Recent Bookings" %}</h2>
        </div>
        <div class="recent-bookings">
            {% for booking in recent_bookings %}
            <div class="booking-item">
                <div class="booking-info">
                    <h4>{{ booking.venue.venue_name }}</h4>
                    <p>{{ booking.customer.get_full_name }} • {{ booking.booking_date }}</p>
                </div>
                <div>
                    <span class="booking-status status-{{ booking.status }}">{{ booking.get_status_display }}</span>
                </div>
            </div>
            {% empty %}
            <p>{% trans "No recent bookings." %}</p>
            {% endfor %}
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2>{% trans "Quick Actions" %}</h2>
        </div>
        <div class="quick-actions">
            <a href="{% url 'admin:venues_app_venue_changelist' %}" class="action-btn">
                {% trans "Manage Venues" %}
            </a>
            <a href="{% url 'admin:venues_app_service_changelist' %}" class="action-btn">
                {% trans "Manage Services" %}
            </a>
            <a href="{% url 'admin:booking_cart_app_booking_changelist' %}" class="action-btn">
                {% trans "View Bookings" %}
            </a>
            <a href="/admin-panel/provider/analytics/" class="action-btn secondary">
                {% trans "View Analytics" %}
            </a>
        </div>
    </div>
    
    {% else %}
    <div class="dashboard-section">
        <h2>{% trans "Provider Profile Required" %}</h2>
        <p>{% trans "You need to have a service provider profile to access this dashboard." %}</p>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Booking Trends Chart
    const ctx = document.getElementById('bookingTrendsChart').getContext('2d');
    const bookingData = {{ booking_trends|safe }};
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: bookingData.map(item => item.day),
            datasets: [{
                label: '{% trans "Bookings" %}',
                data: bookingData.map(item => item.count),
                borderColor: '#42241A',
                backgroundColor: 'rgba(66, 36, 26, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}
