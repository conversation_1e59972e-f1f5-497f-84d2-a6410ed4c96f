{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
    &rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
    &rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="bulk-action-confirmation">
    <h1>{{ title }}</h1>
    
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>{% trans "Confirmation Required" %}</strong>
        <p>{% trans "Are you sure you want to send welcome emails to the following users?" %}</p>
    </div>

    <div class="selected-objects">
        <h3>{% trans "Selected Users" %} ({{ queryset.count }})</h3>
        <ul class="object-list">
            {% for obj in queryset %}
            <li>
                <strong>{{ obj.username }}</strong>
                {% if obj.email %}
                    - {{ obj.email }}
                {% else %}
                    <span class="text-danger">{% trans "(No email address)" %}</span>
                {% endif %}
            </li>
            {% endfor %}
        </ul>
    </div>

    <form method="post">
        {% csrf_token %}
        {% for obj in queryset %}
            <input type="hidden" name="{{ action_checkbox_name }}" value="{{ obj.pk|unlocalize }}" />
        {% endfor %}
        <input type="hidden" name="action" value="bulk_send_welcome_email" />
        <input type="hidden" name="post" value="yes" />
        
        <div class="submit-row">
            <input type="submit" value="{% trans 'Yes, send welcome emails' %}" class="default" />
            <a href="{% url opts|admin_urlname:'changelist' %}" class="button cancel-link">{% trans 'No, take me back' %}</a>
        </div>
    </form>
</div>

<style>
.bulk-action-confirmation {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert i {
    margin-right: 8px;
}

.selected-objects {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.object-list {
    max-height: 300px;
    overflow-y: auto;
    margin: 0;
    padding: 0;
    list-style: none;
}

.object-list li {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.object-list li:last-child {
    border-bottom: none;
}

.text-danger {
    color: #dc3545;
    font-style: italic;
}

.submit-row {
    padding: 20px 0;
    border-top: 1px solid #ddd;
    text-align: right;
}

.submit-row input[type="submit"] {
    background: #42241A;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    margin-right: 10px;
    cursor: pointer;
}

.submit-row input[type="submit"]:hover {
    background: #6B3E2A;
}

.cancel-link {
    color: #6c757d;
    text-decoration: none;
    padding: 10px 20px;
    border: 1px solid #6c757d;
    border-radius: 4px;
}

.cancel-link:hover {
    background: #6c757d;
    color: white;
    text-decoration: none;
}
</style>
{% endblock %}
