{% extends "admin_app/widgets/base_widget.html" %}
{% load i18n %}

{% block widget_content %}
<div class="quick-actions-grid">
    {% for action in actions %}
    <a href="{{ action.url }}" class="action-link action-{{ action.color }}">
        <div class="action-icon">
            <i class="{{ action.icon }}"></i>
        </div>
        <div class="action-title">{{ action.title }}</div>
    </a>
    {% endfor %}
</div>

<style>
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
}

.action-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 15px;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.action-link:hover {
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-primary {
    background: #42241A;
    color: white;
}

.action-primary:hover {
    background: #6B3E2A;
    color: white;
}

.action-warning {
    background: #ffc107;
    color: #212529;
}

.action-warning:hover {
    background: #e0a800;
    color: #212529;
}

.action-info {
    background: #17a2b8;
    color: white;
}

.action-info:hover {
    background: #138496;
    color: white;
}

.action-success {
    background: #28a745;
    color: white;
}

.action-success:hover {
    background: #218838;
    color: white;
}

.action-secondary {
    background: #6c757d;
    color: white;
}

.action-secondary:hover {
    background: #5a6268;
    color: white;
}

.action-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.action-title {
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    line-height: 1.2;
}
</style>
{% endblock %}
