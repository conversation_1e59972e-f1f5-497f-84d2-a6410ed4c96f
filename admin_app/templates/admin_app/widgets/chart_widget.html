{% extends "admin_app/widgets/base_widget.html" %}
{% load i18n %}

{% block widget_content %}
<div class="chart-container">
    <canvas id="{{ chart_id }}" width="400" height="200"></canvas>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('{{ chart_id }}').getContext('2d');
    const chartData = {{ chart_data|safe }};
    
    new Chart(ctx, {
        type: '{{ chart_type }}',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                }
            },
            elements: {
                point: {
                    radius: 4,
                    hoverRadius: 6
                }
            }
        }
    });
});
</script>

<style>
.chart-container {
    position: relative;
    height: 250px;
    margin: 10px 0;
}

.chart-container canvas {
    max-height: 100%;
}
</style>
{% endblock %}
