{% extends "admin_app/widgets/base_widget.html" %}
{% load i18n %}

{% block widget_content %}
<div class="stats-grid">
    <div class="stat-item">
        <div class="stat-value">{{ total_users }}</div>
        <div class="stat-label">{% trans "Total Users" %}</div>
    </div>
    
    <div class="stat-item">
        <div class="stat-value">{{ active_users }}</div>
        <div class="stat-label">{% trans "Active Users" %}</div>
    </div>
    
    <div class="stat-item">
        <div class="stat-value">{{ customers }}</div>
        <div class="stat-label">{% trans "Customers" %}</div>
    </div>
    
    <div class="stat-item">
        <div class="stat-value">{{ providers }}</div>
        <div class="stat-label">{% trans "Providers" %}</div>
    </div>
</div>

<div class="growth-info">
    <div class="growth-item">
        <span class="growth-label">{% trans "New this week:" %}</span>
        <span class="growth-value">{{ new_users_week }}</span>
    </div>
    <div class="growth-item">
        <span class="growth-label">{% trans "New this month:" %}</span>
        <span class="growth-value">{{ new_users_month }}</span>
    </div>
    <div class="growth-item">
        <span class="growth-label">{% trans "Growth rate:" %}</span>
        <span class="growth-value {% if user_growth_rate >= 0 %}positive{% else %}negative{% endif %}">
            {{ user_growth_rate|floatformat:1 }}%
        </span>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #42241A;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #42241A;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.growth-info {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.growth-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.growth-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.growth-value {
    font-weight: 600;
}

.growth-value.positive {
    color: #28a745;
}

.growth-value.negative {
    color: #dc3545;
}
</style>
{% endblock %}
