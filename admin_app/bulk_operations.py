"""
Advanced bulk operations and data validation tools for admin interfaces.
Provides enhanced bulk actions with validation, logging, and error handling.
"""

import csv
import json
from datetime import datetime, timedelta
from io import StringIO

from django.contrib import messages
from django.contrib.admin import helpers
from django.core.exceptions import ValidationError
from django.core.mail import send_mail
from django.db import transaction
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect
from django.template.response import TemplateResponse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile
from admin_app.models import BulkActionLog
from booking_cart_app.models import Booking, Cart
from venues_app.models import Venue, Service


class BulkOperationMixin:
    """Mixin to add advanced bulk operations to admin classes."""
    
    def get_actions(self, request):
        """Add custom bulk actions to the admin."""
        actions = super().get_actions(request)
        
        # Add common bulk actions
        actions['bulk_activate'] = (self.bulk_activate, 'bulk_activate', _('Activate selected items'))
        actions['bulk_deactivate'] = (self.bulk_deactivate, 'bulk_deactivate', _('Deactivate selected items'))
        actions['bulk_export_csv'] = (self.bulk_export_csv, 'bulk_export_csv', _('Export selected to CSV'))
        actions['bulk_validate_data'] = (self.bulk_validate_data, 'bulk_validate_data', _('Validate selected data'))
        
        return actions
    
    def bulk_activate(self, request, queryset):
        """Bulk activate selected items."""
        if not hasattr(queryset.model, 'is_active'):
            self.message_user(request, _('This model does not support activation.'), messages.ERROR)
            return
        
        try:
            with transaction.atomic():
                updated = queryset.update(is_active=True)
                
                # Log the bulk action
                BulkActionLog.objects.create(
                    admin_user=request.user,
                    action_type='bulk_activate',
                    model_name=queryset.model.__name__,
                    affected_count=updated,
                    details={'activated_ids': list(queryset.values_list('id', flat=True))}
                )
                
                self.message_user(
                    request,
                    _('Successfully activated %(count)d %(items)s.') % {
                        'count': updated,
                        'items': queryset.model._meta.verbose_name_plural
                    },
                    messages.SUCCESS
                )
        except Exception as e:
            self.message_user(
                request,
                _('Error activating items: %(error)s') % {'error': str(e)},
                messages.ERROR
            )
    
    def bulk_deactivate(self, request, queryset):
        """Bulk deactivate selected items."""
        if not hasattr(queryset.model, 'is_active'):
            self.message_user(request, _('This model does not support deactivation.'), messages.ERROR)
            return
        
        try:
            with transaction.atomic():
                updated = queryset.update(is_active=False)
                
                # Log the bulk action
                BulkActionLog.objects.create(
                    admin_user=request.user,
                    action_type='bulk_deactivate',
                    model_name=queryset.model.__name__,
                    affected_count=updated,
                    details={'deactivated_ids': list(queryset.values_list('id', flat=True))}
                )
                
                self.message_user(
                    request,
                    _('Successfully deactivated %(count)d %(items)s.') % {
                        'count': updated,
                        'items': queryset.model._meta.verbose_name_plural
                    },
                    messages.SUCCESS
                )
        except Exception as e:
            self.message_user(
                request,
                _('Error deactivating items: %(error)s') % {'error': str(e)},
                messages.ERROR
            )
    
    def bulk_export_csv(self, request, queryset):
        """Export selected items to CSV."""
        try:
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="{queryset.model.__name__}_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'
            
            writer = csv.writer(response)
            
            # Get field names
            fields = [field.name for field in queryset.model._meta.fields]
            writer.writerow(fields)
            
            # Write data
            for obj in queryset:
                row = []
                for field in fields:
                    value = getattr(obj, field)
                    if value is None:
                        value = ''
                    elif isinstance(value, datetime):
                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                    row.append(str(value))
                writer.writerow(row)
            
            # Log the export
            BulkActionLog.objects.create(
                admin_user=request.user,
                action_type='bulk_export_csv',
                model_name=queryset.model.__name__,
                affected_count=queryset.count(),
                details={'exported_ids': list(queryset.values_list('id', flat=True))}
            )
            
            return response
            
        except Exception as e:
            self.message_user(
                request,
                _('Error exporting data: %(error)s') % {'error': str(e)},
                messages.ERROR
            )
    
    def bulk_validate_data(self, request, queryset):
        """Validate selected items and report issues."""
        validation_errors = []
        
        for obj in queryset:
            try:
                obj.full_clean()
            except ValidationError as e:
                validation_errors.append({
                    'object': str(obj),
                    'id': obj.id,
                    'errors': e.message_dict if hasattr(e, 'message_dict') else [str(e)]
                })
        
        if validation_errors:
            # Store validation results in session for display
            request.session['validation_errors'] = validation_errors
            self.message_user(
                request,
                _('Found %(count)d validation errors. Check the validation report.') % {
                    'count': len(validation_errors)
                },
                messages.WARNING
            )
        else:
            self.message_user(
                request,
                _('All %(count)d items passed validation.') % {'count': queryset.count()},
                messages.SUCCESS
            )
        
        # Log the validation
        BulkActionLog.objects.create(
            admin_user=request.user,
            action_type='bulk_validate_data',
            model_name=queryset.model.__name__,
            affected_count=queryset.count(),
            details={
                'validated_ids': list(queryset.values_list('id', flat=True)),
                'error_count': len(validation_errors)
            }
        )


class UserBulkOperations(BulkOperationMixin):
    """Bulk operations specific to user management."""
    
    def get_actions(self, request):
        actions = super().get_actions(request)
        actions['bulk_send_welcome_email'] = (
            self.bulk_send_welcome_email,
            'bulk_send_welcome_email',
            _('Send welcome email to selected users')
        )
        actions['bulk_reset_passwords'] = (
            self.bulk_reset_passwords,
            'bulk_reset_passwords',
            _('Send password reset to selected users')
        )
        actions['bulk_verify_emails'] = (
            self.bulk_verify_emails,
            'bulk_verify_emails',
            _('Mark emails as verified for selected users')
        )
        return actions
    
    def bulk_send_welcome_email(self, request, queryset):
        """Send welcome emails to selected users."""
        if request.POST.get('post'):
            try:
                sent_count = 0
                for user in queryset:
                    if user.email:
                        send_mail(
                            subject=_('Welcome to CozyWish'),
                            message=_('Welcome to CozyWish! We\'re excited to have you join our community.'),
                            from_email='<EMAIL>',
                            recipient_list=[user.email],
                            fail_silently=False,
                        )
                        sent_count += 1
                
                BulkActionLog.objects.create(
                    admin_user=request.user,
                    action_type='bulk_send_welcome_email',
                    model_name='CustomUser',
                    affected_count=sent_count,
                    details={'user_ids': list(queryset.values_list('id', flat=True))}
                )
                
                self.message_user(
                    request,
                    _('Successfully sent welcome emails to %(count)d users.') % {'count': sent_count},
                    messages.SUCCESS
                )
                return None
                
            except Exception as e:
                self.message_user(
                    request,
                    _('Error sending emails: %(error)s') % {'error': str(e)},
                    messages.ERROR
                )
                return None
        
        # Show confirmation page
        context = {
            'title': _('Send welcome emails'),
            'queryset': queryset,
            'action_checkbox_name': helpers.ACTION_CHECKBOX_NAME,
        }
        return TemplateResponse(
            request,
            'admin_app/bulk_operations/confirm_email.html',
            context
        )
    
    def bulk_reset_passwords(self, request, queryset):
        """Send password reset emails to selected users."""
        if request.POST.get('post'):
            try:
                sent_count = 0
                for user in queryset:
                    if user.email and user.is_active:
                        # Generate password reset token and send email
                        # This would integrate with Django's password reset system
                        sent_count += 1
                
                BulkActionLog.objects.create(
                    admin_user=request.user,
                    action_type='bulk_reset_passwords',
                    model_name='CustomUser',
                    affected_count=sent_count,
                    details={'user_ids': list(queryset.values_list('id', flat=True))}
                )
                
                self.message_user(
                    request,
                    _('Successfully sent password reset emails to %(count)d users.') % {'count': sent_count},
                    messages.SUCCESS
                )
                return None
                
            except Exception as e:
                self.message_user(
                    request,
                    _('Error sending password reset emails: %(error)s') % {'error': str(e)},
                    messages.ERROR
                )
                return None
        
        # Show confirmation page
        context = {
            'title': _('Send password reset emails'),
            'queryset': queryset,
            'action_checkbox_name': helpers.ACTION_CHECKBOX_NAME,
        }
        return TemplateResponse(
            request,
            'admin_app/bulk_operations/confirm_password_reset.html',
            context
        )
    
    def bulk_verify_emails(self, request, queryset):
        """Mark emails as verified for selected users."""
        try:
            with transaction.atomic():
                updated = queryset.update(email_verified=True)
                
                BulkActionLog.objects.create(
                    admin_user=request.user,
                    action_type='bulk_verify_emails',
                    model_name='CustomUser',
                    affected_count=updated,
                    details={'user_ids': list(queryset.values_list('id', flat=True))}
                )
                
                self.message_user(
                    request,
                    _('Successfully verified emails for %(count)d users.') % {'count': updated},
                    messages.SUCCESS
                )
        except Exception as e:
            self.message_user(
                request,
                _('Error verifying emails: %(error)s') % {'error': str(e)},
                messages.ERROR
            )


class VenueBulkOperations(BulkOperationMixin):
    """Bulk operations specific to venue management."""
    
    def get_actions(self, request):
        actions = super().get_actions(request)
        actions['bulk_feature_venues'] = (
            self.bulk_feature_venues,
            'bulk_feature_venues',
            _('Feature selected venues')
        )
        actions['bulk_unfeature_venues'] = (
            self.bulk_unfeature_venues,
            'bulk_unfeature_venues',
            _('Unfeature selected venues')
        )
        actions['bulk_update_pricing'] = (
            self.bulk_update_pricing,
            'bulk_update_pricing',
            _('Update pricing for selected venues')
        )
        return actions
    
    def bulk_feature_venues(self, request, queryset):
        """Feature selected venues."""
        try:
            with transaction.atomic():
                updated = queryset.update(is_featured=True)
                
                BulkActionLog.objects.create(
                    admin_user=request.user,
                    action_type='bulk_feature_venues',
                    model_name='Venue',
                    affected_count=updated,
                    details={'venue_ids': list(queryset.values_list('id', flat=True))}
                )
                
                self.message_user(
                    request,
                    _('Successfully featured %(count)d venues.') % {'count': updated},
                    messages.SUCCESS
                )
        except Exception as e:
            self.message_user(
                request,
                _('Error featuring venues: %(error)s') % {'error': str(e)},
                messages.ERROR
            )
    
    def bulk_unfeature_venues(self, request, queryset):
        """Unfeature selected venues."""
        try:
            with transaction.atomic():
                updated = queryset.update(is_featured=False)
                
                BulkActionLog.objects.create(
                    admin_user=request.user,
                    action_type='bulk_unfeature_venues',
                    model_name='Venue',
                    affected_count=updated,
                    details={'venue_ids': list(queryset.values_list('id', flat=True))}
                )
                
                self.message_user(
                    request,
                    _('Successfully unfeatured %(count)d venues.') % {'count': updated},
                    messages.SUCCESS
                )
        except Exception as e:
            self.message_user(
                request,
                _('Error unfeaturing venues: %(error)s') % {'error': str(e)},
                messages.ERROR
            )


class BookingBulkOperations(BulkOperationMixin):
    """Bulk operations specific to booking management."""
    
    def get_actions(self, request):
        actions = super().get_actions(request)
        actions['bulk_confirm_bookings'] = (
            self.bulk_confirm_bookings,
            'bulk_confirm_bookings',
            _('Confirm selected bookings')
        )
        actions['bulk_cancel_bookings'] = (
            self.bulk_cancel_bookings,
            'bulk_cancel_bookings',
            _('Cancel selected bookings')
        )
        actions['bulk_send_reminders'] = (
            self.bulk_send_reminders,
            'bulk_send_reminders',
            _('Send reminders for selected bookings')
        )
        return actions
    
    def bulk_confirm_bookings(self, request, queryset):
        """Confirm selected bookings."""
        try:
            with transaction.atomic():
                updated = queryset.filter(status='pending').update(status='confirmed')
                
                BulkActionLog.objects.create(
                    admin_user=request.user,
                    action_type='bulk_confirm_bookings',
                    model_name='Booking',
                    affected_count=updated,
                    details={'booking_ids': list(queryset.values_list('id', flat=True))}
                )
                
                self.message_user(
                    request,
                    _('Successfully confirmed %(count)d bookings.') % {'count': updated},
                    messages.SUCCESS
                )
        except Exception as e:
            self.message_user(
                request,
                _('Error confirming bookings: %(error)s') % {'error': str(e)},
                messages.ERROR
            )
    
    def bulk_cancel_bookings(self, request, queryset):
        """Cancel selected bookings."""
        if request.POST.get('post'):
            try:
                with transaction.atomic():
                    updated = queryset.exclude(status='cancelled').update(status='cancelled')
                    
                    BulkActionLog.objects.create(
                        admin_user=request.user,
                        action_type='bulk_cancel_bookings',
                        model_name='Booking',
                        affected_count=updated,
                        details={'booking_ids': list(queryset.values_list('id', flat=True))}
                    )
                    
                    self.message_user(
                        request,
                        _('Successfully cancelled %(count)d bookings.') % {'count': updated},
                        messages.SUCCESS
                    )
                    return None
                    
            except Exception as e:
                self.message_user(
                    request,
                    _('Error cancelling bookings: %(error)s') % {'error': str(e)},
                    messages.ERROR
                )
                return None
        
        # Show confirmation page
        context = {
            'title': _('Cancel selected bookings'),
            'queryset': queryset,
            'action_checkbox_name': helpers.ACTION_CHECKBOX_NAME,
        }
        return TemplateResponse(
            request,
            'admin_app/bulk_operations/confirm_cancel_bookings.html',
            context
        )
