"""
Custom menu configuration for Django Admin Tools.
Provides organized navigation for the admin interface.
"""

from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from admin_tools.menu import items, Menu


class CustomMenu(Menu):
    """
    Custom menu for CozyWish admin interface.
    """
    
    def __init__(self, **kwargs):
        Menu.__init__(self, **kwargs)
        
        # Dashboard
        self.children += [
            items.MenuItem(
                title=_('Dashboard'),
                url=reverse('admin:index'),
                icon='fas fa-tachometer-alt'
            ),
        ]
        
        # User Management
        self.children += [
            items.Bookmarks(title=_('Bookmarks')),
            items.AppList(
                title=_('User Management'),
                icon='fas fa-users',
                models=(
                    'accounts_app.*',
                    'django.contrib.auth.*',
                )
            ),
        ]
        
        # Venue & Service Management
        self.children += [
            items.AppList(
                title=_('Venue Management'),
                icon='fas fa-building',
                models=(
                    'venues_app.*',
                )
            ),
        ]
        
        # Booking Management
        self.children += [
            items.AppList(
                title=_('Booking Management'),
                icon='fas fa-calendar-check',
                models=(
                    'booking_cart_app.*',
                )
            ),
        ]
        
        # Content Management
        self.children += [
            items.AppList(
                title=_('Content Management'),
                icon='fas fa-edit',
                models=(
                    'admin_app.*',
                    'cms_app.*',
                )
            ),
        ]
        
        # Reviews & Ratings
        self.children += [
            items.AppList(
                title=_('Reviews & Ratings'),
                icon='fas fa-star',
                models=(
                    'review_app.*',
                )
            ),
        ]
        
        # Payments & Discounts
        self.children += [
            items.AppList(
                title=_('Payments & Discounts'),
                icon='fas fa-credit-card',
                models=(
                    'payments_app.*',
                    'discount_app.*',
                )
            ),
        ]
        
        # Notifications
        self.children += [
            items.AppList(
                title=_('Notifications'),
                icon='fas fa-bell',
                models=(
                    'notifications_app.*',
                )
            ),
        ]
        
        # System Tools
        self.children += [
            items.MenuItem(
                title=_('System Tools'),
                icon='fas fa-tools',
                children=[
                    items.MenuItem(
                        title=_('System Health'),
                        url=reverse('admin:admin_app_systemhealthlog_changelist'),
                        icon='fas fa-heartbeat'
                    ),
                    items.MenuItem(
                        title=_('Bulk Actions Log'),
                        url=reverse('admin:admin_app_bulkactionlog_changelist'),
                        icon='fas fa-list'
                    ),
                    items.MenuItem(
                        title=_('Site Configuration'),
                        url=reverse('admin:admin_app_siteconfiguration_changelist'),
                        icon='fas fa-cog'
                    ),
                ]
            ),
        ]
        
        # Analytics & Reports
        self.children += [
            items.MenuItem(
                title=_('Analytics & Reports'),
                icon='fas fa-chart-bar',
                children=[
                    items.MenuItem(
                        title=_('User Analytics'),
                        url='/admin-panel/analytics/users/',
                        icon='fas fa-users'
                    ),
                    items.MenuItem(
                        title=_('Venue Analytics'),
                        url='/admin-panel/analytics/venues/',
                        icon='fas fa-building'
                    ),
                    items.MenuItem(
                        title=_('Booking Analytics'),
                        url='/admin-panel/analytics/bookings/',
                        icon='fas fa-calendar'
                    ),
                    items.MenuItem(
                        title=_('Revenue Reports'),
                        url='/admin-panel/analytics/revenue/',
                        icon='fas fa-dollar-sign'
                    ),
                ]
            ),
        ]
        
        # External Links
        self.children += [
            items.MenuItem(
                title=_('External Links'),
                icon='fas fa-external-link-alt',
                children=[
                    items.MenuItem(
                        title=_('View Site'),
                        url='/',
                        icon='fas fa-home'
                    ),
                    items.MenuItem(
                        title=_('CMS Admin'),
                        url='/cms-admin/',
                        icon='fas fa-edit'
                    ),
                    items.MenuItem(
                        title=_('Documentation'),
                        url='https://docs.cozywish.com',
                        icon='fas fa-book'
                    ),
                    items.MenuItem(
                        title=_('Support'),
                        url='https://support.cozywish.com',
                        icon='fas fa-life-ring'
                    ),
                ]
            ),
        ]


class ProviderMenu(Menu):
    """
    Simplified menu for service providers.
    """
    
    def __init__(self, **kwargs):
        Menu.__init__(self, **kwargs)
        
        # Dashboard
        self.children += [
            items.MenuItem(
                title=_('Dashboard'),
                url=reverse('admin:index'),
                icon='fas fa-tachometer-alt'
            ),
        ]
        
        # My Business
        self.children += [
            items.MenuItem(
                title=_('My Venues'),
                url=reverse('admin:venues_app_venue_changelist'),
                icon='fas fa-building'
            ),
            items.MenuItem(
                title=_('My Services'),
                url=reverse('admin:venues_app_service_changelist'),
                icon='fas fa-concierge-bell'
            ),
            items.MenuItem(
                title=_('My Bookings'),
                url=reverse('admin:booking_cart_app_booking_changelist'),
                icon='fas fa-calendar-check'
            ),
        ]
        
        # Analytics
        self.children += [
            items.MenuItem(
                title=_('Analytics'),
                icon='fas fa-chart-line',
                children=[
                    items.MenuItem(
                        title=_('Booking Analytics'),
                        url='/admin-panel/provider/analytics/bookings/',
                        icon='fas fa-calendar'
                    ),
                    items.MenuItem(
                        title=_('Revenue Reports'),
                        url='/admin-panel/provider/analytics/revenue/',
                        icon='fas fa-dollar-sign'
                    ),
                    items.MenuItem(
                        title=_('Performance Metrics'),
                        url='/admin-panel/provider/analytics/performance/',
                        icon='fas fa-chart-bar'
                    ),
                ]
            ),
        ]
        
        # Tools
        self.children += [
            items.MenuItem(
                title=_('Tools'),
                icon='fas fa-tools',
                children=[
                    items.MenuItem(
                        title=_('Bulk Operations'),
                        url='/admin-panel/provider/bulk-operations/',
                        icon='fas fa-tasks'
                    ),
                    items.MenuItem(
                        title=_('Export Data'),
                        url='/admin-panel/provider/export/',
                        icon='fas fa-download'
                    ),
                    items.MenuItem(
                        title=_('Import Data'),
                        url='/admin-panel/provider/import/',
                        icon='fas fa-upload'
                    ),
                ]
            ),
        ]
        
        # Support
        self.children += [
            items.MenuItem(
                title=_('Support'),
                icon='fas fa-life-ring',
                children=[
                    items.MenuItem(
                        title=_('Help Center'),
                        url='https://help.cozywish.com',
                        icon='fas fa-question-circle'
                    ),
                    items.MenuItem(
                        title=_('Contact Support'),
                        url='https://support.cozywish.com',
                        icon='fas fa-envelope'
                    ),
                    items.MenuItem(
                        title=_('Provider Guide'),
                        url='https://docs.cozywish.com/providers',
                        icon='fas fa-book'
                    ),
                ]
            ),
        ]
