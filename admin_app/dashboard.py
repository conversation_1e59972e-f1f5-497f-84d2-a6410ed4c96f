"""
Custom dashboard configuration for Django Admin Tools.
Provides modern dashboard widgets and analytics for the admin interface.
"""

from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from admin_tools.dashboard import modules, Dashboard, AppIndexDashboard
from admin_tools.utils import get_admin_site_name


def dashboard_callback(request, context):
    """
    Callback function for django-unfold dashboard customization.
    """
    return {
        'recent_actions_limit': 10,
        'show_bookmarks': True,
        'show_recent_actions': True,
    }


class CustomIndexDashboard(Dashboard):
    """
    Custom index dashboard for CozyWish admin.
    """
    
    def init_with_context(self, context):
        site_name = get_admin_site_name(context)
        
        # Welcome message
        self.children.append(modules.LinkList(
            _('Quick Actions'),
            layout='inline',
            draggable=False,
            deletable=False,
            collapsible=False,
            children=[
                [_('View Site'), '/'],
                [_('User Management'), reverse('admin:auth_user_changelist')],
                [_('Venue Management'), reverse('admin:venues_app_venue_changelist')],
                [_('Booking Management'), reverse('admin:booking_cart_app_booking_changelist')],
                [_('System Health'), reverse('admin:admin_app_systemhealthlog_changelist')],
            ]
        ))
        
        # Recent actions
        self.children.append(modules.RecentActions(
            _('Recent Actions'),
            limit=10,
            collapsible=False,
        ))
        
        # User statistics
        self.children.append(modules.ModelList(
            _('User Management'),
            models=(
                'accounts_app.models.CustomUser',
                'accounts_app.models.CustomerProfile',
                'accounts_app.models.ServiceProviderProfile',
            ),
        ))
        
        # Venue and booking management
        self.children.append(modules.ModelList(
            _('Venue & Booking Management'),
            models=(
                'venues_app.models.Venue',
                'venues_app.models.Service',
                'booking_cart_app.models.Booking',
                'booking_cart_app.models.Cart',
            ),
        ))
        
        # Content management
        self.children.append(modules.ModelList(
            _('Content Management'),
            models=(
                'admin_app.models.StaticPage',
                'admin_app.models.BlogPost',
                'admin_app.models.HomepageBlock',
                'admin_app.models.MediaFile',
            ),
        ))
        
        # System monitoring
        self.children.append(modules.ModelList(
            _('System Monitoring'),
            models=(
                'admin_app.models.SystemHealthLog',
                'admin_app.models.BulkActionLog',
                'admin_app.models.Announcement',
            ),
        ))


class CustomAppIndexDashboard(AppIndexDashboard):
    """
    Custom app index dashboard for CozyWish admin.
    """
    
    def __init__(self, *args, **kwargs):
        AppIndexDashboard.__init__(self, *args, **kwargs)
        
        # Recent actions for this app
        self.children += [
            modules.RecentActions(
                _('Recent Actions'),
                include_list=self.get_app_content_types(),
                limit=10
            ),
            modules.ModelList(
                title=_('Application models'),
                models=self.models,
            ),
        ]


class ProviderDashboard(Dashboard):
    """
    Custom dashboard for service providers.
    """
    
    def init_with_context(self, context):
        # Provider quick actions
        self.children.append(modules.LinkList(
            _('Provider Actions'),
            layout='inline',
            draggable=False,
            deletable=False,
            collapsible=False,
            children=[
                [_('Manage Venues'), reverse('admin:venues_app_venue_changelist')],
                [_('View Bookings'), reverse('admin:booking_cart_app_booking_changelist')],
                [_('Manage Services'), reverse('admin:venues_app_service_changelist')],
                [_('View Analytics'), '/admin-panel/analytics/'],
            ]
        ))
        
        # Provider-specific models
        self.children.append(modules.ModelList(
            _('My Business'),
            models=(
                'venues_app.models.Venue',
                'venues_app.models.Service',
                'booking_cart_app.models.Booking',
            ),
        ))
        
        # Recent provider actions
        self.children.append(modules.RecentActions(
            _('Recent Actions'),
            limit=5,
            collapsible=False,
        ))


# Dashboard widgets for analytics
class AnalyticsWidget:
    """
    Custom analytics widget for dashboard.
    """
    
    @staticmethod
    def get_user_stats():
        """Get user statistics."""
        from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile
        
        return {
            'total_users': CustomUser.objects.count(),
            'active_users': CustomUser.objects.filter(is_active=True).count(),
            'customers': CustomerProfile.objects.count(),
            'providers': ServiceProviderProfile.objects.count(),
        }
    
    @staticmethod
    def get_venue_stats():
        """Get venue statistics."""
        from venues_app.models import Venue, Service
        
        return {
            'total_venues': Venue.objects.count(),
            'active_venues': Venue.objects.filter(is_active=True).count(),
            'total_services': Service.objects.count(),
            'featured_venues': Venue.objects.filter(is_featured=True).count(),
        }
    
    @staticmethod
    def get_booking_stats():
        """Get booking statistics."""
        from booking_cart_app.models import Booking, Cart
        from django.utils import timezone
        from datetime import timedelta
        
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        
        return {
            'total_bookings': Booking.objects.count(),
            'recent_bookings': Booking.objects.filter(booking_date__gte=week_ago).count(),
            'active_carts': Cart.objects.filter(expires_at__gt=timezone.now()).count(),
            'pending_bookings': Booking.objects.filter(status='pending').count(),
        }


class DashboardCharts:
    """
    Chart data for dashboard widgets.
    """
    
    @staticmethod
    def get_booking_trend_data():
        """Get booking trend data for charts."""
        from booking_cart_app.models import Booking
        from django.db.models import Count
        from django.utils import timezone
        from datetime import timedelta
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        bookings = Booking.objects.filter(
            booking_date__range=[start_date, end_date]
        ).extra(
            select={'day': 'date(booking_date)'}
        ).values('day').annotate(
            count=Count('id')
        ).order_by('day')
        
        return list(bookings)
    
    @staticmethod
    def get_user_registration_data():
        """Get user registration trend data."""
        from accounts_app.models import CustomUser
        from django.db.models import Count
        from django.utils import timezone
        from datetime import timedelta
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        users = CustomUser.objects.filter(
            date_joined__range=[start_date, end_date]
        ).extra(
            select={'day': 'date(date_joined)'}
        ).values('day').annotate(
            count=Count('id')
        ).order_by('day')
        
        return list(users)
    
    @staticmethod
    def get_venue_category_data():
        """Get venue distribution by category."""
        from venues_app.models import Venue
        from django.db.models import Count
        
        categories = Venue.objects.values(
            'venue_type__name'
        ).annotate(
            count=Count('id')
        ).order_by('-count')[:10]
        
        return list(categories)
