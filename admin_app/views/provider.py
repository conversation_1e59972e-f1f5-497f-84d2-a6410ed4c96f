"""
Provider-specific dashboard views for enhanced admin interface.
Provides modern analytics, venue management tools, and enhanced UI for service providers.
"""

import json
from datetime import datetime, timedelta
from decimal import Decimal

from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Count, Sum, Avg, Q
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render, get_object_or_404
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.views.decorators.http import require_http_methods

from accounts_app.models import ServiceProviderProfile
from booking_cart_app.models import Booking, Cart
from venues_app.models import Venue, Service
from review_app.models import Review


@method_decorator([login_required, staff_member_required], name='dispatch')
class ProviderDashboardView(TemplateView):
    """
    Main dashboard view for service providers.
    """
    template_name = 'admin_app/provider/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get provider profile
        try:
            provider = ServiceProviderProfile.objects.get(user=self.request.user)
        except ServiceProviderProfile.DoesNotExist:
            provider = None
        
        if provider:
            # Get provider's venues
            venues = Venue.objects.filter(service_provider=provider)
            
            # Get basic statistics
            context.update({
                'provider': provider,
                'total_venues': venues.count(),
                'active_venues': venues.filter(is_active=True).count(),
                'total_services': Service.objects.filter(venue__in=venues).count(),
                'total_bookings': Booking.objects.filter(venue__in=venues).count(),
                'recent_bookings': self.get_recent_bookings(venues),
                'venue_performance': self.get_venue_performance(venues),
                'booking_trends': self.get_booking_trends(venues),
                'revenue_summary': self.get_revenue_summary(venues),
            })
        
        return context
    
    def get_recent_bookings(self, venues):
        """Get recent bookings for provider's venues."""
        return Booking.objects.filter(
            venue__in=venues
        ).select_related(
            'customer', 'venue'
        ).order_by('-booking_date')[:10]
    
    def get_venue_performance(self, venues):
        """Get performance metrics for each venue."""
        performance = []
        for venue in venues:
            bookings = Booking.objects.filter(venue=venue)
            reviews = Review.objects.filter(venue=venue)
            
            performance.append({
                'venue': venue,
                'total_bookings': bookings.count(),
                'revenue': bookings.aggregate(Sum('total_price'))['total_price__sum'] or 0,
                'avg_rating': reviews.aggregate(Avg('rating'))['rating__avg'] or 0,
                'review_count': reviews.count(),
            })
        
        return performance
    
    def get_booking_trends(self, venues):
        """Get booking trends for the last 30 days."""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        bookings = Booking.objects.filter(
            venue__in=venues,
            booking_date__range=[start_date, end_date]
        ).extra(
            select={'day': 'date(booking_date)'}
        ).values('day').annotate(
            count=Count('id'),
            revenue=Sum('total_price')
        ).order_by('day')
        
        return list(bookings)
    
    def get_revenue_summary(self, venues):
        """Get revenue summary for different time periods."""
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        bookings = Booking.objects.filter(venue__in=venues)
        
        return {
            'today': bookings.filter(booking_date=today).aggregate(
                Sum('total_price'))['total_price__sum'] or 0,
            'this_week': bookings.filter(booking_date__gte=week_ago).aggregate(
                Sum('total_price'))['total_price__sum'] or 0,
            'this_month': bookings.filter(booking_date__gte=month_ago).aggregate(
                Sum('total_price'))['total_price__sum'] or 0,
            'total': bookings.aggregate(Sum('total_price'))['total_price__sum'] or 0,
        }


@method_decorator([login_required, staff_member_required], name='dispatch')
class ProviderAnalyticsView(TemplateView):
    """
    Analytics dashboard for service providers.
    """
    template_name = 'admin_app/provider/analytics.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        try:
            provider = ServiceProviderProfile.objects.get(user=self.request.user)
            venues = Venue.objects.filter(service_provider=provider)
            
            context.update({
                'provider': provider,
                'booking_analytics': self.get_booking_analytics(venues),
                'revenue_analytics': self.get_revenue_analytics(venues),
                'customer_analytics': self.get_customer_analytics(venues),
                'service_analytics': self.get_service_analytics(venues),
            })
        except ServiceProviderProfile.DoesNotExist:
            pass
        
        return context
    
    def get_booking_analytics(self, venues):
        """Get detailed booking analytics."""
        bookings = Booking.objects.filter(venue__in=venues)
        
        return {
            'total_bookings': bookings.count(),
            'confirmed_bookings': bookings.filter(status='confirmed').count(),
            'pending_bookings': bookings.filter(status='pending').count(),
            'cancelled_bookings': bookings.filter(status='cancelled').count(),
            'completion_rate': self.calculate_completion_rate(bookings),
            'monthly_trends': self.get_monthly_booking_trends(venues),
        }
    
    def get_revenue_analytics(self, venues):
        """Get detailed revenue analytics."""
        bookings = Booking.objects.filter(venue__in=venues, status='confirmed')
        
        total_revenue = bookings.aggregate(Sum('total_price'))['total_price__sum'] or 0
        avg_booking_value = bookings.aggregate(Avg('total_price'))['total_price__avg'] or 0
        
        return {
            'total_revenue': total_revenue,
            'average_booking_value': avg_booking_value,
            'monthly_revenue': self.get_monthly_revenue_trends(venues),
            'revenue_by_venue': self.get_revenue_by_venue(venues),
        }
    
    def get_customer_analytics(self, venues):
        """Get customer analytics."""
        bookings = Booking.objects.filter(venue__in=venues)
        unique_customers = bookings.values('customer').distinct().count()
        repeat_customers = bookings.values('customer').annotate(
            booking_count=Count('id')
        ).filter(booking_count__gt=1).count()
        
        return {
            'unique_customers': unique_customers,
            'repeat_customers': repeat_customers,
            'repeat_rate': (repeat_customers / unique_customers * 100) if unique_customers > 0 else 0,
            'customer_distribution': self.get_customer_distribution(venues),
        }
    
    def get_service_analytics(self, venues):
        """Get service performance analytics."""
        services = Service.objects.filter(venue__in=venues)
        service_performance = []
        
        for service in services:
            bookings = Booking.objects.filter(
                bookingitem__service=service
            ).distinct()
            
            service_performance.append({
                'service': service,
                'booking_count': bookings.count(),
                'revenue': bookings.aggregate(Sum('total_price'))['total_price__sum'] or 0,
                'avg_rating': Review.objects.filter(
                    booking__bookingitem__service=service
                ).aggregate(Avg('rating'))['rating__avg'] or 0,
            })
        
        return service_performance
    
    def calculate_completion_rate(self, bookings):
        """Calculate booking completion rate."""
        total = bookings.count()
        completed = bookings.filter(status='confirmed').count()
        return (completed / total * 100) if total > 0 else 0
    
    def get_monthly_booking_trends(self, venues):
        """Get monthly booking trends for the last 12 months."""
        end_date = timezone.now().date()
        start_date = end_date.replace(day=1) - timedelta(days=365)
        
        bookings = Booking.objects.filter(
            venue__in=venues,
            booking_date__gte=start_date
        ).extra(
            select={'month': "DATE_FORMAT(booking_date, '%%Y-%%m')"}
        ).values('month').annotate(
            count=Count('id')
        ).order_by('month')
        
        return list(bookings)
    
    def get_monthly_revenue_trends(self, venues):
        """Get monthly revenue trends."""
        end_date = timezone.now().date()
        start_date = end_date.replace(day=1) - timedelta(days=365)
        
        revenue = Booking.objects.filter(
            venue__in=venues,
            booking_date__gte=start_date,
            status='confirmed'
        ).extra(
            select={'month': "DATE_FORMAT(booking_date, '%%Y-%%m')"}
        ).values('month').annotate(
            revenue=Sum('total_price')
        ).order_by('month')
        
        return list(revenue)
    
    def get_revenue_by_venue(self, venues):
        """Get revenue breakdown by venue."""
        revenue_data = []
        for venue in venues:
            revenue = Booking.objects.filter(
                venue=venue,
                status='confirmed'
            ).aggregate(Sum('total_price'))['total_price__sum'] or 0
            
            revenue_data.append({
                'venue': venue.venue_name,
                'revenue': revenue
            })
        
        return revenue_data
    
    def get_customer_distribution(self, venues):
        """Get customer distribution by location."""
        customers = Booking.objects.filter(
            venue__in=venues
        ).values(
            'customer__customerprofile__city'
        ).annotate(
            count=Count('customer', distinct=True)
        ).order_by('-count')[:10]
        
        return list(customers)


@login_required
@staff_member_required
@require_http_methods(["GET"])
def provider_analytics_api(request):
    """
    API endpoint for provider analytics data.
    """
    try:
        provider = ServiceProviderProfile.objects.get(user=request.user)
        venues = Venue.objects.filter(service_provider=provider)
        
        analytics_type = request.GET.get('type', 'overview')
        
        if analytics_type == 'booking_trends':
            data = get_booking_trends_data(venues)
        elif analytics_type == 'revenue_trends':
            data = get_revenue_trends_data(venues)
        elif analytics_type == 'venue_performance':
            data = get_venue_performance_data(venues)
        else:
            data = get_overview_data(venues)
        
        return JsonResponse(data)
    
    except ServiceProviderProfile.DoesNotExist:
        return JsonResponse({'error': 'Provider profile not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def get_booking_trends_data(venues):
    """Get booking trends data for API."""
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)
    
    trends = Booking.objects.filter(
        venue__in=venues,
        booking_date__range=[start_date, end_date]
    ).extra(
        select={'day': 'date(booking_date)'}
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')
    
    return {
        'labels': [item['day'].strftime('%Y-%m-%d') for item in trends],
        'data': [item['count'] for item in trends],
    }


def get_revenue_trends_data(venues):
    """Get revenue trends data for API."""
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)
    
    trends = Booking.objects.filter(
        venue__in=venues,
        booking_date__range=[start_date, end_date],
        status='confirmed'
    ).extra(
        select={'day': 'date(booking_date)'}
    ).values('day').annotate(
        revenue=Sum('total_price')
    ).order_by('day')
    
    return {
        'labels': [item['day'].strftime('%Y-%m-%d') for item in trends],
        'data': [float(item['revenue'] or 0) for item in trends],
    }


def get_venue_performance_data(venues):
    """Get venue performance data for API."""
    performance = []
    for venue in venues:
        bookings = Booking.objects.filter(venue=venue)
        revenue = bookings.filter(status='confirmed').aggregate(
            Sum('total_price'))['total_price__sum'] or 0
        
        performance.append({
            'name': venue.venue_name,
            'bookings': bookings.count(),
            'revenue': float(revenue),
        })
    
    return {'venues': performance}


def get_overview_data(venues):
    """Get overview data for API."""
    bookings = Booking.objects.filter(venue__in=venues)
    
    return {
        'total_venues': venues.count(),
        'total_bookings': bookings.count(),
        'total_revenue': float(bookings.filter(status='confirmed').aggregate(
            Sum('total_price'))['total_price__sum'] or 0),
        'pending_bookings': bookings.filter(status='pending').count(),
    }
