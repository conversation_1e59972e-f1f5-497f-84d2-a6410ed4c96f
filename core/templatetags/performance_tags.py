"""
Performance-related template tags for CozyWish
"""

import os
from django import template
from django.conf import settings
from django.utils.safestring import mark_safe
from django.templatetags.static import static
from django.core.cache import cache

register = template.Library()


@register.simple_tag
def inline_critical_css():
    """
    Inline critical CSS for above-the-fold content
    """
    cache_key = 'critical_css_content'
    css_content = cache.get(cache_key)
    
    if css_content is None:
        try:
            # Try to get compiled CSS first
            css_path = os.path.join(settings.STATIC_ROOT or settings.STATICFILES_DIRS[0], 'dist', 'css', 'critical.css')
            if not os.path.exists(css_path):
                # Fallback to SCSS file
                css_path = os.path.join(settings.STATIC_ROOT or settings.STATICFILES_DIRS[0], 'scss', 'critical.scss')
            
            if os.path.exists(css_path):
                with open(css_path, 'r', encoding='utf-8') as f:
                    css_content = f.read()
                    
                # Cache for 1 hour in production, 1 minute in development
                cache_timeout = 3600 if settings.DEBUG is False else 60
                cache.set(cache_key, css_content, cache_timeout)
            else:
                css_content = ''
                
        except Exception as e:
            css_content = ''
            if settings.DEBUG:
                print(f"Error loading critical CSS: {e}")
    
    if css_content:
        return mark_safe(f'<style>{css_content}</style>')
    return ''


@register.simple_tag
def preload_resource(href, as_type, crossorigin=None, media=None):
    """
    Generate preload link tag for critical resources
    """
    attrs = [
        'rel="preload"',
        f'href="{href}"',
        f'as="{as_type}"'
    ]
    
    if crossorigin:
        attrs.append(f'crossorigin="{crossorigin}"')
    
    if media:
        attrs.append(f'media="{media}"')
    
    return mark_safe(f'<link {" ".join(attrs)}>')


@register.simple_tag
def prefetch_resource(href):
    """
    Generate prefetch link tag for non-critical resources
    """
    return mark_safe(f'<link rel="prefetch" href="{href}">')


@register.simple_tag
def dns_prefetch(domain):
    """
    Generate DNS prefetch link tag
    """
    return mark_safe(f'<link rel="dns-prefetch" href="//{domain}">')


@register.simple_tag
def preconnect(url, crossorigin=False):
    """
    Generate preconnect link tag
    """
    attrs = ['rel="preconnect"', f'href="{url}"']
    
    if crossorigin:
        attrs.append('crossorigin')
    
    return mark_safe(f'<link {" ".join(attrs)}>')


@register.simple_tag
def lazy_image(src, alt="", css_class="", data_src=None, placeholder=None):
    """
    Generate lazy-loaded image tag
    """
    if data_src is None:
        data_src = src
        src = placeholder or "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
    
    attrs = [
        f'src="{src}"',
        f'data-src="{data_src}"',
        f'alt="{alt}"',
        'loading="lazy"'
    ]
    
    if css_class:
        attrs.append(f'class="{css_class} lazy"')
    else:
        attrs.append('class="lazy"')
    
    return mark_safe(f'<img {" ".join(attrs)}>')


@register.simple_tag
def lazy_background(element="div", bg_url="", css_class="", content=""):
    """
    Generate element with lazy-loaded background image
    """
    attrs = [
        f'data-bg="{bg_url}"'
    ]
    
    if css_class:
        attrs.append(f'class="{css_class} lazy"')
    else:
        attrs.append('class="lazy"')
    
    return mark_safe(f'<{element} {" ".join(attrs)}>{content}</{element}>')


@register.simple_tag
def performance_hints():
    """
    Generate performance-related meta tags and hints
    """
    hints = [
        '<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">',
        '<meta http-equiv="X-UA-Compatible" content="IE=edge">',
        '<link rel="dns-prefetch" href="//fonts.googleapis.com">',
        '<link rel="dns-prefetch" href="//fonts.gstatic.com">',
        '<link rel="dns-prefetch" href="//cdn.jsdelivr.net">',
        '<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">',
        '<link rel="preconnect" href="https://fonts.googleapis.com">',
        '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>',
    ]
    
    return mark_safe('\n'.join(hints))


@register.inclusion_tag('performance/resource_hints.html')
def resource_hints():
    """
    Include template with resource hints
    """
    return {
        'fonts': [
            'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
            'https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap'
        ],
        'scripts': [
            'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js',
            'https://unpkg.com/alpinejs@3.13.3/dist/cdn.min.js',
            'https://unpkg.com/htmx.org@1.9.10'
        ],
        'styles': [
            'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css'
        ]
    }


@register.simple_tag
def critical_resource_preload():
    """
    Preload critical resources for faster page load
    """
    preloads = [
        preload_resource(static('css/critical.css'), 'style'),
        preload_resource(static('js/main.js'), 'script'),
        preload_resource(static('images/logo.png'), 'image'),
        preload_resource('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap', 'style', crossorigin='anonymous'),
        preload_resource('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap', 'style', crossorigin='anonymous'),
    ]
    
    return mark_safe('\n'.join(preloads))


@register.filter
def add_lazy_loading(html):
    """
    Add lazy loading attributes to images in HTML content
    """
    import re
    
    # Replace img tags with lazy loading
    def replace_img(match):
        img_tag = match.group(0)
        if 'data-src' not in img_tag and 'loading=' not in img_tag:
            # Add lazy loading if not already present
            img_tag = img_tag.replace('<img ', '<img loading="lazy" ')
        return img_tag
    
    # Apply regex replacement
    html = re.sub(r'<img[^>]+>', replace_img, html)
    
    return mark_safe(html)
