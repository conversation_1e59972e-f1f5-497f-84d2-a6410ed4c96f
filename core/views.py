"""
Core views for CozyWish application
"""

from django.shortcuts import render
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator


class OfflineView(TemplateView):
    """
    Offline page for PWA functionality
    """
    template_name = 'offline.html'
    
    @method_decorator(cache_page(60 * 60 * 24))  # Cache for 24 hours
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': 'Offline - CozyWish',
            'meta_description': 'You are currently offline. Browse cached content while waiting for connection.',
        })
        return context


def manifest_view(request):
    """
    Serve the PWA manifest with dynamic content
    """
    manifest_data = {
        "name": "CozyWish - Venue Booking Platform",
        "short_name": "CozyWish",
        "description": "Find and book the perfect venue for your special events",
        "start_url": "/",
        "display": "standalone",
        "background_color": "#FFF9F4",
        "theme_color": "#42241A",
        "orientation": "portrait-primary",
        "scope": "/",
        "lang": "en-US",
        "categories": ["business", "lifestyle", "travel"],
        "icons": [
            {
                "src": "/static/images/icons/icon-72x72.png",
                "sizes": "72x72",
                "type": "image/png",
                "purpose": "maskable any"
            },
            {
                "src": "/static/images/icons/icon-96x96.png",
                "sizes": "96x96",
                "type": "image/png",
                "purpose": "maskable any"
            },
            {
                "src": "/static/images/icons/icon-128x128.png",
                "sizes": "128x128",
                "type": "image/png",
                "purpose": "maskable any"
            },
            {
                "src": "/static/images/icons/icon-144x144.png",
                "sizes": "144x144",
                "type": "image/png",
                "purpose": "maskable any"
            },
            {
                "src": "/static/images/icons/icon-152x152.png",
                "sizes": "152x152",
                "type": "image/png",
                "purpose": "maskable any"
            },
            {
                "src": "/static/images/icons/icon-192x192.png",
                "sizes": "192x192",
                "type": "image/png",
                "purpose": "maskable any"
            },
            {
                "src": "/static/images/icons/icon-384x384.png",
                "sizes": "384x384",
                "type": "image/png",
                "purpose": "maskable any"
            },
            {
                "src": "/static/images/icons/icon-512x512.png",
                "sizes": "512x512",
                "type": "image/png",
                "purpose": "maskable any"
            }
        ],
        "shortcuts": [
            {
                "name": "Search Venues",
                "short_name": "Search",
                "description": "Find venues for your event",
                "url": "/venues/search/",
                "icons": [
                    {
                        "src": "/static/images/icons/search-96x96.png",
                        "sizes": "96x96"
                    }
                ]
            },
            {
                "name": "My Bookings",
                "short_name": "Bookings",
                "description": "View your venue bookings",
                "url": "/bookings/",
                "icons": [
                    {
                        "src": "/static/images/icons/bookings-96x96.png",
                        "sizes": "96x96"
                    }
                ]
            }
        ],
        "prefer_related_applications": False,
        "launch_handler": {
            "client_mode": "navigate-existing"
        }
    }
    
    return JsonResponse(manifest_data, content_type='application/manifest+json')


def service_worker_view(request):
    """
    Serve the service worker with proper headers
    """
    from django.http import HttpResponse
    from django.conf import settings
    import os
    
    sw_path = os.path.join(settings.STATIC_ROOT or settings.STATICFILES_DIRS[0], 'sw.js')
    
    try:
        with open(sw_path, 'r') as f:
            content = f.read()
    except FileNotFoundError:
        # Fallback content if file not found
        content = """
        // Minimal service worker
        self.addEventListener('install', event => {
            console.log('Service Worker installing...');
        });
        
        self.addEventListener('activate', event => {
            console.log('Service Worker activating...');
        });
        
        self.addEventListener('fetch', event => {
            // Let the browser handle all requests
        });
        """
    
    response = HttpResponse(content, content_type='application/javascript')
    response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'
    
    return response
