"""
Booking Cart App Tasks
Async tasks for calendar integration, reminders, and workflow automation
"""

import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from django.core.mail import send_mail
from django_q.tasks import async_task, schedule
from django_q.models import Schedule

from .models import (
    Booking, BookingReminder, BookingWorkflow, WorkflowExecution,
    BookingCalendarIntegration, AppointmentSlot
)
from .utils import (
    CalendarIntegrationService, ReminderService, WorkflowService
)

logger = logging.getLogger(__name__)


def sync_booking_to_calendar(booking_id: int):
    """
    Sync a booking to external calendars (Google Calendar, etc.)
    This task runs asynchronously to avoid blocking the web request
    """
    try:
        booking = Booking.objects.get(id=booking_id)
        integration, created = BookingCalendarIntegration.objects.get_or_create(
            booking=booking
        )
        
        calendar_service = CalendarIntegrationService()
        
        # Update integration status
        integration.sync_status = 'pending'
        integration.save()
        
        # Generate iCal UID if not exists
        if not integration.ical_uid:
            integration.ical_uid = f"{booking.booking_id}@cozywish.com"
        
        # Sync to Google Calendar if credentials are available
        if hasattr(booking.customer, 'google_calendar_credentials'):
            try:
                # Get appointment slots related to this booking
                appointment_slots = AppointmentSlot.objects.filter(
                    appointment_bookings__booking=booking
                )
                
                for appointment in appointment_slots:
                    event_id = calendar_service.sync_to_google_calendar(
                        appointment, 
                        booking.customer.google_calendar_credentials
                    )
                    
                    if event_id:
                        integration.google_event_id = event_id
                        integration.google_calendar_id = 'primary'
                        integration.sync_status = 'synced'
                        integration.last_synced = timezone.now()
                    else:
                        integration.sync_status = 'failed'
                        integration.sync_error = 'Failed to create Google Calendar event'
                
            except Exception as e:
                integration.sync_status = 'failed'
                integration.sync_error = str(e)
                logger.error(f"Google Calendar sync failed for booking {booking_id}: {e}")
        
        integration.save()
        
        logger.info(f"Calendar sync completed for booking {booking_id}")
        
    except Booking.DoesNotExist:
        logger.error(f"Booking {booking_id} not found for calendar sync")
    except Exception as e:
        logger.error(f"Calendar sync error for booking {booking_id}: {e}")


def send_booking_reminder(reminder_id: int):
    """
    Send a booking reminder
    """
    try:
        reminder = BookingReminder.objects.get(id=reminder_id)
        
        # Check if reminder is still scheduled
        if reminder.status != 'scheduled':
            logger.info(f"Reminder {reminder_id} already processed")
            return
        
        reminder_service = ReminderService()
        
        # Update reminder status
        reminder.status = 'sending'
        reminder.save()
        
        # Send the reminder
        success = reminder_service.send_reminder(reminder)
        
        if success:
            logger.info(f"Reminder {reminder_id} sent successfully")
        else:
            logger.error(f"Failed to send reminder {reminder_id}")
        
    except BookingReminder.DoesNotExist:
        logger.error(f"Reminder {reminder_id} not found")
    except Exception as e:
        logger.error(f"Error sending reminder {reminder_id}: {e}")


def execute_booking_workflow(workflow_id: int, booking_id: int, execution_id: int = None):
    """
    Execute a booking workflow
    """
    try:
        workflow = BookingWorkflow.objects.get(id=workflow_id)
        booking = Booking.objects.get(id=booking_id)
        
        # Create or get workflow execution record
        if execution_id:
            execution = WorkflowExecution.objects.get(id=execution_id)
        else:
            execution = WorkflowExecution.objects.create(
                workflow=workflow,
                booking=booking,
                status='running',
                started_at=timezone.now()
            )
        
        # Update execution status
        execution.status = 'running'
        execution.started_at = timezone.now()
        execution.save()
        
        workflow_service = WorkflowService()
        
        # Execute the workflow
        success = workflow_service.execute_workflow(workflow, booking)
        
        # Update execution record
        execution.completed_at = timezone.now()
        execution.status = 'completed' if success else 'failed'
        execution.result = {
            'success': success,
            'executed_at': timezone.now().isoformat(),
            'workflow_action': workflow.action_type
        }
        execution.save()
        
        # Update workflow statistics
        workflow.execution_count += 1
        workflow.last_executed = timezone.now()
        workflow.save()
        
        logger.info(f"Workflow {workflow_id} executed for booking {booking_id}")
        
    except (BookingWorkflow.DoesNotExist, Booking.DoesNotExist) as e:
        logger.error(f"Workflow execution failed: {e}")
    except Exception as e:
        logger.error(f"Error executing workflow {workflow_id}: {e}")
        
        if execution_id:
            try:
                execution = WorkflowExecution.objects.get(id=execution_id)
                execution.status = 'failed'
                execution.completed_at = timezone.now()
                execution.result = {'error': str(e)}
                execution.save()
            except:
                pass


def process_scheduled_reminders():
    """
    Process all scheduled reminders that are due
    This task runs periodically (e.g., every 15 minutes)
    """
    try:
        now = timezone.now()
        
        # Get reminders that are due
        due_reminders = BookingReminder.objects.filter(
            status='scheduled',
            scheduled_time__lte=now
        )
        
        logger.info(f"Processing {due_reminders.count()} due reminders")
        
        for reminder in due_reminders:
            # Queue reminder sending task
            async_task(
                'booking_cart_app.tasks.send_booking_reminder',
                reminder.id,
                task_name=f'send_reminder_{reminder.id}'
            )
        
    except Exception as e:
        logger.error(f"Error processing scheduled reminders: {e}")


def process_scheduled_workflows():
    """
    Process scheduled workflow executions
    This task runs periodically (e.g., every 5 minutes)
    """
    try:
        now = timezone.now()
        
        # Get workflow executions that are due
        due_executions = WorkflowExecution.objects.filter(
            status='pending',
            scheduled_time__lte=now
        )
        
        logger.info(f"Processing {due_executions.count()} due workflow executions")
        
        for execution in due_executions:
            # Queue workflow execution task
            async_task(
                'booking_cart_app.tasks.execute_booking_workflow',
                execution.workflow.id,
                execution.booking.id,
                execution.id,
                task_name=f'execute_workflow_{execution.id}'
            )
        
    except Exception as e:
        logger.error(f"Error processing scheduled workflows: {e}")


def cleanup_old_calendar_integrations():
    """
    Cleanup old calendar integration records
    Runs weekly to remove old sync records
    """
    try:
        # Remove integration records older than 1 year
        cutoff_date = timezone.now() - timedelta(days=365)
        
        old_integrations = BookingCalendarIntegration.objects.filter(
            created_at__lt=cutoff_date,
            booking__status__in=['completed', 'cancelled']
        )
        
        deleted_count = old_integrations.count()
        old_integrations.delete()
        
        logger.info(f"Cleaned up {deleted_count} old calendar integrations")
        
    except Exception as e:
        logger.error(f"Error cleaning up calendar integrations: {e}")


def sync_all_pending_calendars():
    """
    Sync all pending calendar integrations
    Useful for batch processing or recovery
    """
    try:
        pending_integrations = BookingCalendarIntegration.objects.filter(
            sync_status='pending'
        )
        
        logger.info(f"Syncing {pending_integrations.count()} pending calendar integrations")
        
        for integration in pending_integrations:
            # Queue calendar sync task
            async_task(
                'booking_cart_app.tasks.sync_booking_to_calendar',
                integration.booking.id,
                task_name=f'sync_calendar_{integration.booking.id}'
            )
        
    except Exception as e:
        logger.error(f"Error syncing pending calendars: {e}")


def generate_appointment_series():
    """
    Generate appointment slots from recurring series
    This task runs daily to create upcoming appointment slots
    """
    try:
        from .models import AppointmentSeries
        
        # Get active appointment series
        active_series = AppointmentSeries.objects.filter(
            is_active=True
        )
        
        for series in active_series:
            # Generate appointments for the next 30 days
            series.generate_upcoming_appointments(days_ahead=30)
        
        logger.info(f"Generated appointments for {active_series.count()} series")
        
    except Exception as e:
        logger.error(f"Error generating appointment series: {e}")


def send_daily_booking_summary():
    """
    Send daily booking summary to service providers
    """
    try:
        from accounts_app.models import ServiceProviderProfile
        from django.template.loader import render_to_string
        
        today = timezone.now().date()
        tomorrow = today + timedelta(days=1)
        
        # Get all service providers
        providers = ServiceProviderProfile.objects.filter(
            user__is_active=True
        )
        
        for provider in providers:
            # Get tomorrow's appointments
            appointments = AppointmentSlot.objects.filter(
                service_provider=provider,
                appointment_date=tomorrow,
                status='booked'
            )
            
            if appointments.exists():
                # Send summary email
                context = {
                    'provider': provider,
                    'appointments': appointments,
                    'date': tomorrow
                }
                
                subject = f"Tomorrow's Appointments - {tomorrow.strftime('%B %d, %Y')}"
                
                html_message = render_to_string(
                    'emails/daily_booking_summary.html',
                    context
                )
                
                send_mail(
                    subject=subject,
                    message='',
                    html_message=html_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[provider.user.email],
                    fail_silently=False
                )
        
        logger.info("Daily booking summaries sent")
        
    except Exception as e:
        logger.error(f"Error sending daily booking summaries: {e}")


def setup_periodic_tasks():
    """
    Set up periodic tasks for the booking system
    Call this from management command or app ready
    """
    try:
        # Clear existing schedules
        Schedule.objects.filter(
            func__startswith='booking_cart_app.tasks.'
        ).delete()
        
        # Process reminders every 15 minutes
        schedule(
            'booking_cart_app.tasks.process_scheduled_reminders',
            schedule_type=Schedule.MINUTES,
            minutes=15,
            name='process_reminders'
        )
        
        # Process workflows every 5 minutes
        schedule(
            'booking_cart_app.tasks.process_scheduled_workflows',
            schedule_type=Schedule.MINUTES,
            minutes=5,
            name='process_workflows'
        )
        
        # Generate appointment series daily at 2 AM
        schedule(
            'booking_cart_app.tasks.generate_appointment_series',
            schedule_type=Schedule.DAILY,
            name='generate_appointments'
        )
        
        # Send daily summaries at 6 PM
        schedule(
            'booking_cart_app.tasks.send_daily_booking_summary',
            schedule_type=Schedule.DAILY,
            name='daily_summaries'
        )
        
        # Cleanup old integrations weekly
        schedule(
            'booking_cart_app.tasks.cleanup_old_calendar_integrations',
            schedule_type=Schedule.WEEKLY,
            name='cleanup_integrations'
        )
        
        # Sync pending calendars every hour
        schedule(
            'booking_cart_app.tasks.sync_all_pending_calendars',
            schedule_type=Schedule.HOURLY,
            name='sync_pending_calendars'
        )
        
        logger.info("Periodic tasks set up successfully")
        
    except Exception as e:
        logger.error(f"Error setting up periodic tasks: {e}")


# Signal handlers for automatic task queuing
def queue_calendar_sync_on_booking_save(sender, instance, created, **kwargs):
    """
    Queue calendar sync when a booking is saved
    """
    if created or instance.status in ['confirmed', 'cancelled']:
        async_task(
            'booking_cart_app.tasks.sync_booking_to_calendar',
            instance.id,
            task_name=f'sync_booking_{instance.id}'
        )


def queue_workflow_on_booking_status_change(sender, instance, **kwargs):
    """
    Queue workflows when booking status changes
    """
    if instance.pk:  # Only for existing instances
        try:
            old_instance = Booking.objects.get(pk=instance.pk)
            if old_instance.status != instance.status:
                # Status changed, trigger workflows
                trigger_type = f'booking_{instance.status}'
                
                workflows = BookingWorkflow.objects.filter(
                    trigger_type=trigger_type,
                    is_active=True
                )
                
                for workflow in workflows:
                    # Calculate execution time based on delay
                    scheduled_time = timezone.now() + workflow.delay
                    
                    execution = WorkflowExecution.objects.create(
                        workflow=workflow,
                        booking=instance,
                        scheduled_time=scheduled_time,
                        status='pending'
                    )
                    
                    if workflow.delay.total_seconds() == 0:
                        # Execute immediately
                        async_task(
                            'booking_cart_app.tasks.execute_booking_workflow',
                            workflow.id,
                            instance.id,
                            execution.id,
                            task_name=f'workflow_{workflow.id}_{instance.id}'
                        )
        except Booking.DoesNotExist:
            pass


# Management functions
def queue_reminder_for_booking(booking: Booking, reminder_type: str = 'email', 
                              hours_before: int = 24) -> BookingReminder:
    """
    Create and queue a reminder for a booking
    """
    reminder_service = ReminderService()
    reminder = reminder_service.create_reminder(booking, reminder_type, hours_before)
    
    # Queue the reminder task
    async_task(
        'booking_cart_app.tasks.send_booking_reminder',
        reminder.id,
        schedule_eta=reminder.scheduled_time,
        task_name=f'reminder_{reminder.id}'
    )
    
    return reminder


def queue_workflow_execution(workflow: BookingWorkflow, booking: Booking, 
                           delay_seconds: int = 0) -> WorkflowExecution:
    """
    Create and queue a workflow execution
    """
    scheduled_time = timezone.now() + timedelta(seconds=delay_seconds)
    
    execution = WorkflowExecution.objects.create(
        workflow=workflow,
        booking=booking,
        scheduled_time=scheduled_time,
        status='pending'
    )
    
    if delay_seconds == 0:
        # Execute immediately
        async_task(
            'booking_cart_app.tasks.execute_booking_workflow',
            workflow.id,
            booking.id,
            execution.id,
            task_name=f'workflow_{workflow.id}_{booking.id}'
        )
    else:
        # Schedule for later
        async_task(
            'booking_cart_app.tasks.execute_booking_workflow',
            workflow.id,
            booking.id,
            execution.id,
            schedule_eta=scheduled_time,
            task_name=f'workflow_{workflow.id}_{booking.id}'
        )
    
    return execution 