"""
Booking Calendar Views
Modern calendar interface with API endpoints for appointment management
"""

import json
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import Q
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render, get_object_or_404
from django.urls import reverse
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.views.generic import TemplateView
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from ..models import (
    Booking, BookingItem, AppointmentSeries, AppointmentSlot, 
    BookingCalendarIntegration, BookingReminder, BookingWorkflow
)
from ..serializers import (
    AppointmentSlotSerializer, BookingSerializer, AppointmentSeriesSerializer
)
from accounts_app.models import ServiceProviderProfile
from venues_app.models import Venue, Service


class CalendarView(LoginRequiredMixin, TemplateView):
    """
    Main calendar interface view
    """
    template_name = 'booking_cart_app/calendar.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': 'Booking Calendar',
            'api_endpoint': reverse('booking_calendar_api'),
            'calendar_settings': {
                'weekStartsOn': 1,  # Monday
                'timeSlotDuration': 30,
                'workingHours': {'start': 9, 'end': 17},
                'enableDragDrop': True,
                'enableModifications': True,
                'locale': 'en-US',
            }
        })
        return context


class CalendarAPIView(LoginRequiredMixin, View):
    """
    Base API view for calendar operations
    """
    def get_user_appointments(self, user, start_date=None, end_date=None):
        """Get appointments for the current user"""
        queryset = AppointmentSlot.objects.select_related(
            'venue', 'service_provider', 'series'
        ).prefetch_related(
            'appointment_bookings'
        )
        
        if user.is_service_provider:
            # Service providers see their own appointments
            queryset = queryset.filter(
                service_provider=user.service_provider_profile
            )
        else:
            # Customers see their booked appointments
            queryset = queryset.filter(
                appointment_bookings__customer=user
            )
        
        if start_date and end_date:
            queryset = queryset.filter(
                appointment_date__range=[start_date, end_date]
            )
        
        return queryset.order_by('appointment_date', 'start_time')


@method_decorator(csrf_exempt, name='dispatch')
class AppointmentListAPIView(CalendarAPIView):
    """
    API endpoint for listing appointments
    """
    def get(self, request, *args, **kwargs):
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        
        # Parse dates if provided
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        appointments = self.get_user_appointments(
            request.user, start_date, end_date
        )
        
        # Serialize appointments
        appointment_data = []
        for appointment in appointments:
            # Get booking information
            booking = None
            if hasattr(appointment, 'appointment_bookings'):
                booking = appointment.appointment_bookings.first()
            
            start_datetime = datetime.combine(
                appointment.appointment_date, 
                appointment.start_time
            )
            end_datetime = start_datetime + appointment.duration
            
            appointment_data.append({
                'id': str(appointment.slot_id),
                'title': f"{appointment.venue.business_name} - {appointment.service_provider.business_name}",
                'customer_name': booking.customer.get_full_name() if booking else 'Available',
                'start_time': start_datetime.isoformat(),
                'end_time': end_datetime.isoformat(),
                'status': appointment.status,
                'venue': appointment.venue.business_name,
                'service_provider': appointment.service_provider.business_name,
                'price': str(appointment.price),
                'max_attendees': appointment.max_attendees,
                'current_attendees': appointment.current_attendees,
                'notes': appointment.notes,
                'is_available': appointment.is_available,
                'can_be_booked': appointment.can_be_booked_by(request.user),
            })
        
        return JsonResponse(appointment_data, safe=False)


@method_decorator(csrf_exempt, name='dispatch')
class AppointmentDetailAPIView(CalendarAPIView):
    """
    API endpoint for individual appointment operations
    """
    def get(self, request, appointment_id, *args, **kwargs):
        """Get appointment details"""
        try:
            appointment = AppointmentSlot.objects.select_related(
                'venue', 'service_provider', 'series'
            ).get(
                slot_id=appointment_id
            )
            
            # Check permissions
            if not self.can_access_appointment(request.user, appointment):
                return JsonResponse(
                    {'error': 'Permission denied'}, 
                    status=403
                )
            
            serializer = AppointmentSlotSerializer(appointment)
            return JsonResponse(serializer.data)
            
        except AppointmentSlot.DoesNotExist:
            return JsonResponse(
                {'error': 'Appointment not found'}, 
                status=404
            )
    
    def post(self, request, appointment_id, *args, **kwargs):
        """Update appointment"""
        try:
            appointment = AppointmentSlot.objects.get(
                slot_id=appointment_id
            )
            
            # Check permissions
            if not self.can_modify_appointment(request.user, appointment):
                return JsonResponse(
                    {'error': 'Permission denied'}, 
                    status=403
                )
            
            data = json.loads(request.body)
            
            # Update appointment fields
            if 'start_time' in data:
                start_time = datetime.fromisoformat(data['start_time'])
                appointment.appointment_date = start_time.date()
                appointment.start_time = start_time.time()
            
            if 'status' in data:
                appointment.status = data['status']
            
            if 'notes' in data:
                appointment.notes = data['notes']
            
            appointment.save()
            
            # Trigger calendar sync if enabled
            self.sync_calendar_integration(appointment)
            
            return JsonResponse({'success': True})
            
        except AppointmentSlot.DoesNotExist:
            return JsonResponse(
                {'error': 'Appointment not found'}, 
                status=404
            )
        except Exception as e:
            return JsonResponse(
                {'error': str(e)}, 
                status=500
            )
    
    def can_access_appointment(self, user, appointment):
        """Check if user can access this appointment"""
        if user.is_service_provider:
            return appointment.service_provider == user.service_provider_profile
        else:
            # Check if user has booking for this appointment
            return appointment.appointment_bookings.filter(
                customer=user
            ).exists()
    
    def can_modify_appointment(self, user, appointment):
        """Check if user can modify this appointment"""
        if user.is_service_provider:
            return appointment.service_provider == user.service_provider_profile
        else:
            # Customers can only modify their own bookings
            return appointment.appointment_bookings.filter(
                customer=user
            ).exists()
    
    def sync_calendar_integration(self, appointment):
        """Sync appointment with external calendars"""
        try:
            # Get or create calendar integration
            bookings = appointment.appointment_bookings.all()
            for booking in bookings:
                integration, created = BookingCalendarIntegration.objects.get_or_create(
                    booking=booking
                )
                
                if integration.sync_status != 'disabled':
                    # Queue calendar sync task
                    from ..tasks import sync_booking_to_calendar
                    sync_booking_to_calendar.delay(booking.id)
        
        except Exception as e:
            print(f"Calendar sync error: {e}")


@method_decorator(csrf_exempt, name='dispatch')
class AppointmentMoveAPIView(CalendarAPIView):
    """
    API endpoint for moving appointments via drag-and-drop
    """
    def post(self, request, appointment_id, *args, **kwargs):
        try:
            appointment = AppointmentSlot.objects.get(
                slot_id=appointment_id
            )
            
            # Check permissions
            if not self.can_modify_appointment(request.user, appointment):
                return JsonResponse(
                    {'error': 'Permission denied'}, 
                    status=403
                )
            
            data = json.loads(request.body)
            new_start_time = datetime.fromisoformat(data['start_time'])
            new_end_time = datetime.fromisoformat(data['end_time'])
            
            # Validate new time slot
            if not self.is_valid_time_slot(appointment, new_start_time, new_end_time):
                return JsonResponse(
                    {'error': 'Invalid time slot'}, 
                    status=400
                )
            
            # Update appointment
            appointment.appointment_date = new_start_time.date()
            appointment.start_time = new_start_time.time()
            appointment.duration = new_end_time - new_start_time
            appointment.save()
            
            # Log the move
            self.log_appointment_move(appointment, request.user)
            
            # Sync with calendar
            self.sync_calendar_integration(appointment)
            
            return JsonResponse({'success': True})
            
        except AppointmentSlot.DoesNotExist:
            return JsonResponse(
                {'error': 'Appointment not found'}, 
                status=404
            )
        except Exception as e:
            return JsonResponse(
                {'error': str(e)}, 
                status=500
            )
    
    def is_valid_time_slot(self, appointment, start_time, end_time):
        """Validate that the new time slot is available"""
        # Check for conflicts with existing appointments
        conflicts = AppointmentSlot.objects.filter(
            service_provider=appointment.service_provider,
            appointment_date=start_time.date(),
            start_time__lt=end_time.time(),
            status__in=['available', 'booked']
        ).exclude(
            slot_id=appointment.slot_id
        )
        
        for conflict in conflicts:
            conflict_start = datetime.combine(
                conflict.appointment_date, 
                conflict.start_time
            )
            conflict_end = conflict_start + conflict.duration
            
            # Check for time overlap
            if (start_time < conflict_end and end_time > conflict_start):
                return False
        
        return True
    
    def log_appointment_move(self, appointment, user):
        """Log appointment move for audit trail"""
        # This could be implemented to track changes
        pass


@method_decorator(csrf_exempt, name='dispatch')
class AppointmentActionAPIView(CalendarAPIView):
    """
    API endpoint for appointment actions (confirm, cancel, etc.)
    """
    def post(self, request, appointment_id, action, *args, **kwargs):
        try:
            appointment = AppointmentSlot.objects.get(
                slot_id=appointment_id
            )
            
            # Check permissions
            if not self.can_modify_appointment(request.user, appointment):
                return JsonResponse(
                    {'error': 'Permission denied'}, 
                    status=403
                )
            
            success = False
            message = ''
            
            if action == 'confirm':
                success, message = self.confirm_appointment(appointment)
            elif action == 'cancel':
                success, message = self.cancel_appointment(appointment)
            elif action == 'complete':
                success, message = self.complete_appointment(appointment)
            else:
                return JsonResponse(
                    {'error': 'Invalid action'}, 
                    status=400
                )
            
            if success:
                # Sync with calendar
                self.sync_calendar_integration(appointment)
                return JsonResponse({'success': True, 'message': message})
            else:
                return JsonResponse(
                    {'error': message}, 
                    status=400
                )
                
        except AppointmentSlot.DoesNotExist:
            return JsonResponse(
                {'error': 'Appointment not found'}, 
                status=404
            )
        except Exception as e:
            return JsonResponse(
                {'error': str(e)}, 
                status=500
            )
    
    def confirm_appointment(self, appointment):
        """Confirm an appointment"""
        if appointment.status != 'available':
            return False, 'Appointment is not available for confirmation'
        
        appointment.status = 'booked'
        appointment.save()
        
        # Trigger confirmation workflow
        self.trigger_workflow('appointment_confirmed', appointment)
        
        return True, 'Appointment confirmed successfully'
    
    def cancel_appointment(self, appointment):
        """Cancel an appointment"""
        if appointment.status == 'cancelled':
            return False, 'Appointment is already cancelled'
        
        appointment.status = 'cancelled'
        appointment.current_attendees = 0
        appointment.save()
        
        # Trigger cancellation workflow
        self.trigger_workflow('appointment_cancelled', appointment)
        
        return True, 'Appointment cancelled successfully'
    
    def complete_appointment(self, appointment):
        """Mark appointment as completed"""
        if appointment.status != 'booked':
            return False, 'Only booked appointments can be completed'
        
        appointment.status = 'completed'
        appointment.save()
        
        # Trigger completion workflow
        self.trigger_workflow('appointment_completed', appointment)
        
        return True, 'Appointment marked as completed'
    
    def trigger_workflow(self, trigger_type, appointment):
        """Trigger automated workflows"""
        workflows = BookingWorkflow.objects.filter(
            trigger_type=trigger_type,
            is_active=True
        )
        
        for workflow in workflows:
            # Queue workflow execution
            from ..tasks import execute_booking_workflow
            execute_booking_workflow.delay(
                workflow.id, 
                appointment.slot_id
            )


@method_decorator(csrf_exempt, name='dispatch')
class AvailabilityAPIView(CalendarAPIView):
    """
    API endpoint for checking availability
    """
    def get(self, request, *args, **kwargs):
        """Get availability for a date range"""
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        service_provider_id = request.GET.get('service_provider_id')
        
        if not start_date or not end_date:
            return JsonResponse(
                {'error': 'start_date and end_date are required'}, 
                status=400
            )
        
        # Parse dates
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        # Get available slots
        queryset = AppointmentSlot.objects.filter(
            appointment_date__range=[start_date, end_date],
            status='available'
        )
        
        if service_provider_id:
            queryset = queryset.filter(
                service_provider_id=service_provider_id
            )
        
        # Serialize availability data
        availability_data = []
        for slot in queryset:
            availability_data.append({
                'id': str(slot.slot_id),
                'date': slot.appointment_date.isoformat(),
                'start_time': slot.start_time.isoformat(),
                'duration': slot.duration.total_seconds() / 60,  # minutes
                'available_spots': slot.available_spots,
                'price': str(slot.price),
                'service_provider': slot.service_provider.business_name,
                'venue': slot.venue.business_name,
            })
        
        return JsonResponse(availability_data, safe=False)


@login_required
def calendar_export(request):
    """
    Export calendar as iCal format
    """
    # Get user's appointments
    calendar_api = CalendarAPIView()
    appointments = calendar_api.get_user_appointments(request.user)
    
    # Generate iCal content
    from ..utils import generate_ical_calendar
    ical_content = generate_ical_calendar(appointments, request.user)
    
    # Return as downloadable file
    response = HttpResponse(ical_content, content_type='text/calendar')
    response['Content-Disposition'] = f'attachment; filename="calendar_{request.user.id}.ics"'
    return response


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def calendar_statistics(request):
    """
    Get calendar statistics for dashboard
    """
    user = request.user
    today = timezone.now().date()
    
    # Get appointments for current month
    month_start = today.replace(day=1)
    month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
    
    appointments = AppointmentSlot.objects.filter(
        appointment_date__range=[month_start, month_end]
    )
    
    if user.is_service_provider:
        appointments = appointments.filter(
            service_provider=user.service_provider_profile
        )
    else:
        appointments = appointments.filter(
            appointment_bookings__customer=user
        )
    
    # Calculate statistics
    stats = {
        'total_appointments': appointments.count(),
        'confirmed_appointments': appointments.filter(status='booked').count(),
        'pending_appointments': appointments.filter(status='available').count(),
        'cancelled_appointments': appointments.filter(status='cancelled').count(),
        'completed_appointments': appointments.filter(status='completed').count(),
        'upcoming_appointments': appointments.filter(
            appointment_date__gte=today,
            status='booked'
        ).count(),
        'revenue_potential': sum(
            float(apt.price) for apt in appointments.filter(status='booked')
        ),
    }
    
    return Response(stats)


# Calendar integration views would go here
# Google Calendar, Outlook, etc. 