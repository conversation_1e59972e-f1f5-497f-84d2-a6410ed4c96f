# Generated by Django 5.2.4 on 2025-07-05 20:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("booking_cart_app", "0001_initial"),
        ("venues_app", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="booking",
            name="venue",
            field=models.ForeignKey(
                help_text="Venue where services will be provided",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="bookings",
                to="venues_app.venue",
            ),
        ),
        migrations.AddField(
            model_name="bookingitem",
            name="booking",
            field=models.ForeignKey(
                help_text="Booking this item belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="booking_cart_app.booking",
            ),
        ),
        migrations.AddField(
            model_name="bookingitem",
            name="service",
            field=models.ForeignKey(
                help_text="Service being booked",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="booking_items",
                to="venues_app.service",
            ),
        ),
        migrations.AddField(
            model_name="bookingstatushistory",
            name="booking",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="status_history",
                to="booking_cart_app.booking",
            ),
        ),
        migrations.AddField(
            model_name="cart",
            name="customer",
            field=models.OneToOneField(
                help_text="Customer who owns this cart",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="cart",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="cartitem",
            name="cart",
            field=models.ForeignKey(
                help_text="Cart this item belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="booking_cart_app.cart",
            ),
        ),
        migrations.AddField(
            model_name="cartitem",
            name="service",
            field=models.ForeignKey(
                help_text="Service being booked",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="cart_items",
                to="venues_app.service",
            ),
        ),
        migrations.AddField(
            model_name="recurringavailabilitypattern",
            name="service",
            field=models.ForeignKey(
                help_text="Service this pattern applies to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="recurring_patterns",
                to="venues_app.service",
            ),
        ),
        migrations.AddField(
            model_name="patterndateexclusion",
            name="pattern",
            field=models.ForeignKey(
                help_text="Pattern to exclude date from",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="date_exclusions",
                to="booking_cart_app.recurringavailabilitypattern",
            ),
        ),
        migrations.AddField(
            model_name="serviceavailability",
            name="recurring_pattern",
            field=models.ForeignKey(
                blank=True,
                help_text="Recurring pattern that generated this slot",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="generated_slots",
                to="booking_cart_app.recurringavailabilitypattern",
            ),
        ),
        migrations.AddField(
            model_name="serviceavailability",
            name="service",
            field=models.ForeignKey(
                help_text="Service this availability is for",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="availability_slots",
                to="venues_app.service",
            ),
        ),
        migrations.AddField(
            model_name="serviceavailabilitytemplate",
            name="service",
            field=models.ForeignKey(
                help_text="Service this template applies to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="availability_templates",
                to="venues_app.service",
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(fields=["status"], name="booking_car_status_545363_idx"),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["booking_date"], name="booking_car_booking_fbece0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["friendly_id"], name="booking_car_friendl_687387_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="bookingitem",
            unique_together={
                ("booking", "service", "scheduled_date", "scheduled_time")
            },
        ),
        migrations.AlterUniqueTogether(
            name="cartitem",
            unique_together={
                ("cart", "service", "selected_date", "selected_time_slot")
            },
        ),
        migrations.AlterUniqueTogether(
            name="patterndateexclusion",
            unique_together={("pattern", "excluded_date")},
        ),
        migrations.AddIndex(
            model_name="serviceavailability",
            index=models.Index(
                fields=["available_date"], name="booking_car_availab_b1cb90_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceavailability",
            index=models.Index(
                fields=["is_recurring"], name="booking_car_is_recu_0625cd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceavailability",
            index=models.Index(
                fields=["recurring_pattern"], name="booking_car_recurri_96e514_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="serviceavailability",
            unique_together={("service", "available_date", "start_time")},
        ),
        migrations.AlterUniqueTogether(
            name="serviceavailabilitytemplate",
            unique_together={("service", "name")},
        ),
    ]
