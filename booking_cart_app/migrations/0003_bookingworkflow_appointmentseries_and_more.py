# Generated by Django 5.2.4 on 2025-07-06 06:03

import datetime
import django.db.models.deletion
import recurrence.fields
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts_app", "0001_initial"),
        ("booking_cart_app", "0002_initial"),
        ("venues_app", "0003_migrate_to_picture_fields"),
    ]

    operations = [
        migrations.CreateModel(
            name="BookingWorkflow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name of the workflow", max_length=200),
                ),
                (
                    "trigger_type",
                    models.CharField(
                        choices=[
                            ("booking_created", "Booking Created"),
                            ("booking_confirmed", "Booking Confirmed"),
                            ("booking_cancelled", "Booking Cancelled"),
                            ("appointment_approaching", "Appointment Approaching"),
                            ("appointment_completed", "Appointment Completed"),
                            ("payment_received", "Payment Received"),
                            ("review_requested", "Review Requested"),
                        ],
                        help_text="Event that triggers this workflow",
                        max_length=30,
                    ),
                ),
                (
                    "action_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("send_email", "Send Email"),
                            ("send_sms", "Send SMS"),
                            ("create_reminder", "Create Reminder"),
                            ("update_status", "Update Status"),
                            ("sync_calendar", "Sync to Calendar"),
                            ("generate_invoice", "Generate Invoice"),
                            ("request_review", "Request Review"),
                        ],
                        help_text="Action to perform when triggered",
                        max_length=30,
                    ),
                ),
                (
                    "delay",
                    models.DurationField(
                        default=datetime.timedelta(0),
                        help_text="Delay before executing the action",
                    ),
                ),
                (
                    "conditions",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="JSON conditions that must be met for workflow to execute",
                    ),
                ),
                (
                    "action_params",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Parameters for the action (templates, settings, etc.)",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this workflow is currently active",
                    ),
                ),
                (
                    "execution_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of times this workflow has been executed",
                    ),
                ),
                (
                    "last_executed",
                    models.DateTimeField(
                        blank=True,
                        help_text="Last time this workflow was executed",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Booking Workflow",
                "verbose_name_plural": "Booking Workflows",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AppointmentSeries",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "series_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier for the appointment series",
                        unique=True,
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="Title for the appointment series", max_length=200
                    ),
                ),
                (
                    "recurrence",
                    recurrence.fields.RecurrenceField(
                        help_text="Recurrence pattern for the appointment series"
                    ),
                ),
                (
                    "base_start_time",
                    models.TimeField(
                        help_text="Default start time for appointments in this series"
                    ),
                ),
                (
                    "base_duration",
                    models.DurationField(
                        default=datetime.timedelta(seconds=3600),
                        help_text="Default duration for appointments in this series",
                    ),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Base price for appointments in this series",
                        max_digits=10,
                    ),
                ),
                (
                    "max_attendees",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="Maximum number of attendees per appointment",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether the series is currently accepting bookings",
                    ),
                ),
                (
                    "series_start_date",
                    models.DateField(help_text="When the appointment series begins"),
                ),
                (
                    "series_end_date",
                    models.DateField(
                        blank=True,
                        help_text="When the appointment series ends (optional)",
                        null=True,
                    ),
                ),
                (
                    "advance_booking_limit",
                    models.DurationField(
                        default=datetime.timedelta(days=30),
                        help_text="How far in advance bookings can be made",
                    ),
                ),
                (
                    "cancellation_deadline",
                    models.DurationField(
                        default=datetime.timedelta(days=1),
                        help_text="Deadline for cancellations before appointment",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "service_provider",
                    models.ForeignKey(
                        help_text="Service provider managing the series",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="appointment_series",
                        to="accounts_app.serviceproviderprofile",
                    ),
                ),
                (
                    "venue",
                    models.ForeignKey(
                        help_text="Venue where the series takes place",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="appointment_series",
                        to="venues_app.venue",
                    ),
                ),
            ],
            options={
                "verbose_name": "Appointment Series",
                "verbose_name_plural": "Appointment Series",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="BookingCalendarIntegration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "google_event_id",
                    models.CharField(
                        blank=True,
                        help_text="Google Calendar event ID",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "google_calendar_id",
                    models.CharField(
                        blank=True,
                        help_text="Google Calendar ID where event is stored",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "ical_uid",
                    models.CharField(
                        blank=True,
                        help_text="iCal UID for this booking",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "last_synced",
                    models.DateTimeField(
                        blank=True,
                        help_text="Last successful sync timestamp",
                        null=True,
                    ),
                ),
                (
                    "sync_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Sync"),
                            ("synced", "Successfully Synced"),
                            ("failed", "Sync Failed"),
                            ("disabled", "Sync Disabled"),
                        ],
                        default="pending",
                        help_text="Current sync status",
                        max_length=20,
                    ),
                ),
                (
                    "sync_error",
                    models.TextField(
                        blank=True, help_text="Error message from last sync attempt"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "booking",
                    models.OneToOneField(
                        help_text="Associated booking",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="calendar_integration",
                        to="booking_cart_app.booking",
                    ),
                ),
            ],
            options={
                "verbose_name": "Booking Calendar Integration",
                "verbose_name_plural": "Booking Calendar Integrations",
            },
        ),
        migrations.CreateModel(
            name="BookingReminder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reminder_type",
                    models.CharField(
                        choices=[
                            ("email", "Email Reminder"),
                            ("sms", "SMS Reminder"),
                            ("push", "Push Notification"),
                            ("system", "In-System Notification"),
                        ],
                        help_text="Type of reminder",
                        max_length=20,
                    ),
                ),
                (
                    "scheduled_time",
                    models.DateTimeField(help_text="When to send the reminder"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("sent", "Sent"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="scheduled",
                        help_text="Current status of the reminder",
                        max_length=20,
                    ),
                ),
                (
                    "message_template",
                    models.CharField(
                        help_text="Template to use for the reminder message",
                        max_length=100,
                    ),
                ),
                (
                    "sent_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the reminder was actually sent",
                        null=True,
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="Error message if sending failed"
                    ),
                ),
                (
                    "task_id",
                    models.CharField(
                        blank=True,
                        help_text="Celery/Django-Q task ID",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "booking",
                    models.ForeignKey(
                        help_text="Associated booking",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reminders",
                        to="booking_cart_app.booking",
                    ),
                ),
            ],
            options={
                "verbose_name": "Booking Reminder",
                "verbose_name_plural": "Booking Reminders",
                "ordering": ["scheduled_time"],
            },
        ),
        migrations.CreateModel(
            name="WorkflowExecution",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        help_text="Current execution status",
                        max_length=20,
                    ),
                ),
                (
                    "scheduled_time",
                    models.DateTimeField(help_text="When the workflow should execute"),
                ),
                (
                    "started_at",
                    models.DateTimeField(
                        blank=True, help_text="When execution started", null=True
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, help_text="When execution completed", null=True
                    ),
                ),
                (
                    "result",
                    models.JSONField(
                        blank=True,
                        help_text="Execution result or error details",
                        null=True,
                    ),
                ),
                (
                    "task_id",
                    models.CharField(
                        blank=True,
                        help_text="Background task ID",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "booking",
                    models.ForeignKey(
                        help_text="Associated booking",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="workflow_executions",
                        to="booking_cart_app.booking",
                    ),
                ),
                (
                    "workflow",
                    models.ForeignKey(
                        help_text="Associated workflow",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="executions",
                        to="booking_cart_app.bookingworkflow",
                    ),
                ),
            ],
            options={
                "verbose_name": "Workflow Execution",
                "verbose_name_plural": "Workflow Executions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AppointmentSlot",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "slot_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier for the appointment slot",
                        unique=True,
                    ),
                ),
                (
                    "appointment_date",
                    models.DateField(help_text="Date of the appointment"),
                ),
                (
                    "start_time",
                    models.TimeField(help_text="Start time of the appointment"),
                ),
                (
                    "duration",
                    models.DurationField(
                        default=datetime.timedelta(seconds=3600),
                        help_text="Duration of the appointment",
                    ),
                ),
                (
                    "max_attendees",
                    models.PositiveIntegerField(
                        default=1, help_text="Maximum number of attendees"
                    ),
                ),
                (
                    "current_attendees",
                    models.PositiveIntegerField(
                        default=0, help_text="Current number of booked attendees"
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Price for this appointment",
                        max_digits=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("available", "Available"),
                            ("booked", "Fully Booked"),
                            ("cancelled", "Cancelled"),
                            ("completed", "Completed"),
                        ],
                        default="available",
                        help_text="Current status of the appointment slot",
                        max_length=20,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Internal notes about this appointment slot",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "series",
                    models.ForeignKey(
                        blank=True,
                        help_text="Parent series (if part of a recurring series)",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="appointment_slots",
                        to="booking_cart_app.appointmentseries",
                    ),
                ),
                (
                    "service_provider",
                    models.ForeignKey(
                        help_text="Service provider for this appointment",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="appointment_slots",
                        to="accounts_app.serviceproviderprofile",
                    ),
                ),
                (
                    "venue",
                    models.ForeignKey(
                        help_text="Venue for this appointment",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="appointment_slots",
                        to="venues_app.venue",
                    ),
                ),
            ],
            options={
                "verbose_name": "Appointment Slot",
                "verbose_name_plural": "Appointment Slots",
                "ordering": ["appointment_date", "start_time"],
                "unique_together": {
                    ("venue", "service_provider", "appointment_date", "start_time")
                },
            },
        ),
    ]
