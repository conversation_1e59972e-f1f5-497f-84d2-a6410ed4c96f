"""
Booking Cart App Serializers
Django REST Framework serializers for the booking calendar API
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Booking, BookingItem, Cart, CartItem, 
    AppointmentSeries, AppointmentSlot, BookingCalendarIntegration,
    BookingReminder, BookingWorkflow, WorkflowExecution,
    ServiceAvailability, RecurringAvailabilityPattern
)
from venues_app.models import Venue, Service
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class BookingItemSerializer(serializers.ModelSerializer):
    """Serializer for booking items"""
    service_title = serializers.CharField(read_only=True)
    venue_name = serializers.CharField(source='service.venue.business_name', read_only=True)
    
    class Meta:
        model = BookingItem
        fields = [
            'id', 'service', 'service_title', 'venue_name', 'service_price',
            'quantity', 'scheduled_date', 'scheduled_time', 'duration_minutes',
            'total_price', 'end_time'
        ]


class BookingSerializer(serializers.ModelSerializer):
    """Serializer for bookings"""
    items = BookingItemSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    venue_name = serializers.CharField(source='venue.business_name', read_only=True)
    service_provider_name = serializers.CharField(source='service_provider.business_name', read_only=True)
    
    class Meta:
        model = Booking
        fields = [
            'booking_id', 'friendly_id', 'customer', 'customer_name',
            'venue', 'venue_name', 'service_provider_name', 'status',
            'total_price', 'booking_date', 'notes', 'items',
            'can_be_cancelled', 'cancellation_deadline'
        ]


class AppointmentSeriesSerializer(serializers.ModelSerializer):
    """Serializer for appointment series"""
    venue_name = serializers.CharField(source='venue.business_name', read_only=True)
    service_provider_name = serializers.CharField(source='service_provider.business_name', read_only=True)
    
    class Meta:
        model = AppointmentSeries
        fields = [
            'series_id', 'title', 'venue', 'venue_name', 'service_provider',
            'service_provider_name', 'recurrence', 'base_start_time',
            'base_duration', 'base_price', 'max_attendees', 'is_active',
            'series_start_date', 'series_end_date', 'advance_booking_limit',
            'cancellation_deadline', 'created_at', 'updated_at'
        ]


class AppointmentSlotSerializer(serializers.ModelSerializer):
    """Serializer for appointment slots"""
    venue_name = serializers.CharField(source='venue.business_name', read_only=True)
    service_provider_name = serializers.CharField(source='service_provider.business_name', read_only=True)
    series_title = serializers.CharField(source='series.title', read_only=True)
    start_datetime = serializers.SerializerMethodField()
    end_datetime = serializers.SerializerMethodField()
    
    class Meta:
        model = AppointmentSlot
        fields = [
            'slot_id', 'series', 'series_title', 'venue', 'venue_name',
            'service_provider', 'service_provider_name', 'appointment_date',
            'start_time', 'duration', 'max_attendees', 'current_attendees',
            'price', 'status', 'notes', 'start_datetime', 'end_datetime',
            'is_available', 'available_spots', 'created_at', 'updated_at'
        ]
    
    def get_start_datetime(self, obj):
        """Get combined start datetime"""
        from datetime import datetime
        return datetime.combine(obj.appointment_date, obj.start_time)
    
    def get_end_datetime(self, obj):
        """Get combined end datetime"""
        from datetime import datetime
        start_dt = datetime.combine(obj.appointment_date, obj.start_time)
        return start_dt + obj.duration


class BookingCalendarIntegrationSerializer(serializers.ModelSerializer):
    """Serializer for booking calendar integration"""
    
    class Meta:
        model = BookingCalendarIntegration
        fields = [
            'id', 'booking', 'google_event_id', 'google_calendar_id',
            'ical_uid', 'last_synced', 'sync_status', 'sync_error',
            'created_at', 'updated_at'
        ]


class BookingReminderSerializer(serializers.ModelSerializer):
    """Serializer for booking reminders"""
    
    class Meta:
        model = BookingReminder
        fields = [
            'id', 'booking', 'reminder_type', 'scheduled_time', 'status',
            'message_template', 'sent_at', 'error_message', 'task_id',
            'created_at', 'updated_at'
        ]


class BookingWorkflowSerializer(serializers.ModelSerializer):
    """Serializer for booking workflows"""
    
    class Meta:
        model = BookingWorkflow
        fields = [
            'id', 'name', 'trigger_type', 'action_type', 'delay',
            'conditions', 'action_params', 'is_active', 'execution_count',
            'last_executed', 'created_at', 'updated_at'
        ]


class WorkflowExecutionSerializer(serializers.ModelSerializer):
    """Serializer for workflow executions"""
    workflow_name = serializers.CharField(source='workflow.name', read_only=True)
    
    class Meta:
        model = WorkflowExecution
        fields = [
            'id', 'workflow', 'workflow_name', 'booking', 'status',
            'scheduled_time', 'started_at', 'completed_at', 'result',
            'task_id', 'created_at'
        ]


class ServiceAvailabilitySerializer(serializers.ModelSerializer):
    """Serializer for service availability"""
    service_name = serializers.CharField(source='service.service_title', read_only=True)
    venue_name = serializers.CharField(source='service.venue.business_name', read_only=True)
    
    class Meta:
        model = ServiceAvailability
        fields = [
            'id', 'service', 'service_name', 'venue_name', 'available_date',
            'start_time', 'end_time', 'max_bookings', 'current_bookings',
            'is_available', 'is_recurring', 'recurring_pattern',
            'is_exception', 'exception_reason', 'is_fully_booked',
            'available_spots', 'duration_minutes', 'created_at', 'updated_at'
        ]


class RecurringAvailabilityPatternSerializer(serializers.ModelSerializer):
    """Serializer for recurring availability patterns"""
    service_name = serializers.CharField(source='service.service_title', read_only=True)
    selected_weekdays = serializers.ListField(read_only=True)
    
    class Meta:
        model = RecurringAvailabilityPattern
        fields = [
            'id', 'service', 'service_name', 'name', 'pattern_type',
            'start_time', 'end_time', 'slot_duration_minutes',
            'break_between_slots', 'monday', 'tuesday', 'wednesday',
            'thursday', 'friday', 'saturday', 'sunday', 'start_date',
            'end_date', 'max_bookings_per_slot', 'is_active',
            'generate_advance_days', 'exclude_holidays', 'selected_weekdays',
            'last_generated', 'created_at', 'updated_at'
        ]


class CartItemSerializer(serializers.ModelSerializer):
    """Serializer for cart items"""
    service_name = serializers.CharField(source='service.service_title', read_only=True)
    venue_name = serializers.CharField(source='service.venue.business_name', read_only=True)
    
    class Meta:
        model = CartItem
        fields = [
            'id', 'service', 'service_name', 'venue_name', 'selected_date',
            'selected_time_slot', 'quantity', 'price_per_item',
            'total_price', 'added_at'
        ]


class CartSerializer(serializers.ModelSerializer):
    """Serializer for shopping carts"""
    items = CartItemSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    
    class Meta:
        model = Cart
        fields = [
            'id', 'customer', 'customer_name', 'created_at', 'expires_at',
            'updated_at', 'is_expired', 'total_items', 'total_price', 'items'
        ]


# Nested serializers for detailed views
class DetailedBookingSerializer(BookingSerializer):
    """Detailed booking serializer with all related data"""
    calendar_integration = BookingCalendarIntegrationSerializer(read_only=True)
    reminders = BookingReminderSerializer(many=True, read_only=True)
    workflow_executions = WorkflowExecutionSerializer(many=True, read_only=True)
    
    class Meta(BookingSerializer.Meta):
        fields = BookingSerializer.Meta.fields + [
            'calendar_integration', 'reminders', 'workflow_executions'
        ]


class DetailedAppointmentSlotSerializer(AppointmentSlotSerializer):
    """Detailed appointment slot serializer with booking information"""
    bookings = BookingSerializer(source='appointment_bookings', many=True, read_only=True)
    
    class Meta(AppointmentSlotSerializer.Meta):
        fields = AppointmentSlotSerializer.Meta.fields + ['bookings']


# Calendar-specific serializers
class CalendarEventSerializer(serializers.Serializer):
    """Serializer for calendar events (unified format)"""
    id = serializers.CharField()
    title = serializers.CharField()
    start = serializers.DateTimeField()
    end = serializers.DateTimeField()
    allDay = serializers.BooleanField(default=False)
    backgroundColor = serializers.CharField(required=False)
    borderColor = serializers.CharField(required=False)
    textColor = serializers.CharField(required=False)
    url = serializers.URLField(required=False)
    extendedProps = serializers.DictField(required=False)


class CalendarConfigSerializer(serializers.Serializer):
    """Serializer for calendar configuration"""
    weekStartsOn = serializers.IntegerField(default=0)
    timeSlotDuration = serializers.IntegerField(default=30)
    workingHours = serializers.DictField(default={'start': 9, 'end': 17})
    enableDragDrop = serializers.BooleanField(default=True)
    enableModifications = serializers.BooleanField(default=True)
    locale = serializers.CharField(default='en-US')
    defaultView = serializers.CharField(default='month')
    showWeekends = serializers.BooleanField(default=True)
    businessHours = serializers.ListField(required=False)
    
    def validate_timeSlotDuration(self, value):
        """Validate time slot duration"""
        if value not in [15, 30, 60]:
            raise serializers.ValidationError("Time slot duration must be 15, 30, or 60 minutes")
        return value
    
    def validate_workingHours(self, value):
        """Validate working hours"""
        if 'start' not in value or 'end' not in value:
            raise serializers.ValidationError("Working hours must have 'start' and 'end' keys")
        
        start = value['start']
        end = value['end']
        
        if not (0 <= start <= 23 and 0 <= end <= 23):
            raise serializers.ValidationError("Working hours must be between 0 and 23")
        
        if start >= end:
            raise serializers.ValidationError("Start time must be before end time")
        
        return value


# Utility serializers
class AvailabilityCheckSerializer(serializers.Serializer):
    """Serializer for availability check requests"""
    service_id = serializers.IntegerField()
    date = serializers.DateField()
    time = serializers.TimeField()
    duration = serializers.IntegerField(default=60)  # minutes
    attendees = serializers.IntegerField(default=1)
    
    def validate_duration(self, value):
        """Validate duration"""
        if value < 15 or value > 480:  # 15 minutes to 8 hours
            raise serializers.ValidationError("Duration must be between 15 and 480 minutes")
        return value


class BookingCreateSerializer(serializers.Serializer):
    """Serializer for creating bookings"""
    appointment_slot_id = serializers.CharField()
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)
    attendees = serializers.IntegerField(default=1)
    
    def validate_attendees(self, value):
        """Validate number of attendees"""
        if value < 1:
            raise serializers.ValidationError("Number of attendees must be at least 1")
        return value


class AppointmentMoveSerializer(serializers.Serializer):
    """Serializer for moving appointments"""
    start_time = serializers.DateTimeField()
    end_time = serializers.DateTimeField()
    
    def validate(self, data):
        """Validate that end time is after start time"""
        if data['end_time'] <= data['start_time']:
            raise serializers.ValidationError("End time must be after start time")
        return data


class CalendarExportSerializer(serializers.Serializer):
    """Serializer for calendar export options"""
    format = serializers.ChoiceField(choices=['ical', 'csv', 'pdf'], default='ical')
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    include_cancelled = serializers.BooleanField(default=False)
    
    def validate(self, data):
        """Validate date range"""
        if data['end_date'] <= data['start_date']:
            raise serializers.ValidationError("End date must be after start date")
        return data 