from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from import_export import resources, fields
from import_export.admin import ImportExportModelAdmin
from import_export.widgets import ForeignKeyWidget

from admin_app.bulk_operations import BookingBulkOperations
from .models import Booking, BookingItem, Cart, CartItem, ServiceAvailability


# --- Import/Export Resources ---
class BookingResource(resources.ModelResource):
    """Resource for importing/exporting Booking data."""

    customer = fields.Field(
        column_name='customer_email',
        attribute='customer',
        widget=ForeignKeyWidget('accounts_app.CustomUser', 'email')
    )

    class Meta:
        model = Booking
        fields = ('id', 'booking_id', 'customer', 'venue', 'status', 'total_price', 'booking_date')
        export_order = ('booking_id', 'customer', 'venue', 'status', 'total_price', 'booking_date')


class CartResource(resources.ModelResource):
    """Resource for importing/exporting Cart data."""

    customer = fields.Field(
        column_name='customer_email',
        attribute='customer',
        widget=ForeignKeyWidget('accounts_app.CustomUser', 'email')
    )

    class Meta:
        model = Cart
        fields = ('id', 'customer', 'created_at', 'expires_at', 'total_items', 'total_price')
        export_order = ('id', 'customer', 'created_at', 'total_items', 'total_price')


@admin.register(Cart)
class CartAdmin(ImportExportModelAdmin):
    """Admin interface for Cart model."""

    resource_class = CartResource

    list_display = ["customer", "created_at", "expires_at", "total_items", "is_expired"]
    list_filter = ["created_at", "expires_at"]
    search_fields = ["customer__email"]
    readonly_fields = [
        "created_at",
        "updated_at",
        "total_items",
        "total_price",
        "is_expired",
    ]

    fieldsets = (
        (_("Cart Information"), {"fields": ("customer", "expires_at")}),
        (
            _("Metadata"),
            {
                "fields": (
                    "created_at",
                    "updated_at",
                    "total_items",
                    "total_price",
                    "is_expired",
                ),
                "classes": ("collapse",),
            },
        ),
    )


@admin.register(CartItem)
class CartItemAdmin(admin.ModelAdmin):
    """Admin interface for CartItem model."""

    list_display = [
        "cart",
        "service",
        "selected_date",
        "selected_time_slot",
        "quantity",
        "total_price",
    ]
    list_filter = ["selected_date", "added_at"]
    search_fields = ["cart__customer__email", "service__service_title"]
    readonly_fields = ["added_at", "total_price", "venue"]

    fieldsets = (
        (
            _("Cart Item Information"),
            {
                "fields": (
                    "cart",
                    "service",
                    "selected_date",
                    "selected_time_slot",
                    "quantity",
                    "price_per_item",
                )
            },
        ),
        (
            _("Metadata"),
            {"fields": ("added_at", "total_price", "venue"), "classes": ("collapse",)},
        ),
    )


@admin.register(Booking)
class BookingAdmin(BookingBulkOperations, ImportExportModelAdmin):
    """Admin interface for Booking model."""

    resource_class = BookingResource

    list_display = [
        "slug",
        "customer",
        "venue",
        "status",
        "total_price",
        "booking_date",
    ]
    list_filter = ["status", "booking_date", "last_status_change"]
    search_fields = ["slug", "booking_id", "customer__email", "venue__venue_name"]
    readonly_fields = [
        "booking_id",
        "slug",
        "booking_date",
        "last_status_change",
        "can_be_cancelled",
        "service_provider",
    ]

    fieldsets = (
        (
            _("Booking Information"),
            {
                "fields": (
                    "booking_id",
                    "slug",
                    "customer",
                    "venue",
                    "status",
                    "total_price",
                )
            },
        ),
        (_("Notes and Reasons"), {"fields": ("notes", "cancellation_reason")}),
        (
            _("Metadata"),
            {
                "fields": (
                    "booking_date",
                    "last_status_change",
                    "can_be_cancelled",
                    "service_provider",
                ),
                "classes": ("collapse",),
            },
        ),
    )


@admin.register(BookingItem)
class BookingItemAdmin(admin.ModelAdmin):
    """Admin interface for BookingItem model."""

    list_display = [
        "booking",
        "service_title",
        "scheduled_date",
        "scheduled_time",
        "quantity",
        "total_price",
    ]
    list_filter = ["scheduled_date", "created_at"]
    search_fields = ["booking__booking_id", "service_title", "booking__customer__email"]
    readonly_fields = ["created_at", "total_price", "end_time"]

    fieldsets = (
        (
            _("Booking Item Information"),
            {
                "fields": (
                    "booking",
                    "service",
                    "service_title",
                    "service_price",
                    "quantity",
                )
            },
        ),
        (
            _("Schedule"),
            {
                "fields": (
                    "scheduled_date",
                    "scheduled_time",
                    "duration_minutes",
                    "end_time",
                )
            },
        ),
        (
            _("Metadata"),
            {"fields": ("created_at", "total_price"), "classes": ("collapse",)},
        ),
    )


@admin.register(ServiceAvailability)
class ServiceAvailabilityAdmin(admin.ModelAdmin):
    """Admin interface for ServiceAvailability model."""

    list_display = [
        "service",
        "available_date",
        "start_time",
        "end_time",
        "current_bookings",
        "max_bookings",
        "is_available",
    ]
    list_filter = ["available_date", "is_available", "created_at"]
    search_fields = ["service__service_title", "service__venue__venue_name"]
    readonly_fields = ["created_at", "updated_at", "is_fully_booked", "available_spots"]

    fieldsets = (
        (
            _("Availability Information"),
            {
                "fields": (
                    "service",
                    "available_date",
                    "start_time",
                    "end_time",
                    "is_available",
                )
            },
        ),
        (
            _("Booking Limits"),
            {
                "fields": (
                    "max_bookings",
                    "current_bookings",
                    "is_fully_booked",
                    "available_spots",
                )
            },
        ),
        (
            _("Metadata"),
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )
