# Generated by Django 5.2.4 on 2025-07-05 20:51

import accounts_app.utils
import django.db.models.deletion
import django.utils.timezone
import phonenumber_field.modelfields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        error_messages={
                            "unique": "A user with this email already exists."
                        },
                        max_length=254,
                        unique=True,
                        verbose_name="email address",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("customer", "Customer"),
                            ("service_provider", "Service Provider"),
                            ("admin", "Admin"),
                        ],
                        default="customer",
                        max_length=20,
                        verbose_name="role",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
                "ordering": ["-date_joined"],
            },
        ),
        migrations.CreateModel(
            name="CustomerProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="last name"
                    ),
                ),
                (
                    "profile_picture",
                    models.ImageField(
                        blank=True,
                        help_text="Profile image uploaded to cloud storage",
                        null=True,
                        upload_to=accounts_app.utils.get_customer_profile_image_path,
                        verbose_name="profile picture",
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        blank=True,
                        choices=[("M", "Male"), ("F", "Female"), ("O", "Other")],
                        max_length=1,
                        verbose_name="gender",
                    ),
                ),
                (
                    "birth_month",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (1, "January"),
                            (2, "February"),
                            (3, "March"),
                            (4, "April"),
                            (5, "May"),
                            (6, "June"),
                            (7, "July"),
                            (8, "August"),
                            (9, "September"),
                            (10, "October"),
                            (11, "November"),
                            (12, "December"),
                        ],
                        null=True,
                        verbose_name="birth month",
                    ),
                ),
                (
                    "birth_year",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        help_text="Format: YYYY",
                        null=True,
                        verbose_name="birth year",
                    ),
                ),
                (
                    "phone_number",
                    phonenumber_field.modelfields.PhoneNumberField(
                        blank=True,
                        help_text="Enter phone number with country code (e.g., ******-123-4567)",
                        max_length=128,
                        region=None,
                        verbose_name="phone number",
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="street address"
                    ),
                ),
                (
                    "city",
                    models.CharField(blank=True, max_length=100, verbose_name="city"),
                ),
                (
                    "zip_code",
                    models.CharField(
                        blank=True, max_length=10, verbose_name="postal code"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="customer_profile",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user account",
                    ),
                ),
            ],
            options={
                "verbose_name": "Customer Profile",
                "verbose_name_plural": "Customer Profiles",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="LoginAlert",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(verbose_name="source IP")),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("multiple_failures", "Multiple Failed Attempts"),
                            ("suspicious_ip", "Suspicious IP Address"),
                            ("unusual_location", "Unusual Login Location"),
                            ("brute_force", "Brute Force Attack"),
                        ],
                        max_length=20,
                        verbose_name="type",
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="severity",
                    ),
                ),
                ("description", models.TextField(verbose_name="details")),
                (
                    "attempt_count",
                    models.PositiveIntegerField(default=1, verbose_name="attempts"),
                ),
                (
                    "is_resolved",
                    models.BooleanField(default=False, verbose_name="resolved"),
                ),
                (
                    "resolved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="resolution time"
                    ),
                ),
                (
                    "resolution_notes",
                    models.TextField(blank=True, verbose_name="resolution notes"),
                ),
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_alerts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="resolved by",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="alerts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="associated user",
                    ),
                ),
            ],
            options={
                "verbose_name": "Security Alert",
                "verbose_name_plural": "Security Alerts",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="LoginHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="timestamp"),
                ),
                ("ip_address", models.GenericIPAddressField(verbose_name="IP address")),
                ("user_agent", models.TextField(blank=True, verbose_name="user agent")),
                (
                    "is_successful",
                    models.BooleanField(default=False, verbose_name="successful"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="login_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Login History",
                "verbose_name_plural": "Login History Records",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="ServiceProviderProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "legal_name",
                    models.CharField(
                        help_text="Official registered business name",
                        max_length=200,
                        verbose_name="legal business name",
                    ),
                ),
                (
                    "display_name",
                    models.CharField(
                        blank=True,
                        help_text="Name shown to customers (if different from legal name)",
                        max_length=200,
                        verbose_name="public display name",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Brief overview of services offered (500 characters max)",
                        max_length=500,
                        verbose_name="business description",
                    ),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        help_text="Company logo displayed on your profile",
                        null=True,
                        upload_to=accounts_app.utils.get_provider_profile_image_path,
                        verbose_name="business logo",
                    ),
                ),
                (
                    "phone",
                    phonenumber_field.modelfields.PhoneNumberField(
                        help_text="Business contact number with country code (e.g., ******-123-4567)",
                        max_length=128,
                        region=None,
                        verbose_name="business phone",
                    ),
                ),
                (
                    "contact_name",
                    models.CharField(
                        help_text="Name of main business contact",
                        max_length=100,
                        verbose_name="primary contact",
                    ),
                ),
                (
                    "address",
                    models.CharField(max_length=255, verbose_name="street address"),
                ),
                ("city", models.CharField(max_length=100, verbose_name="city")),
                (
                    "state",
                    models.CharField(
                        choices=[
                            ("AL", "Alabama"),
                            ("AK", "Alaska"),
                            ("AZ", "Arizona"),
                            ("AR", "Arkansas"),
                            ("CA", "California"),
                            ("CO", "Colorado"),
                            ("CT", "Connecticut"),
                            ("DE", "Delaware"),
                            ("FL", "Florida"),
                            ("GA", "Georgia"),
                            ("HI", "Hawaii"),
                            ("ID", "Idaho"),
                            ("IL", "Illinois"),
                            ("IN", "Indiana"),
                            ("IA", "Iowa"),
                            ("KS", "Kansas"),
                            ("KY", "Kentucky"),
                            ("LA", "Louisiana"),
                            ("ME", "Maine"),
                            ("MD", "Maryland"),
                            ("MA", "Massachusetts"),
                            ("MI", "Michigan"),
                            ("MN", "Minnesota"),
                            ("MS", "Mississippi"),
                            ("MO", "Missouri"),
                            ("MT", "Montana"),
                            ("NE", "Nebraska"),
                            ("NV", "Nevada"),
                            ("NH", "New Hampshire"),
                            ("NJ", "New Jersey"),
                            ("NM", "New Mexico"),
                            ("NY", "New York"),
                            ("NC", "North Carolina"),
                            ("ND", "North Dakota"),
                            ("OH", "Ohio"),
                            ("OK", "Oklahoma"),
                            ("OR", "Oregon"),
                            ("PA", "Pennsylvania"),
                            ("RI", "Rhode Island"),
                            ("SC", "South Carolina"),
                            ("SD", "South Dakota"),
                            ("TN", "Tennessee"),
                            ("TX", "Texas"),
                            ("UT", "Utah"),
                            ("VT", "Vermont"),
                            ("VA", "Virginia"),
                            ("WA", "Washington"),
                            ("WV", "West Virginia"),
                            ("WI", "Wisconsin"),
                            ("WY", "Wyoming"),
                        ],
                        max_length=2,
                        verbose_name="state",
                    ),
                ),
                (
                    "county",
                    models.CharField(blank=True, max_length=100, verbose_name="county"),
                ),
                ("zip_code", models.CharField(max_length=10, verbose_name="ZIP code")),
                (
                    "ein",
                    models.CharField(
                        blank=True,
                        help_text="Employer Identification Number (optional)",
                        max_length=20,
                        verbose_name="EIN number",
                    ),
                ),
                ("website", models.URLField(blank=True, verbose_name="website URL")),
                (
                    "instagram",
                    models.URLField(blank=True, verbose_name="Instagram URL"),
                ),
                ("facebook", models.URLField(blank=True, verbose_name="Facebook URL")),
                (
                    "is_public",
                    models.BooleanField(
                        default=True,
                        help_text="Show business in public listings",
                        verbose_name="public visibility",
                    ),
                ),
                (
                    "venue_creation_tutorial_completed",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user has completed the venue creation guided tour",
                        verbose_name="venue creation tutorial completed",
                    ),
                ),
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_provider_profile",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="business account",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Provider",
                "verbose_name_plural": "Service Providers",
                "ordering": ["-created"],
            },
        ),
        migrations.CreateModel(
            name="TeamMember",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Team member's full name",
                        max_length=100,
                        verbose_name="full name",
                    ),
                ),
                (
                    "position",
                    models.CharField(
                        help_text="Role or job title",
                        max_length=100,
                        verbose_name="position",
                    ),
                ),
                (
                    "photo",
                    models.ImageField(
                        blank=True,
                        help_text="Professional headshot (optional)",
                        null=True,
                        upload_to=accounts_app.utils.get_staff_profile_image_path,
                        verbose_name="profile photo",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Is this team member currently active?",
                        verbose_name="active status",
                    ),
                ),
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "service_provider",
                    models.ForeignKey(
                        help_text="Business this team member belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="team",
                        to="accounts_app.serviceproviderprofile",
                        verbose_name="service provider",
                    ),
                ),
            ],
            options={
                "verbose_name": "Team Member",
                "verbose_name_plural": "Team Members",
                "ordering": ["name"],
            },
        ),
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(fields=["email"], name="accounts_ap_email_71e114_idx"),
        ),
        migrations.AddIndex(
            model_name="customuser",
            index=models.Index(fields=["role"], name="accounts_ap_role_99f07c_idx"),
        ),
        migrations.AddIndex(
            model_name="customerprofile",
            index=models.Index(
                fields=["last_name", "first_name"],
                name="accounts_ap_last_na_ba144e_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="customerprofile",
            index=models.Index(fields=["city"], name="accounts_ap_city_c2488c_idx"),
        ),
        migrations.AddIndex(
            model_name="customerprofile",
            index=models.Index(
                fields=["zip_code"], name="accounts_ap_zip_cod_47ad8c_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="loginalert",
            unique_together={("ip_address", "alert_type", "is_resolved")},
        ),
        migrations.AddIndex(
            model_name="loginhistory",
            index=models.Index(fields=["timestamp"], name="login_timestamp_idx"),
        ),
        migrations.AddIndex(
            model_name="loginhistory",
            index=models.Index(fields=["ip_address"], name="login_ip_idx"),
        ),
        migrations.AddIndex(
            model_name="loginhistory",
            index=models.Index(fields=["is_successful"], name="login_success_idx"),
        ),
        migrations.AddIndex(
            model_name="serviceproviderprofile",
            index=models.Index(
                fields=["legal_name"], name="accounts_ap_legal_n_eb2f9c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceproviderprofile",
            index=models.Index(
                fields=["city", "state"], name="accounts_ap_city_c474b7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceproviderprofile",
            index=models.Index(
                fields=["is_public"], name="accounts_ap_is_publ_05d92d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="teammember",
            index=models.Index(fields=["name"], name="accounts_ap_name_dd1d3f_idx"),
        ),
        migrations.AddIndex(
            model_name="teammember",
            index=models.Index(
                fields=["position"], name="accounts_ap_positio_abafb1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="teammember",
            index=models.Index(
                fields=["is_active"], name="accounts_ap_is_acti_604b15_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="teammember",
            index=models.Index(
                fields=["service_provider", "is_active"],
                name="accounts_ap_service_24baca_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="teammember",
            index=models.Index(
                fields=["service_provider", "name"],
                name="accounts_ap_service_a0d970_idx",
            ),
        ),
        migrations.AddConstraint(
            model_name="teammember",
            constraint=models.UniqueConstraint(
                fields=("service_provider", "name"), name="unique_team_member"
            ),
        ),
    ]
