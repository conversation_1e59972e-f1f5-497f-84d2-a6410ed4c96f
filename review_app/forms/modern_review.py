# --- Third-Party Imports ---
import bleach
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Column, Div, Field, HTML, Layout, Row, Submit
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import Review, ReviewHelpfulness, ReviewResponse
from venues_app.models import Venue


class ModernReviewForm(forms.ModelForm):
    """
    Modern review form with enhanced styling and validation using crispy-forms.
    """

    class Meta:
        model = Review
        fields = ["rating", "written_review"]
        widgets = {
            "written_review": forms.Textarea(
                attrs={
                    "placeholder": _("Share your experience..."),
                    "rows": 5,
                    "maxlength": 1000,
                    "class": "review-form-textarea",
                }
            ),
        }

    def __init__(self, *args, customer=None, venue=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.customer = customer
        self.venue = venue

        # Initialize crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.form_class = "review-form"
        self.helper.form_id = "review-form"

        # Custom layout with modern styling
        self.helper.layout = Layout(
            Div(
                HTML(
                    '<h4 class="review-form-title mb-4">'
                    '<i class="fas fa-star text-warning me-2"></i>'
                    "Share Your Experience"
                    "</h4>"
                ),
                css_class="review-form-header",
            ),
            Row(
                Column(
                    Div(
                        HTML(
                            '<label class="review-form-label">'
                            '<i class="fas fa-star me-2"></i>Your Rating</label>'
                        ),
                        HTML(
                            """
                            <div class="star-rating star-rating-interactive star-rating-lg" id="rating-input">
                                <span class="star" data-rating="1">★</span>
                                <span class="star" data-rating="2">★</span>
                                <span class="star" data-rating="3">★</span>
                                <span class="star" data-rating="4">★</span>
                                <span class="star" data-rating="5">★</span>
                                <span class="star-rating-label ms-3"></span>
                            </div>
                            """
                        ),
                        Field("rating", type="hidden"),
                        css_class="review-form-group rating-group mb-4",
                    ),
                    css_class="col-12",
                ),
            ),
            Row(
                Column(
                    Div(
                        HTML(
                            '<label class="review-form-label" for="id_written_review">'
                            '<i class="fas fa-edit me-2"></i>Your Review</label>'
                        ),
                        Field(
                            "written_review",
                            css_class="form-control review-form-textarea",
                            placeholder=_(
                                "Tell others about your experience. What did you like? What could be improved?"
                            ),
                        ),
                        HTML(
                            '<div class="review-form-help-text mt-2">'
                            '<small class="text-muted">'
                            '<i class="fas fa-info-circle me-1"></i>'
                            "Your review helps others make informed decisions. "
                            "Please be honest and constructive."
                            "</small>"
                            "</div>"
                        ),
                        css_class="review-form-group mb-4",
                    ),
                    css_class="col-12",
                ),
            ),
            Row(
                Column(
                    Div(
                        Submit(
                            "submit",
                            '<i class="fas fa-paper-plane me-2"></i>Submit Review',
                            css_class="btn btn-primary btn-lg review-submit-btn",
                        ),
                        HTML(
                            '<button type="button" class="btn btn-outline-secondary btn-lg ms-2" '
                            'onclick="history.back()">'
                            '<i class="fas fa-times me-2"></i>Cancel'
                            "</button>"
                        ),
                        css_class="review-form-actions d-flex gap-2",
                    ),
                    css_class="col-12",
                ),
            ),
        )

        # Customize field properties
        self.fields["rating"].required = True
        self.fields["rating"].widget = forms.HiddenInput()
        self.fields["written_review"].required = False
        self.fields["written_review"].help_text = _(
            "Optional: Share details about your experience (up to 1000 characters)"
        )

    def clean_rating(self):
        """Enhanced rating validation."""
        rating = self.cleaned_data.get("rating")

        if not rating:
            raise ValidationError(_("Please select a rating between 1 and 5 stars."))

        try:
            rating = int(rating)
        except (ValueError, TypeError):
            raise ValidationError(_("Invalid rating value."))

        if rating < 1 or rating > 5:
            raise ValidationError(_("Rating must be between 1 and 5 stars."))

        return rating

    def clean_written_review(self):
        """Enhanced review content validation."""
        written_review = self.cleaned_data.get("written_review")

        if not written_review:
            return ""

        # Remove leading/trailing whitespace
        written_review = written_review.strip()

        # If provided, ensure minimum length
        if written_review and len(written_review) < 10:
            raise ValidationError(
                _("If provided, review must be at least 10 characters long.")
            )

        # Content moderation - enhanced version
        inappropriate_patterns = [
            "spam",
            "fake",
            "scam",
            "hate",
            "discrimination",
            "harassment",
        ]

        review_lower = written_review.lower()
        for pattern in inappropriate_patterns:
            if pattern in review_lower:
                raise ValidationError(
                    _(
                        "Please keep your review professional and appropriate. "
                        "Avoid inappropriate language or accusations."
                    )
                )

        # Character limit validation
        if len(written_review) > 1000:
            raise ValidationError(_("Review must be 1000 characters or less."))

        # HTML sanitization
        allowed_tags = []  # No HTML tags allowed
        cleaned_review = bleach.clean(written_review, tags=allowed_tags, strip=True)

        # Check for excessive repetition
        words = cleaned_review.split()
        if len(words) > 5:
            unique_words = set(word.lower() for word in words)
            if len(unique_words) / len(words) < 0.3:  # Less than 30% unique words
                raise ValidationError(
                    _("Please write a more detailed and varied review.")
                )

        return cleaned_review

    def clean(self):
        """Form-level validation."""
        cleaned_data = super().clean()
        customer = self.customer
        venue = self.venue
        rating = cleaned_data.get("rating")
        written_review = cleaned_data.get("written_review")

        # Check if customer can review this venue
        if customer and venue:
            # Ensure customer hasn't already reviewed this venue
            if (
                Review.objects.filter(customer=customer, venue=venue)
                .exclude(pk=self.instance.pk if self.instance.pk else None)
                .exists()
            ):
                raise ValidationError(_("You have already reviewed this venue."))

            # Ensure customer has a booking for this venue (optional business rule)
            # Uncomment if you want to enforce this rule
            # from booking_cart_app.models import Booking
            # if not Booking.objects.filter(customer=customer, venue=venue).exists():
            #     raise ValidationError(_("You can only review venues you have booked."))

        # Ensure at least rating is provided
        if not rating:
            raise ValidationError(_("Please provide a rating."))

        return cleaned_data

    def save(self, commit=True):
        """Enhanced save method."""
        review = super().save(commit=False)

        if self.customer:
            review.customer = self.customer
        if self.venue:
            review.venue = self.venue

        if commit:
            review.save()

        return review


class ReviewHelpfulnessForm(forms.ModelForm):
    """
    Form for marking reviews as helpful or not helpful.
    """

    class Meta:
        model = ReviewHelpfulness
        fields = ["is_helpful"]

    def __init__(self, *args, user=None, review=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
        self.review = review

        # Initialize crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.form_class = "review-helpfulness-form"
        self.helper.form_show_labels = False

    def clean(self):
        """Validate helpfulness vote."""
        cleaned_data = super().clean()
        
        if self.user and self.review:
            # Check if user has already voted on this review
            existing_vote = ReviewHelpfulness.objects.filter(
                user=self.user, review=self.review
            ).exclude(pk=self.instance.pk if self.instance.pk else None)
            
            if existing_vote.exists():
                raise ValidationError(_("You have already voted on this review."))

            # Users cannot vote on their own reviews
            if self.review.customer == self.user:
                raise ValidationError(_("You cannot vote on your own review."))

        return cleaned_data

    def save(self, commit=True):
        """Save helpfulness vote."""
        vote = super().save(commit=False)

        if self.user:
            vote.user = self.user
        if self.review:
            vote.review = self.review

        if commit:
            vote.save()

        return vote


class ReviewResponseForm(forms.ModelForm):
    """
    Form for venue providers to respond to reviews.
    """

    class Meta:
        model = ReviewResponse
        fields = ["response_text"]
        widgets = {
            "response_text": forms.Textarea(
                attrs={
                    "placeholder": _(
                        "Thank you for your review. We appreciate your feedback..."
                    ),
                    "rows": 4,
                    "maxlength": 500,
                    "class": "form-control",
                }
            ),
        }

    def __init__(self, *args, provider=None, review=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.provider = provider
        self.review = review

        # Initialize crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = "post"
        self.helper.form_class = "review-response-form"

        self.helper.layout = Layout(
            Div(
                HTML(
                    '<h5 class="response-form-title mb-3">'
                    '<i class="fas fa-reply me-2"></i>'
                    "Respond to Review"
                    "</h5>"
                ),
                Field(
                    "response_text",
                    placeholder=_(
                        "Thank you for your feedback. We value all customer input..."
                    ),
                ),
                HTML(
                    '<small class="form-text text-muted mt-2">'
                    '<i class="fas fa-info-circle me-1"></i>'
                    "Your response will be public and help show your commitment to customer service."
                    "</small>"
                ),
                Submit(
                    "submit",
                    '<i class="fas fa-paper-plane me-2"></i>Submit Response',
                    css_class="btn btn-primary mt-3",
                ),
                css_class="response-form-content",
            ),
        )

    def clean_response_text(self):
        """Validate response text."""
        response_text = self.cleaned_data.get("response_text")

        if not response_text or not response_text.strip():
            raise ValidationError(_("Response text is required."))

        response_text = response_text.strip()

        # Minimum length
        if len(response_text) < 20:
            raise ValidationError(_("Response must be at least 20 characters long."))

        # Maximum length
        if len(response_text) > 500:
            raise ValidationError(_("Response must be 500 characters or less."))

        # Basic content filtering
        inappropriate_words = ["fake", "liar", "stupid", "idiot"]
        response_lower = response_text.lower()
        for word in inappropriate_words:
            if word in response_lower:
                raise ValidationError(
                    _("Please keep your response professional and respectful.")
                )

        # HTML sanitization
        cleaned_response = bleach.clean(response_text, tags=[], strip=True)

        return cleaned_response

    def clean(self):
        """Form-level validation."""
        cleaned_data = super().clean()

        if self.provider and self.review:
            # Ensure provider owns the venue being reviewed
            if self.review.venue.service_provider != self.provider:
                raise ValidationError(
                    _("You can only respond to reviews for your own venue.")
                )

            # Check if response already exists
            from ..models import ReviewResponse
            if (
                ReviewResponse.objects.filter(review=self.review)
                .exclude(pk=self.instance.pk if self.instance.pk else None)
                .exists()
            ):
                raise ValidationError(_("A response to this review already exists."))

        return cleaned_data

    def save(self, commit=True):
        """Save review response."""
        response = super().save(commit=False)

        if self.provider:
            response.service_provider = self.provider
        if self.review:
            response.review = self.review

        if commit:
            response.save()

        return response 