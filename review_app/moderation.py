# --- Third-Party Imports ---
import re
from typing import Dict, List, Optional, Tuple

from django.conf import settings
from django.core.cache import cache
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .models import Review, ReviewFlag


class ContentModerationService:
    """
    Modern content moderation service for review system.
    Provides automated content filtering and manual moderation tools.
    """

    # Inappropriate content patterns
    SPAM_PATTERNS = [
        r'\b(spam|click here|visit our|buy now|limited time)\b',
        r'\b(www\.|http|\.com|\.net|\.org)\b',
        r'\b(\$\d+|\d+\$|free money|earn \$)\b',
        r'\b(urgent|act now|limited offer)\b',
    ]

    PROFANITY_PATTERNS = [
        # Add appropriate patterns for your use case
        r'\b(inappropriate|offensive)\b',  # Placeholder patterns
    ]

    FAKE_REVIEW_PATTERNS = [
        r'\b(fake|paid|bought|purchased) review\b',
        r'\b(never been|never visited|never used)\b',
        r'\b(competitor|rival business)\b',
    ]

    HARASSMENT_PATTERNS = [
        r'\b(hate|harassment|discrimination)\b',
        r'\b(racist|sexist|homophobic)\b',
        r'\b(threat|harm|violence)\b',
    ]

    def __init__(self):
        self.cache_timeout = getattr(settings, 'REVIEW_MODERATION_CACHE_TIMEOUT', 3600)

    def moderate_review(self, review: Review) -> Dict:
        """
        Perform comprehensive content moderation on a review.
        
        Args:
            review: Review instance to moderate
            
        Returns:
            Dict with moderation results
        """
        content = f"{review.written_review or ''}"
        
        # Get cached result if available
        cache_key = f"review_moderation_{review.id}_{hash(content)}"
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result

        # Perform moderation checks
        result = {
            'approved': True,
            'flagged': False,
            'issues': [],
            'confidence': 1.0,
            'auto_action': 'approve',
            'reasons': []
        }

        # Run all moderation checks
        checks = [
            self._check_spam_content,
            self._check_profanity,
            self._check_fake_review_indicators,
            self._check_harassment,
            self._check_repetitive_content,
            self._check_length_quality,
            self._check_rating_text_mismatch,
        ]

        for check in checks:
            check_result = check(review, content)
            if check_result['flagged']:
                result['issues'].extend(check_result['issues'])
                result['confidence'] *= check_result['confidence']
                result['reasons'].extend(check_result['reasons'])

        # Determine final action based on severity
        severity_score = len(result['issues'])
        if severity_score >= 3:
            result['auto_action'] = 'reject'
            result['approved'] = False
            result['flagged'] = True
        elif severity_score >= 1:
            result['auto_action'] = 'flag'
            result['flagged'] = True
        else:
            result['auto_action'] = 'approve'

        # Cache result
        cache.set(cache_key, result, self.cache_timeout)
        
        return result

    def _check_spam_content(self, review: Review, content: str) -> Dict:
        """Check for spam content patterns."""
        issues = []
        reasons = []
        
        for pattern in self.SPAM_PATTERNS:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append('spam_content')
                reasons.append(f'Potential spam pattern detected')
                break

        # Check for excessive external links
        link_count = len(re.findall(r'http[s]?://|www\.|\.\w{2,4}', content))
        if link_count > 2:
            issues.append('excessive_links')
            reasons.append('Contains too many external links')

        # Check for excessive capitalization
        caps_ratio = sum(1 for c in content if c.isupper()) / max(len(content), 1)
        if caps_ratio > 0.3 and len(content) > 20:
            issues.append('excessive_caps')
            reasons.append('Excessive use of capital letters')

        return {
            'flagged': len(issues) > 0,
            'issues': issues,
            'confidence': 0.8 if issues else 1.0,
            'reasons': reasons
        }

    def _check_profanity(self, review: Review, content: str) -> Dict:
        """Check for inappropriate language."""
        issues = []
        reasons = []
        
        for pattern in self.PROFANITY_PATTERNS:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append('inappropriate_language')
                reasons.append('Contains inappropriate language')
                break

        return {
            'flagged': len(issues) > 0,
            'issues': issues,
            'confidence': 0.9 if issues else 1.0,
            'reasons': reasons
        }

    def _check_fake_review_indicators(self, review: Review, content: str) -> Dict:
        """Check for indicators of fake reviews."""
        issues = []
        reasons = []
        
        for pattern in self.FAKE_REVIEW_PATTERNS:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append('fake_review_indicator')
                reasons.append('Contains fake review indicators')
                break

        # Check review age vs account age
        if hasattr(review.customer, 'date_joined'):
            account_age = (review.created_at - review.customer.date_joined).days
            if account_age < 1:  # Account created same day as review
                issues.append('new_account_review')
                reasons.append('Review from very new account')

        # Check for generic/template-like content
        generic_phrases = [
            'great service', 'highly recommend', 'will come back',
            'amazing experience', 'perfect place', 'excellent staff'
        ]
        generic_count = sum(1 for phrase in generic_phrases 
                          if phrase in content.lower())
        
        if generic_count >= 3 and len(content.split()) < 50:
            issues.append('generic_content')
            reasons.append('Contains generic template-like phrases')

        return {
            'flagged': len(issues) > 0,
            'issues': issues,
            'confidence': 0.7 if issues else 1.0,
            'reasons': reasons
        }

    def _check_harassment(self, review: Review, content: str) -> Dict:
        """Check for harassment or discriminatory content."""
        issues = []
        reasons = []
        
        for pattern in self.HARASSMENT_PATTERNS:
            if re.search(pattern, content, re.IGNORECASE):
                issues.append('harassment')
                reasons.append('Contains harassment or discriminatory language')
                break

        return {
            'flagged': len(issues) > 0,
            'issues': issues,
            'confidence': 0.95 if issues else 1.0,
            'reasons': reasons
        }

    def _check_repetitive_content(self, review: Review, content: str) -> Dict:
        """Check for repetitive or low-quality content."""
        issues = []
        reasons = []
        
        if not content.strip():
            return {'flagged': False, 'issues': [], 'confidence': 1.0, 'reasons': []}

        words = content.lower().split()
        if len(words) > 5:
            # Check for excessive word repetition
            word_counts = {}
            for word in words:
                word_counts[word] = word_counts.get(word, 0) + 1
            
            most_common_count = max(word_counts.values())
            if most_common_count > len(words) * 0.4:  # 40% repetition
                issues.append('repetitive_content')
                reasons.append('Contains excessive word repetition')

        # Check for very short content with high rating
        if len(content.strip()) < 10 and review.rating >= 4:
            issues.append('suspiciously_short')
            reasons.append('Very short content for high rating')

        return {
            'flagged': len(issues) > 0,
            'issues': issues,
            'confidence': 0.6 if issues else 1.0,
            'reasons': reasons
        }

    def _check_length_quality(self, review: Review, content: str) -> Dict:
        """Check review length and quality indicators."""
        issues = []
        reasons = []
        
        if not content.strip():
            return {'flagged': False, 'issues': [], 'confidence': 1.0, 'reasons': []}

        # Check for extremely long reviews (potential spam)
        if len(content) > 2000:
            issues.append('excessively_long')
            reasons.append('Review is excessively long')

        # Check for lack of punctuation in long text
        if len(content) > 100:
            punctuation_count = sum(1 for c in content if c in '.,!?;:')
            if punctuation_count == 0:
                issues.append('poor_formatting')
                reasons.append('Long text with no punctuation')

        return {
            'flagged': len(issues) > 0,
            'issues': issues,
            'confidence': 0.5 if issues else 1.0,
            'reasons': reasons
        }

    def _check_rating_text_mismatch(self, review: Review, content: str) -> Dict:
        """Check for mismatch between rating and review text sentiment."""
        issues = []
        reasons = []
        
        if not content.strip():
            return {'flagged': False, 'issues': [], 'confidence': 1.0, 'reasons': []}

        # Simple sentiment analysis based on keywords
        positive_words = [
            'great', 'excellent', 'amazing', 'wonderful', 'fantastic',
            'love', 'perfect', 'best', 'awesome', 'outstanding'
        ]
        negative_words = [
            'terrible', 'awful', 'horrible', 'worst', 'hate',
            'disgusting', 'disappointing', 'poor', 'bad', 'rude'
        ]

        content_lower = content.lower()
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)

        # Check for high rating with negative sentiment
        if review.rating >= 4 and negative_count > positive_count and negative_count >= 2:
            issues.append('rating_sentiment_mismatch')
            reasons.append('High rating but negative sentiment in text')

        # Check for low rating with positive sentiment
        if review.rating <= 2 and positive_count > negative_count and positive_count >= 2:
            issues.append('rating_sentiment_mismatch')
            reasons.append('Low rating but positive sentiment in text')

        return {
            'flagged': len(issues) > 0,
            'issues': issues,
            'confidence': 0.6 if issues else 1.0,
            'reasons': reasons
        }

    def apply_moderation_result(self, review: Review, moderation_result: Dict) -> None:
        """
        Apply moderation result to review.
        
        Args:
            review: Review instance
            moderation_result: Result from moderate_review()
        """
        review.is_approved = moderation_result['approved']
        review.is_flagged = moderation_result['flagged']
        
        # Store moderation metadata
        if hasattr(review, '_moderation_data'):
            review._moderation_data = moderation_result
        
        review.save(update_fields=['is_approved', 'is_flagged'])

        # Create flag if issues found
        if moderation_result['flagged'] and moderation_result['reasons']:
            self._create_moderation_flag(review, moderation_result)

    def _create_moderation_flag(self, review: Review, moderation_result: Dict) -> None:
        """Create a flag for moderated content."""
        try:
            # Create a system flag for moderation
            flag_reason = ReviewFlag.OTHER
            if 'spam_content' in moderation_result['issues']:
                flag_reason = ReviewFlag.SPAM
            elif 'inappropriate_language' in moderation_result['issues']:
                flag_reason = ReviewFlag.OFFENSIVE_LANGUAGE
            elif 'fake_review_indicator' in moderation_result['issues']:
                flag_reason = ReviewFlag.FAKE_REVIEW
            elif 'harassment' in moderation_result['issues']:
                flag_reason = ReviewFlag.INAPPROPRIATE_CONTENT

            # Use system user or the review customer as fallback
            flagged_by = getattr(settings, 'SYSTEM_USER', review.customer)

            ReviewFlag.objects.create(
                review=review,
                flagged_by=flagged_by,
                reason=flag_reason,
                reason_text='; '.join(moderation_result['reasons'][:3]),  # Limit length
                status=ReviewFlag.PENDING
            )
        except Exception as e:
            # Log error but don't fail the moderation process
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to create moderation flag: {e}")

    def bulk_moderate_reviews(self, reviews: List[Review]) -> Dict:
        """
        Moderate multiple reviews in bulk.
        
        Args:
            reviews: List of Review instances
            
        Returns:
            Dict with bulk moderation results
        """
        results = {
            'processed': 0,
            'approved': 0,
            'flagged': 0,
            'rejected': 0,
            'errors': []
        }

        for review in reviews:
            try:
                moderation_result = self.moderate_review(review)
                self.apply_moderation_result(review, moderation_result)
                
                results['processed'] += 1
                if moderation_result['auto_action'] == 'approve':
                    results['approved'] += 1
                elif moderation_result['auto_action'] == 'flag':
                    results['flagged'] += 1
                elif moderation_result['auto_action'] == 'reject':
                    results['rejected'] += 1
                    
            except Exception as e:
                results['errors'].append(f"Review {review.id}: {str(e)}")

        return results

    def get_moderation_stats(self) -> Dict:
        """Get moderation statistics."""
        cache_key = 'review_moderation_stats'
        stats = cache.get(cache_key)
        
        if not stats:
            stats = {
                'total_reviews': Review.objects.count(),
                'flagged_reviews': Review.objects.filter(is_flagged=True).count(),
                'pending_reviews': Review.objects.filter(is_approved=False).count(),
                'approved_reviews': Review.objects.filter(is_approved=True, is_flagged=False).count(),
                'flags_pending': ReviewFlag.objects.filter(status=ReviewFlag.PENDING).count(),
                'flags_resolved': ReviewFlag.objects.filter(status=ReviewFlag.RESOLVED).count(),
            }
            cache.set(cache_key, stats, 300)  # Cache for 5 minutes
        
        return stats


# Global instance
moderation_service = ContentModerationService()


def moderate_review_content(review: Review) -> Dict:
    """
    Convenience function to moderate a single review.
    
    Args:
        review: Review instance to moderate
        
    Returns:
        Moderation result dictionary
    """
    return moderation_service.moderate_review(review) 