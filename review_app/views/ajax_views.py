# --- Third-Party Imports ---
import json
from django.contrib.auth.decorators import login_required
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

# --- Local App Imports ---
from ..forms.modern_review import ReviewHelpfulnessForm
from ..models import Review, ReviewFlag, ReviewHelpfulness
from ..moderation import moderation_service
from .common import is_customer


@login_required
@require_http_methods(["POST"])
def review_helpfulness_vote(request):
    """
    AJAX endpoint for voting on review helpfulness.
    """
    try:
        data = json.loads(request.body)
        review_id = data.get('review_id')
        is_helpful = data.get('is_helpful', True)
        
        if not review_id:
            return JsonResponse({
                'success': False,
                'message': _('Review ID is required')
            }, status=400)

        review = get_object_or_404(Review, id=review_id, is_approved=True)
        
        # Users cannot vote on their own reviews
        if review.customer == request.user:
            return JsonResponse({
                'success': False,
                'message': _('You cannot vote on your own review')
            }, status=403)

        with transaction.atomic():
            # Check if user has already voted
            existing_vote = ReviewHelpfulness.objects.filter(
                user=request.user,
                review=review
            ).first()

            if existing_vote:
                # Update existing vote
                if existing_vote.is_helpful != is_helpful:
                    existing_vote.is_helpful = is_helpful
                    existing_vote.save()
                    message = _('Vote updated successfully')
                else:
                    # Remove vote if clicking same option
                    existing_vote.delete()
                    message = _('Vote removed')
            else:
                # Create new vote
                ReviewHelpfulness.objects.create(
                    user=request.user,
                    review=review,
                    is_helpful=is_helpful
                )
                message = _('Vote recorded successfully')

        # Get updated counts
        helpful_count = review.helpfulness_votes.filter(is_helpful=True).count()
        not_helpful_count = review.helpfulness_votes.filter(is_helpful=False).count()
        total_votes = helpful_count + not_helpful_count
        helpfulness_ratio = (helpful_count / total_votes * 100) if total_votes > 0 else 0

        return JsonResponse({
            'success': True,
            'message': message,
            'helpful_count': helpful_count,
            'not_helpful_count': not_helpful_count,
            'total_votes': total_votes,
            'helpfulness_ratio': round(helpfulness_ratio, 1),
            'user_vote': is_helpful if not existing_vote or existing_vote.is_helpful == is_helpful else None
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': _('An error occurred. Please try again.')
        }, status=500)


@login_required
@require_http_methods(["POST"])
def flag_review(request):
    """
    AJAX endpoint for flagging inappropriate reviews.
    """
    try:
        data = json.loads(request.body)
        review_id = data.get('review_id')
        reason = data.get('reason', ReviewFlag.OTHER)
        reason_text = data.get('reason_text', '').strip()
        
        if not review_id:
            return JsonResponse({
                'success': False,
                'message': _('Review ID is required')
            }, status=400)

        review = get_object_or_404(Review, id=review_id)
        
        # Users cannot flag their own reviews
        if review.customer == request.user:
            return JsonResponse({
                'success': False,
                'message': _('You cannot flag your own review')
            }, status=403)

        # Check if user has already flagged this review
        existing_flag = ReviewFlag.objects.filter(
            review=review,
            flagged_by=request.user
        ).first()

        if existing_flag:
            return JsonResponse({
                'success': False,
                'message': _('You have already flagged this review')
            }, status=409)

        # Validate reason
        valid_reasons = dict(ReviewFlag.REASON_CHOICES).keys()
        if reason not in valid_reasons:
            reason = ReviewFlag.OTHER

        # Create flag
        with transaction.atomic():
            flag = ReviewFlag.objects.create(
                review=review,
                flagged_by=request.user,
                reason=reason,
                reason_text=reason_text[:500],  # Limit length
                status=ReviewFlag.PENDING
            )

            # Update review flag status if multiple flags
            flag_count = review.flags.filter(status=ReviewFlag.PENDING).count()
            if flag_count >= 3:  # Auto-flag after 3 reports
                review.is_flagged = True
                review.save(update_fields=['is_flagged'])

        return JsonResponse({
            'success': True,
            'message': _('Review flagged successfully. Thank you for helping maintain our community standards.'),
            'flag_count': flag_count
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': _('An error occurred. Please try again.')
        }, status=500)


@login_required
@require_http_methods(["POST"])
def moderate_review_ajax(request):
    """
    AJAX endpoint for automated review moderation.
    """
    if not request.user.is_staff:
        return JsonResponse({
            'success': False,
            'message': _('Permission denied')
        }, status=403)

    try:
        data = json.loads(request.body)
        review_id = data.get('review_id')
        
        if not review_id:
            return JsonResponse({
                'success': False,
                'message': _('Review ID is required')
            }, status=400)

        review = get_object_or_404(Review, id=review_id)
        
        # Run moderation
        moderation_result = moderation_service.moderate_review(review)
        moderation_service.apply_moderation_result(review, moderation_result)

        return JsonResponse({
            'success': True,
            'message': _('Review moderated successfully'),
            'moderation_result': {
                'approved': moderation_result['approved'],
                'flagged': moderation_result['flagged'],
                'auto_action': moderation_result['auto_action'],
                'issues': moderation_result['issues'],
                'confidence': round(moderation_result['confidence'], 2),
                'reasons': moderation_result['reasons']
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': _('An error occurred during moderation.')
        }, status=500)


@login_required
@require_http_methods(["GET"])
def review_stats_ajax(request, review_id):
    """
    AJAX endpoint for getting real-time review statistics.
    """
    try:
        review = get_object_or_404(Review, id=review_id, is_approved=True)
        
        # Get helpfulness stats
        helpful_votes = review.helpfulness_votes.filter(is_helpful=True).count()
        not_helpful_votes = review.helpfulness_votes.filter(is_helpful=False).count()
        total_votes = helpful_votes + not_helpful_votes
        helpfulness_ratio = (helpful_votes / total_votes * 100) if total_votes > 0 else 0

        # Get user's vote if logged in
        user_vote = None
        if request.user.is_authenticated:
            user_vote_obj = review.helpfulness_votes.filter(user=request.user).first()
            if user_vote_obj:
                user_vote = user_vote_obj.is_helpful

        # Get flag count (for admins)
        flag_count = review.flags.filter(status=ReviewFlag.PENDING).count() if request.user.is_staff else 0

        return JsonResponse({
            'success': True,
            'stats': {
                'helpful_votes': helpful_votes,
                'not_helpful_votes': not_helpful_votes,
                'total_votes': total_votes,
                'helpfulness_ratio': round(helpfulness_ratio, 1),
                'user_vote': user_vote,
                'flag_count': flag_count,
                'is_flagged': review.is_flagged,
                'is_approved': review.is_approved
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': _('Could not load review statistics')
        }, status=500)


@login_required
@require_http_methods(["POST"])
def bulk_moderate_reviews(request):
    """
    AJAX endpoint for bulk review moderation.
    """
    if not request.user.is_staff:
        return JsonResponse({
            'success': False,
            'message': _('Permission denied')
        }, status=403)

    try:
        data = json.loads(request.body)
        review_ids = data.get('review_ids', [])
        action = data.get('action', 'moderate')  # moderate, approve, reject, flag
        
        if not review_ids:
            return JsonResponse({
                'success': False,
                'message': _('No reviews selected')
            }, status=400)

        reviews = Review.objects.filter(id__in=review_ids)
        
        if action == 'moderate':
            results = moderation_service.bulk_moderate_reviews(reviews)
        elif action == 'approve':
            with transaction.atomic():
                updated_count = reviews.update(is_approved=True, is_flagged=False)
            results = {
                'processed': updated_count,
                'approved': updated_count,
                'flagged': 0,
                'rejected': 0,
                'errors': []
            }
        elif action == 'reject':
            with transaction.atomic():
                updated_count = reviews.update(is_approved=False, is_flagged=True)
            results = {
                'processed': updated_count,
                'approved': 0,
                'flagged': 0,
                'rejected': updated_count,
                'errors': []
            }
        elif action == 'flag':
            with transaction.atomic():
                updated_count = reviews.update(is_flagged=True)
            results = {
                'processed': updated_count,
                'approved': 0,
                'flagged': updated_count,
                'rejected': 0,
                'errors': []
            }
        else:
            return JsonResponse({
                'success': False,
                'message': _('Invalid action')
            }, status=400)

        return JsonResponse({
            'success': True,
            'message': _('Bulk moderation completed'),
            'results': results
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': _('An error occurred during bulk moderation.')
        }, status=500)


@login_required
@require_http_methods(["GET"])
def load_more_reviews(request):
    """
    AJAX endpoint for infinite scroll loading of reviews.
    """
    try:
        venue_id = request.GET.get('venue_id')
        page = int(request.GET.get('page', 2))
        rating_filter = request.GET.get('rating')
        sort_by = request.GET.get('sort', 'newest')
        
        if not venue_id:
            return JsonResponse({
                'success': False,
                'message': _('Venue ID is required')
            }, status=400)

        from django.core.paginator import Paginator
        from venues_app.models import Venue
        
        venue = get_object_or_404(Venue, id=venue_id)
        
        # Get reviews
        reviews = Review.objects.filter(
            venue=venue,
            is_approved=True
        ).select_related('customer').prefetch_related('helpfulness_votes')

        # Apply filters
        if rating_filter:
            try:
                rating_value = int(rating_filter)
                if 1 <= rating_value <= 5:
                    reviews = reviews.filter(rating=rating_value)
            except (ValueError, TypeError):
                pass

        # Apply sorting
        if sort_by == 'oldest':
            reviews = reviews.order_by('created_at')
        elif sort_by == 'rating_asc':
            reviews = reviews.order_by('rating', '-created_at')
        elif sort_by == 'rating_desc':
            reviews = reviews.order_by('-rating', '-created_at')
        elif sort_by == 'helpful':
            reviews = reviews.annotate(
                helpful_count=models.Count('helpfulness_votes', filter=models.Q(helpfulness_votes__is_helpful=True))
            ).order_by('-helpful_count', '-created_at')
        else:
            reviews = reviews.order_by('-created_at')

        # Paginate
        paginator = Paginator(reviews, 10)
        page_obj = paginator.get_page(page)

        # Render reviews HTML
        from django.template.loader import render_to_string
        reviews_html = render_to_string(
            'review_app/components/review_list_items.html',
            {
                'reviews': page_obj,
                'user': request.user,
                'venue': venue
            },
            request=request
        )

        return JsonResponse({
            'success': True,
            'html': reviews_html,
            'has_more': page_obj.has_next(),
            'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
            'current_page': page,
            'total_pages': paginator.num_pages
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': _('Could not load more reviews')
        }, status=500)


@login_required
@require_http_methods(["POST"])
def save_review_draft(request):
    """
    AJAX endpoint for auto-saving review drafts.
    """
    try:
        data = json.loads(request.body)
        venue_id = data.get('venue_id')
        rating = data.get('rating')
        written_review = data.get('written_review', '')
        
        if not venue_id:
            return JsonResponse({
                'success': False,
                'message': _('Venue ID is required')
            }, status=400)

        from venues_app.models import Venue
        venue = get_object_or_404(Venue, id=venue_id)

        # Save or update draft
        try:
            from ..models import ReviewDraft
            draft, created = ReviewDraft.objects.update_or_create(
                customer=request.user,
                venue=venue,
                defaults={
                    'rating': rating,
                    'written_review': written_review[:1000]  # Limit length
                }
            )
            
            return JsonResponse({
                'success': True,
                'message': _('Draft saved'),
                'draft_id': draft.id,
                'created': created
            })
        except:
            # If ReviewDraft model doesn't exist, just return success
            return JsonResponse({
                'success': True,
                'message': _('Draft saved locally')
            })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': _('Could not save draft')
        }, status=500)


@require_http_methods(["GET"])
def moderation_stats(request):
    """
    AJAX endpoint for getting moderation statistics.
    """
    if not request.user.is_staff:
        return JsonResponse({
            'success': False,
            'message': _('Permission denied')
        }, status=403)

    try:
        stats = moderation_service.get_moderation_stats()
        
        return JsonResponse({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': _('Could not load moderation statistics')
        }, status=500) 