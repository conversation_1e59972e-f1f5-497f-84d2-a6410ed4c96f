# Generated by Django 5.2.4 on 2025-07-05 20:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("review_app", "0001_initial"),
        ("venues_app", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="review",
            name="venue",
            field=models.ForeignKey(
                help_text="Venue being reviewed",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="reviews",
                to="venues_app.venue",
            ),
        ),
        migrations.AddField(
            model_name="reviewdraft",
            name="customer",
            field=models.ForeignKey(
                help_text="Customer who created this draft",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="review_drafts",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="reviewdraft",
            name="venue",
            field=models.ForeignKey(
                help_text="Venue being reviewed",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="review_drafts",
                to="venues_app.venue",
            ),
        ),
        migrations.AddField(
            model_name="reviewflag",
            name="flagged_by",
            field=models.ForeignKey(
                help_text="Customer who flagged this review",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="review_flags",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="reviewflag",
            name="review",
            field=models.ForeignKey(
                help_text="Review being flagged",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="flags",
                to="review_app.review",
            ),
        ),
        migrations.AddField(
            model_name="reviewflag",
            name="reviewed_by",
            field=models.ForeignKey(
                blank=True,
                help_text="Admin who reviewed this flag",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="reviewed_review_flags",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="reviewhelpfulness",
            name="review",
            field=models.ForeignKey(
                help_text="Review being voted on",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="helpfulness_votes",
                to="review_app.review",
            ),
        ),
        migrations.AddField(
            model_name="reviewhelpfulness",
            name="user",
            field=models.ForeignKey(
                help_text="User who voted on helpfulness",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="review_helpfulness_votes",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="reviewresponse",
            name="provider",
            field=models.ForeignKey(
                help_text="Service provider who wrote this response",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="review_responses",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="reviewresponse",
            name="review",
            field=models.OneToOneField(
                help_text="Review being responded to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="response",
                to="review_app.review",
            ),
        ),
        migrations.AddField(
            model_name="customerreviewresponse",
            name="provider_response",
            field=models.OneToOneField(
                help_text="Provider response being responded to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="customer_response",
                to="review_app.reviewresponse",
            ),
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(fields=["rating"], name="review_app__rating_7210ed_idx"),
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(
                fields=["created_at"], name="review_app__created_cd94ef_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(fields=["venue"], name="review_app__venue_i_703b73_idx"),
        ),
        migrations.AlterUniqueTogether(
            name="review",
            unique_together={("customer", "venue")},
        ),
        migrations.AddIndex(
            model_name="reviewdraft",
            index=models.Index(
                fields=["customer"], name="review_app__custome_aa9086_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="reviewdraft",
            index=models.Index(fields=["venue"], name="review_app__venue_i_c7bcba_idx"),
        ),
        migrations.AddIndex(
            model_name="reviewdraft",
            index=models.Index(
                fields=["updated_at"], name="review_app__updated_ab7c4c_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="reviewdraft",
            unique_together={("customer", "venue")},
        ),
        migrations.AlterUniqueTogether(
            name="reviewflag",
            unique_together={("review", "flagged_by")},
        ),
        migrations.AddIndex(
            model_name="reviewhelpfulness",
            index=models.Index(
                fields=["review", "is_helpful"], name="review_app__review__3cbfed_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="reviewhelpfulness",
            index=models.Index(fields=["user"], name="review_app__user_id_19077a_idx"),
        ),
        migrations.AlterUniqueTogether(
            name="reviewhelpfulness",
            unique_together={("review", "user")},
        ),
    ]
