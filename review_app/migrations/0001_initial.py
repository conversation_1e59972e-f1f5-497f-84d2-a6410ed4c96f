# Generated by Django 5.2.4 on 2025-07-05 20:51

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ReviewDraft",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rating",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        help_text="Star rating from 1 to 5 (optional in draft)",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "written_review",
                    models.TextField(
                        blank=True,
                        help_text="Written review content (max 1000 characters, optional)",
                        max_length=1000,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When the draft was first created"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When the draft was last updated"
                    ),
                ),
            ],
            options={
                "verbose_name": "Review Draft",
                "verbose_name_plural": "Review Drafts",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="ReviewFlag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reason",
                    models.CharField(
                        choices=[
                            ("inappropriate", "Inappropriate Content"),
                            ("fake", "Fake Review"),
                            ("spam", "Spam"),
                            ("offensive", "Offensive Language"),
                            ("other", "Other"),
                        ],
                        help_text="Reason for flagging this review",
                        max_length=20,
                    ),
                ),
                (
                    "reason_text",
                    models.TextField(
                        blank=True,
                        help_text="Additional details about why this review was flagged",
                        max_length=500,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("reviewed", "Reviewed"),
                            ("resolved", "Resolved"),
                        ],
                        default="pending",
                        help_text="Current status of this flag",
                        max_length=20,
                    ),
                ),
                (
                    "admin_notes",
                    models.TextField(
                        blank=True, help_text="Admin notes about the flag resolution"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When the flag was created"
                    ),
                ),
                (
                    "reviewed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the flag was reviewed by admin",
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name": "Review Flag",
                "verbose_name_plural": "Review Flags",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ReviewHelpfulness",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_helpful",
                    models.BooleanField(
                        help_text="True if user found review helpful, False if not helpful"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When the vote was cast"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When the vote was last updated"
                    ),
                ),
            ],
            options={
                "verbose_name": "Review Helpfulness Vote",
                "verbose_name_plural": "Review Helpfulness Votes",
            },
        ),
        migrations.CreateModel(
            name="ReviewResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "response_text",
                    models.TextField(
                        help_text="Provider response to the review (max 500 characters)",
                        max_length=500,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When the response was posted"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When the response was last updated"
                    ),
                ),
            ],
            options={
                "verbose_name": "Review Response",
                "verbose_name_plural": "Review Responses",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CustomerReviewResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "response_text",
                    models.TextField(
                        help_text="Customer response to provider response (max 300 characters)",
                        max_length=300,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When the response was posted"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When the response was last updated"
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        help_text="Customer who wrote this response",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="customer_review_responses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Customer Review Response",
                "verbose_name_plural": "Customer Review Responses",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Review",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rating",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "1 Star - Poor"),
                            (2, "2 Stars - Fair"),
                            (3, "3 Stars - Good"),
                            (4, "4 Stars - Very Good"),
                            (5, "5 Stars - Excellent"),
                        ],
                        help_text="Star rating from 1 to 5",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "written_review",
                    models.TextField(
                        blank=True,
                        help_text="Written review content (max 1000 characters, optional)",
                        max_length=1000,
                    ),
                ),
                (
                    "slug",
                    models.SlugField(
                        blank=True,
                        help_text="Unique slug for sharing this review",
                        max_length=64,
                        unique=True,
                    ),
                ),
                (
                    "is_approved",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this review is approved and visible",
                    ),
                ),
                (
                    "is_flagged",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this review has been flagged for review",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When the review was posted"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When the review was last updated"
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        help_text="Customer who wrote this review",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reviews",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Review",
                "verbose_name_plural": "Reviews",
                "ordering": ["-created_at"],
            },
        ),
    ]
