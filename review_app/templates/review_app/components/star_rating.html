{% comment %}
Modern Star Rating Component
Usage: {% include 'review_app/components/star_rating.html' with rating=4.5 interactive=True size='lg' %}

Parameters:
- rating: Current rating value (1-5)
- max_rating: Maximum rating value (default: 5)  
- interactive: Boolean to enable user interaction
- size: 'sm', 'md', 'lg', or 'xl' (default: 'md')
- show_label: Boolean to show rating label
- input_name: Name for hidden input field (required for interactive)
- css_class: Additional CSS classes
{% endcomment %}

{% load static %}

{% with rating=rating|default:0 max_rating=max_rating|default:5 size=size|default:'md' %}
<div class="star-rating{% if interactive %} star-rating-interactive{% endif %}{% if size != 'md' %} star-rating-{{ size }}{% endif %}{% if show_label %} star-rating-with-label{% endif %}{% if css_class %} {{ css_class }}{% endif %}"
     {% if interactive and input_name %}data-input-name="{{ input_name }}"{% endif %}>
    
    {% for star_num in "12345"|make_list %}
        {% with star_value=star_num|add:0 %}
            <span class="star{% if star_value <= rating %} star-filled{% elif star_value|add:'-0.5' <= rating %} star-half{% endif %}"
                  {% if interactive %}data-rating="{{ star_value }}"{% endif %}>★</span>
        {% endwith %}
    {% endfor %}
    
    {% if show_label %}
        <span class="star-rating-label">
            {% if rating == 1 %}Poor
            {% elif rating == 2 %}Fair
            {% elif rating == 3 %}Good
            {% elif rating == 4 %}Very Good
            {% elif rating == 5 %}Excellent
            {% else %}{{ rating|floatformat:1 }}/5
            {% endif %}
        </span>
    {% endif %}
    
    {% if interactive and input_name %}
        <input type="hidden" name="{{ input_name }}" value="{{ rating|default:'' }}">
    {% endif %}
</div>
{% endwith %}

{% comment %}
CSS Animation Styles - Include in your CSS file:

@keyframes star-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

.star-rating-interactive .star.star-selected {
    animation: star-pulse 0.3s ease-out;
}
{% endcomment %} 