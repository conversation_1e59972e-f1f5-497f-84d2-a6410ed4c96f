"""Django admin configuration for dashboard_app with modern chart integrations."""

# --- Standard Library Imports ---
import datetime
from decimal import Decimal

# --- Third-Party Imports ---
from django.contrib import admin
from django.db.models import Count, Sum, Avg
from django.utils.html import format_html
from django.utils import timezone
from django.urls import reverse
from django.conf import settings

# --- Chart Integration Imports ---
# Chart functionality will be handled via custom views and templates
ADMIN_CHARTS_AVAILABLE = True

# --- Local App Imports ---
from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile
from booking_cart_app.models import Booking, BookingItem
from venues_app.models import Venue, Service
from .models import FavoriteVenue

try:
    from review_app.models import Review
except ImportError:
    Review = None

try:
    from payments_app.models import Payment
except ImportError:
    Payment = None


@admin.register(FavoriteVenue)
class FavoriteVenueAdmin(admin.ModelAdmin):
    """Admin configuration for FavoriteVenue model."""
    
    list_display = ('customer', 'venue', 'added_date')
    list_filter = ('added_date', 'venue__approval_status')
    search_fields = ('customer__user__email', 'venue__venue_name')
    readonly_fields = ('added_date',)
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'customer__user', 'venue'
        )


# --- Dashboard Statistics Configuration ---
# Chart configurations will be initialized in the admin views


# --- Custom Admin Dashboard Widgets ---
class DashboardChartMixin:
    """Mixin to add chart functionality to admin views."""
    
    def get_chart_data(self, request, model_class, date_field='created_at', days=30):
        """Get chart data for the last N days."""
        end_date = timezone.now().date()
        start_date = end_date - datetime.timedelta(days=days)
        
        data = []
        for i in range(days):
            current_date = start_date + datetime.timedelta(days=i)
            count = model_class.objects.filter(
                **{f'{date_field}__date': current_date}
            ).count()
            data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': count
            })
        
        return data
    
    def get_revenue_chart_data(self, request, days=30):
        """Get revenue chart data for the last N days."""
        end_date = timezone.now().date()
        start_date = end_date - datetime.timedelta(days=days)
        
        data = []
        for i in range(days):
            current_date = start_date + datetime.timedelta(days=i)
            revenue = Booking.objects.filter(
                booking_date__date=current_date,
                status__in=['confirmed', 'completed']
            ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')
            
            data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'revenue': float(revenue)
            })
        
        return data


# --- Enhanced Admin Site Configuration ---
admin.site.site_header = "CozyWish Admin Dashboard"
admin.site.site_title = "CozyWish Admin"
admin.site.index_title = "Welcome to CozyWish Administration"

# Add custom CSS and JS for enhanced dashboard
admin.site.enable_nav_sidebar = False  # Use custom navigation

# --- Dashboard Analytics Views ---
def get_dashboard_analytics():
    """Get comprehensive analytics for the dashboard."""
    today = timezone.now().date()
    last_30_days = today - datetime.timedelta(days=30)
    
    analytics = {
        'total_users': CustomUser.objects.count(),
        'new_users_today': CustomUser.objects.filter(date_joined__date=today).count(),
        'new_users_30_days': CustomUser.objects.filter(date_joined__date__gte=last_30_days).count(),
        'total_bookings': Booking.objects.count(),
        'bookings_today': Booking.objects.filter(booking_date__date=today).count(),
        'bookings_30_days': Booking.objects.filter(booking_date__date__gte=last_30_days).count(),
        'total_revenue': Booking.objects.filter(
            status__in=['confirmed', 'completed']
        ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00'),
        'revenue_today': Booking.objects.filter(
            booking_date__date=today,
            status__in=['confirmed', 'completed']
        ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00'),
        'revenue_30_days': Booking.objects.filter(
            booking_date__date__gte=last_30_days,
            status__in=['confirmed', 'completed']
        ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00'),
        'total_venues': 0,  # Will be populated in views
        'approved_venues': 0,  # Will be populated in views
        'pending_venues': 0,  # Will be populated in views
        'avg_booking_value': Booking.objects.filter(
            status__in=['confirmed', 'completed']
        ).aggregate(avg=Avg('total_price'))['avg'] or Decimal('0.00'),
    }
    
    return analytics


# --- Custom Dashboard Templates ---
def get_admin_dashboard_context(request):
    """Get context data for custom admin dashboard."""
    analytics = get_dashboard_analytics()
    
    # Get chart data
    chart_mixin = DashboardChartMixin()
    
    context = {
        'analytics': analytics,
        'user_chart_data': chart_mixin.get_chart_data(request, CustomUser, 'date_joined'),
        'booking_chart_data': chart_mixin.get_chart_data(request, Booking, 'booking_date'),
        'revenue_chart_data': chart_mixin.get_revenue_chart_data(request),
        'venue_chart_data': chart_mixin.get_chart_data(request, Venue, 'created_at'),
        'admin_charts_available': ADMIN_CHARTS_AVAILABLE,
    }
    
    return context
