# Generated by Django 5.2.4 on 2025-07-05 20:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserPreferences",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "email_notifications",
                    models.BooleanField(
                        default=True,
                        help_text="Receive email notifications for booking updates and promotions",
                    ),
                ),
                (
                    "sms_reminders",
                    models.BooleanField(
                        default=True,
                        help_text="Receive SMS reminders for upcoming appointments",
                    ),
                ),
                (
                    "marketing_updates",
                    models.BooleanField(
                        default=False,
                        help_text="Receive marketing emails about special offers and new venues",
                    ),
                ),
                (
                    "dashboard_notifications",
                    models.<PERSON><PERSON>anField(
                        default=True, help_text="Show notifications in dashboard"
                    ),
                ),
                (
                    "weekly_summary",
                    models.<PERSON><PERSON>anField(
                        default=True, help_text="Receive weekly summary emails"
                    ),
                ),
                (
                    "profile_visibility",
                    models.CharField(
                        choices=[
                            ("public", "Public"),
                            ("private", "Private"),
                            ("friends", "Friends Only"),
                        ],
                        default="public",
                        help_text="Who can see your profile information",
                        max_length=20,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When preferences were created"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When preferences were last updated"
                    ),
                ),
            ],
            options={
                "verbose_name": "User Preferences",
                "verbose_name_plural": "User Preferences",
            },
        ),
        migrations.CreateModel(
            name="FavoriteVenue",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "added_date",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="When the venue was added to favorites",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When the favorite was last updated"
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        help_text="Customer who favorited this venue",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="favorite_venues",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Favorite Venue",
                "verbose_name_plural": "Favorite Venues",
                "ordering": ["-added_date"],
            },
        ),
    ]
