# Generated by Django 5.2.4 on 2025-07-05 20:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("dashboard_app", "0001_initial"),
        ("venues_app", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="favoritevenue",
            name="venue",
            field=models.ForeignKey(
                help_text="Venue that was favorited",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="favorited_by",
                to="venues_app.venue",
            ),
        ),
        migrations.AddField(
            model_name="userpreferences",
            name="user",
            field=models.OneToOneField(
                help_text="User these preferences belong to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="dashboard_preferences",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="favoritevenue",
            index=models.Index(
                fields=["customer", "-added_date"],
                name="dashboard_a_custome_fa9ce1_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="favoritevenue",
            index=models.Index(
                fields=["venue", "-added_date"], name="dashboard_a_venue_i_830d12_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="favoritevenue",
            unique_together={("customer", "venue")},
        ),
        migrations.AddIndex(
            model_name="userpreferences",
            index=models.Index(fields=["user"], name="dashboard_a_user_id_5e8b99_idx"),
        ),
    ]
