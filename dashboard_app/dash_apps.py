"""Plotly Dash applications for advanced analytics dashboard."""

# --- Standard Library Imports ---
import datetime
from decimal import Decimal

# --- Third-Party Imports ---
import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from django.contrib.auth.decorators import staff_required
from django.contrib.auth.models import User

# --- Local App Imports ---
from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile
from booking_cart_app.models import Booking, BookingItem
from venues_app.models import Venue, Service

try:
    from review_app.models import Review
except ImportError:
    Review = None

try:
    from payments_app.models import Payment
except ImportError:
    Payment = None


# --- Initialize Dash App ---
def create_analytics_dash_app():
    """Create and configure the analytics Dash application."""
    
    # Create Dash app
    app = dash.Dash(
        __name__,
        server=False,
        url_base_pathname='/admin/analytics/',
        external_stylesheets=[
            'https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css'
        ]
    )
    
    # Define app layout
    app.layout = html.Div([
        # Header
        html.Div([
            html.H1("CozyWish Analytics Dashboard", className="text-primary mb-0"),
            html.P("Advanced analytics and business intelligence", className="text-muted"),
        ], className="mb-4"),
        
        # Date Range Picker
        html.Div([
            html.Label("Select Date Range:", className="form-label"),
            dcc.DatePickerRange(
                id='date-picker-range',
                start_date=datetime.date.today() - datetime.timedelta(days=30),
                end_date=datetime.date.today(),
                display_format='YYYY-MM-DD',
                className="form-control"
            ),
        ], className="mb-4"),
        
        # Key Metrics Cards
        html.Div([
            html.Div([
                html.H5("Total Users", className="card-title"),
                html.H3(id="total-users", className="text-primary"),
                html.P(id="users-change", className="text-muted"),
            ], className="card-body"),
        ], className="card col-md-3 mb-3"),
        
        html.Div([
            html.Div([
                html.H5("Total Bookings", className="card-title"),
                html.H3(id="total-bookings", className="text-success"),
                html.P(id="bookings-change", className="text-muted"),
            ], className="card-body"),
        ], className="card col-md-3 mb-3"),
        
        html.Div([
            html.Div([
                html.H5("Total Revenue", className="card-title"),
                html.H3(id="total-revenue", className="text-warning"),
                html.P(id="revenue-change", className="text-muted"),
            ], className="card-body"),
        ], className="card col-md-3 mb-3"),
        
        html.Div([
            html.Div([
                html.H5("Active Venues", className="card-title"),
                html.H3(id="active-venues", className="text-info"),
                html.P(id="venues-change", className="text-muted"),
            ], className="card-body"),
        ], className="card col-md-3 mb-3"),
        
        # Charts Section
        html.Div([
            # Revenue Trends
            html.Div([
                html.H4("Revenue Trends", className="card-title"),
                dcc.Graph(id="revenue-trends-chart"),
            ], className="card-body"),
        ], className="card mb-4"),
        
        html.Div([
            # User Growth
            html.Div([
                html.H4("User Growth", className="card-title"),
                dcc.Graph(id="user-growth-chart"),
            ], className="card-body col-md-6"),
            
            # Booking Status Distribution
            html.Div([
                html.H4("Booking Status Distribution", className="card-title"),
                dcc.Graph(id="booking-status-chart"),
            ], className="card-body col-md-6"),
        ], className="card mb-4"),
        
        html.Div([
            # Top Venues
            html.Div([
                html.H4("Top Performing Venues", className="card-title"),
                dcc.Graph(id="top-venues-chart"),
            ], className="card-body col-md-6"),
            
            # Service Category Distribution
            html.Div([
                html.H4("Service Category Distribution", className="card-title"),
                dcc.Graph(id="service-category-chart"),
            ], className="card-body col-md-6"),
        ], className="card mb-4"),
        
        # Geographic Analysis
        html.Div([
            html.H4("Geographic Distribution", className="card-title"),
            dcc.Graph(id="geographic-chart"),
        ], className="card-body mb-4"),
        
        # Performance Metrics
        html.Div([
            html.H4("Performance Metrics", className="card-title"),
            html.Div([
                html.Div([
                    html.H6("Average Booking Value"),
                    html.H4(id="avg-booking-value", className="text-primary"),
                ], className="col-md-3"),
                html.Div([
                    html.H6("Conversion Rate"),
                    html.H4(id="conversion-rate", className="text-success"),
                ], className="col-md-3"),
                html.Div([
                    html.H6("Customer Retention"),
                    html.H4(id="customer-retention", className="text-warning"),
                ], className="col-md-3"),
                html.Div([
                    html.H6("Platform Growth"),
                    html.H4(id="platform-growth", className="text-info"),
                ], className="col-md-3"),
            ], className="row"),
        ], className="card-body"),
        
    ], className="container-fluid p-4")
    
    return app


# --- Callback Functions ---
def get_analytics_data(start_date, end_date):
    """Get analytics data for the specified date range."""
    
    # Convert dates
    start_date = pd.to_datetime(start_date).date()
    end_date = pd.to_datetime(end_date).date()
    
    # Get user statistics
    total_users = CustomUser.objects.count()
    new_users = CustomUser.objects.filter(
        date_joined__date__range=[start_date, end_date]
    ).count()
    
    # Get booking statistics
    total_bookings = Booking.objects.count()
    period_bookings = Booking.objects.filter(
        booking_date__date__range=[start_date, end_date]
    ).count()
    
    # Get revenue statistics
    total_revenue = Booking.objects.filter(
        status__in=['confirmed', 'completed']
    ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')
    
    period_revenue = Booking.objects.filter(
        booking_date__date__range=[start_date, end_date],
        status__in=['confirmed', 'completed']
    ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')
    
    # Get venue statistics
    active_venues = Venue.objects.filter(
        approval_status='approved',
        visibility='active'
    ).count()
    
    return {
        'total_users': total_users,
        'new_users': new_users,
        'total_bookings': total_bookings,
        'period_bookings': period_bookings,
        'total_revenue': float(total_revenue),
        'period_revenue': float(period_revenue),
        'active_venues': active_venues,
    }


def create_revenue_trends_chart(start_date, end_date):
    """Create revenue trends chart."""
    
    # Get daily revenue data
    bookings = Booking.objects.filter(
        booking_date__date__range=[start_date, end_date],
        status__in=['confirmed', 'completed']
    ).values('booking_date__date').annotate(
        daily_revenue=Sum('total_price')
    ).order_by('booking_date__date')
    
    # Convert to DataFrame
    df = pd.DataFrame(list(bookings))
    if df.empty:
        df = pd.DataFrame({'booking_date__date': [start_date], 'daily_revenue': [0]})
    
    # Create chart
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=df['booking_date__date'],
        y=df['daily_revenue'],
        mode='lines+markers',
        name='Daily Revenue',
        line=dict(color='#007cba', width=3),
        marker=dict(size=6)
    ))
    
    fig.update_layout(
        title="Daily Revenue Trends",
        xaxis_title="Date",
        yaxis_title="Revenue ($)",
        hovermode='x unified',
        template='plotly_white'
    )
    
    return fig


def create_user_growth_chart(start_date, end_date):
    """Create user growth chart."""
    
    # Get daily user registrations
    users = CustomUser.objects.filter(
        date_joined__date__range=[start_date, end_date]
    ).values('date_joined__date').annotate(
        daily_users=Count('id')
    ).order_by('date_joined__date')
    
    # Convert to DataFrame
    df = pd.DataFrame(list(users))
    if df.empty:
        df = pd.DataFrame({'date_joined__date': [start_date], 'daily_users': [0]})
    
    # Create chart
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=df['date_joined__date'],
        y=df['daily_users'],
        mode='lines+markers',
        name='Daily Registrations',
        line=dict(color='#28a745', width=3),
        marker=dict(size=6)
    ))
    
    fig.update_layout(
        title="User Registration Growth",
        xaxis_title="Date",
        yaxis_title="New Users",
        hovermode='x unified',
        template='plotly_white'
    )
    
    return fig


def create_booking_status_chart():
    """Create booking status distribution chart."""
    
    # Get booking status distribution
    status_data = Booking.objects.values('status').annotate(
        count=Count('id')
    ).order_by('status')
    
    # Convert to DataFrame
    df = pd.DataFrame(list(status_data))
    if df.empty:
        df = pd.DataFrame({'status': ['pending'], 'count': [0]})
    
    # Create pie chart
    fig = px.pie(
        df, 
        values='count', 
        names='status',
        title="Booking Status Distribution",
        color_discrete_map={
            'pending': '#ffc107',
            'confirmed': '#28a745',
            'completed': '#007cba',
            'cancelled': '#dc3545'
        }
    )
    
    return fig


def create_top_venues_chart():
    """Create top venues chart."""
    
    # Get top venues by booking count
    top_venues = Venue.objects.annotate(
        booking_count=Count('booking')
    ).order_by('-booking_count')[:10]
    
    # Convert to DataFrame
    venue_data = []
    for venue in top_venues:
        venue_data.append({
            'venue_name': venue.venue_name,
            'booking_count': venue.booking_count
        })
    
    df = pd.DataFrame(venue_data)
    if df.empty:
        df = pd.DataFrame({'venue_name': ['No data'], 'booking_count': [0]})
    
    # Create bar chart
    fig = px.bar(
        df, 
        x='booking_count', 
        y='venue_name',
        orientation='h',
        title="Top 10 Venues by Booking Count",
        color='booking_count',
        color_continuous_scale='Blues'
    )
    
    fig.update_layout(
        yaxis={'categoryorder': 'total ascending'},
        template='plotly_white'
    )
    
    return fig


# --- Register Callbacks ---
def register_callbacks(app):
    """Register all callback functions."""
    
    @app.callback(
        [Output('total-users', 'children'),
         Output('users-change', 'children'),
         Output('total-bookings', 'children'),
         Output('bookings-change', 'children'),
         Output('total-revenue', 'children'),
         Output('revenue-change', 'children'),
         Output('active-venues', 'children'),
         Output('venues-change', 'children')],
        [Input('date-picker-range', 'start_date'),
         Input('date-picker-range', 'end_date')]
    )
    def update_metrics(start_date, end_date):
        """Update key metrics cards."""
        data = get_analytics_data(start_date, end_date)
        
        return (
            f"{data['total_users']:,}",
            f"+{data['new_users']:,} this period",
            f"{data['total_bookings']:,}",
            f"+{data['period_bookings']:,} this period",
            f"${data['total_revenue']:,.2f}",
            f"+${data['period_revenue']:,.2f} this period",
            f"{data['active_venues']:,}",
            "Active venues"
        )
    
    @app.callback(
        Output('revenue-trends-chart', 'figure'),
        [Input('date-picker-range', 'start_date'),
         Input('date-picker-range', 'end_date')]
    )
    def update_revenue_chart(start_date, end_date):
        """Update revenue trends chart."""
        return create_revenue_trends_chart(start_date, end_date)
    
    @app.callback(
        Output('user-growth-chart', 'figure'),
        [Input('date-picker-range', 'start_date'),
         Input('date-picker-range', 'end_date')]
    )
    def update_user_growth_chart(start_date, end_date):
        """Update user growth chart."""
        return create_user_growth_chart(start_date, end_date)
    
    @app.callback(
        Output('booking-status-chart', 'figure'),
        [Input('date-picker-range', 'start_date'),
         Input('date-picker-range', 'end_date')]
    )
    def update_booking_status_chart(start_date, end_date):
        """Update booking status chart."""
        return create_booking_status_chart()
    
    @app.callback(
        Output('top-venues-chart', 'figure'),
        [Input('date-picker-range', 'start_date'),
         Input('date-picker-range', 'end_date')]
    )
    def update_top_venues_chart(start_date, end_date):
        """Update top venues chart."""
        return create_top_venues_chart()


# --- Main App Instance ---
analytics_app = create_analytics_dash_app()
register_callbacks(analytics_app) 