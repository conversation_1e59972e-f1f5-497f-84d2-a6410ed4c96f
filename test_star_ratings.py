#!/usr/bin/env python
"""
Test script to verify star ratings integration with Venue and Service models.
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
django.setup()

from django.contrib.auth import get_user_model
from venues_app.models import Venue, Service
from star_ratings.models import Rating

User = get_user_model()

def test_star_ratings_integration():
    """Test that star ratings integration is working correctly."""
    print("Testing Star Ratings Integration...")
    
    # Test 1: Check if Venue model has ratings relation
    print("\n1. Testing Venue model ratings relation...")
    try:
        # Get a venue (if any exist)
        venue = Venue.objects.first()
        if venue:
            print(f"✓ Found venue: {venue.venue_name}")
            
            # Test rating helper methods
            avg_rating = venue.get_average_rating()
            rating_count = venue.get_rating_count()
            rating_distribution = venue.get_rating_distribution()
            
            print(f"✓ Average rating: {avg_rating}")
            print(f"✓ Rating count: {rating_count}")
            print(f"✓ Rating distribution: {rating_distribution}")
            
            # Test user rating methods
            user = User.objects.first()
            if user:
                has_rated = venue.has_user_rated(user)
                user_rating = venue.get_user_rating(user)
                print(f"✓ User has rated: {has_rated}")
                print(f"✓ User rating: {user_rating}")
        else:
            print("⚠ No venues found in database")
    except Exception as e:
        print(f"✗ Error testing venue ratings: {e}")
    
    # Test 2: Check if Service model has ratings relation
    print("\n2. Testing Service model ratings relation...")
    try:
        # Get a service (if any exist)
        service = Service.objects.first()
        if service:
            print(f"✓ Found service: {service.service_title}")
            
            # Test rating helper methods
            avg_rating = service.get_average_rating()
            rating_count = service.get_rating_count()
            rating_distribution = service.get_rating_distribution()
            
            print(f"✓ Average rating: {avg_rating}")
            print(f"✓ Rating count: {rating_count}")
            print(f"✓ Rating distribution: {rating_distribution}")
            
            # Test user rating methods
            user = User.objects.first()
            if user:
                has_rated = service.has_user_rated(user)
                user_rating = service.get_user_rating(user)
                print(f"✓ User has rated: {has_rated}")
                print(f"✓ User rating: {user_rating}")
        else:
            print("⚠ No services found in database")
    except Exception as e:
        print(f"✗ Error testing service ratings: {e}")
    
    # Test 3: Check star_ratings app configuration
    print("\n3. Testing star_ratings app configuration...")
    try:
        from django.conf import settings
        if 'star_ratings' in settings.INSTALLED_APPS:
            print("✓ star_ratings app is installed")
            
            # Check configuration
            star_ratings_config = getattr(settings, 'STAR_RATINGS', {})
            print(f"✓ Star ratings config: {star_ratings_config}")
        else:
            print("✗ star_ratings app not found in INSTALLED_APPS")
    except Exception as e:
        print(f"✗ Error checking star_ratings configuration: {e}")
    
    # Test 4: Check if Rating model is accessible
    print("\n4. Testing Rating model accessibility...")
    try:
        rating_count = Rating.objects.count()
        print(f"✓ Rating model accessible, found {rating_count} ratings")
    except Exception as e:
        print(f"✗ Error accessing Rating model: {e}")
    
    print("\n✅ Star ratings integration test completed!")

if __name__ == "__main__":
    test_star_ratings_integration()
