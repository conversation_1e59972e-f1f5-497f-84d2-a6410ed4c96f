# Generated by Django 5.2.4 on 2025-07-05 20:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ImageMetadata",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image_path", models.Char<PERSON>ield(max_length=255, unique=True)),
                ("thumbnail_path", models.CharField(blank=True, max_length=255)),
                ("high_res_path", models.CharField(blank=True, max_length=255)),
                ("original_path", models.CharField(blank=True, max_length=255)),
                (
                    "image_type",
                    models.CharField(
                        choices=[
                            ("profile", "Profile Image"),
                            ("logo", "Business Logo"),
                            ("venue", "Venue Image"),
                            ("other", "Other"),
                        ],
                        default="other",
                        max_length=20,
                    ),
                ),
                (
                    "entity_type",
                    models.CharField(
                        choices=[
                            ("customers", "Customer"),
                            ("professionals", "Service Provider"),
                            ("venues", "Venue"),
                            ("staff", "Staff Member"),
                            ("other", "Other"),
                        ],
                        default="other",
                        max_length=20,
                    ),
                ),
                ("entity_id", models.IntegerField(blank=True, null=True)),
                ("width", models.IntegerField()),
                ("height", models.IntegerField()),
                ("file_size", models.IntegerField(help_text="Size in bytes")),
                (
                    "original_file_size",
                    models.IntegerField(
                        blank=True, help_text="Original size in bytes", null=True
                    ),
                ),
                ("format", models.CharField(max_length=10)),
                ("content_type", models.CharField(max_length=50)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="uploaded_images",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Image Metadata",
                "verbose_name_plural": "Image Metadata",
                "ordering": ["-created_at"],
            },
        ),
    ]
