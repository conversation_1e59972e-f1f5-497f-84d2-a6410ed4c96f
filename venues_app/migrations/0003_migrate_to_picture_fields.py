# Generated by Django 5.2.3 on 2025-07-06 05:26

import pictures.models
import venues_app.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('venues_app', '0002_add_star_ratings_integration'),
    ]

    operations = [
        migrations.AddField(
            model_name='service',
            name='service_image_height',
            field=models.PositiveIntegerField(blank=True, editable=False, null=True, verbose_name='service image height'),
        ),
        migrations.AddField(
            model_name='service',
            name='service_image_width',
            field=models.PositiveIntegerField(blank=True, editable=False, null=True, verbose_name='service image width'),
        ),
        migrations.AddField(
            model_name='venue',
            name='main_image_height',
            field=models.PositiveIntegerField(blank=True, editable=False, null=True, verbose_name='main image height'),
        ),
        migrations.AddField(
            model_name='venue',
            name='main_image_width',
            field=models.PositiveIntegerField(blank=True, editable=False, null=True, verbose_name='main image width'),
        ),
        migrations.AddField(
            model_name='venueimage',
            name='image_height',
            field=models.PositiveIntegerField(blank=True, editable=False, null=True, verbose_name='image height'),
        ),
        migrations.AddField(
            model_name='venueimage',
            name='image_width',
            field=models.PositiveIntegerField(blank=True, editable=False, null=True, verbose_name='image width'),
        ),
        migrations.AlterField(
            model_name='service',
            name='service_image',
            field=pictures.models.PictureField(aspect_ratios=[None, '3/2', '16/9', '4/3', '1/1'], blank=True, breakpoints={'l': 1200, 'm': 992, 's': 768, 'xl': 1400, 'xs': 576}, container_width=1200, file_types=['AVIF', 'WEBP'], grid_columns=12, height_field='service_image_height', help_text='Optional image for this service. Supports modern formats (AVIF, WebP) with automatic optimization and responsive variants.', null=True, pixel_densities=[1, 2], upload_to='services/images/', verbose_name='service image', width_field='service_image_width'),
        ),
        migrations.AlterField(
            model_name='venue',
            name='main_image',
            field=pictures.models.PictureField(aspect_ratios=[None, '3/2', '16/9', '4/3', '1/1'], blank=True, breakpoints={'l': 1200, 'm': 992, 's': 768, 'xl': 1400, 'xs': 576}, container_width=1200, file_types=['AVIF', 'WEBP'], grid_columns=12, height_field='main_image_height', help_text='Main featured image for the venue. Supports modern formats (AVIF, WebP) with automatic optimization and responsive variants.', null=True, pixel_densities=[1, 2], upload_to=venues_app.models.get_venue_main_image_path, verbose_name='main image', width_field='main_image_width'),
        ),
        migrations.AlterField(
            model_name='venueimage',
            name='image',
            field=pictures.models.PictureField(aspect_ratios=[None, '3/2', '16/9', '4/3', '1/1'], breakpoints={'l': 1200, 'm': 992, 's': 768, 'xl': 1400, 'xs': 576}, container_width=1200, file_types=['AVIF', 'WEBP'], grid_columns=12, height_field='image_height', help_text='Venue gallery image. Supports modern formats (AVIF, WebP) with automatic optimization and responsive variants.', pixel_densities=[1, 2], upload_to=venues_app.models.get_venue_gallery_image_path, verbose_name='image', width_field='image_width'),
        ),
    ]
