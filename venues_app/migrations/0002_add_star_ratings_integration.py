# Generated by Django 5.2.3 on 2025-07-06 05:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('venues_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Favorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField(verbose_name='object id')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this item was favorited', verbose_name='created at')),
                ('notes', models.TextField(blank=True, help_text='Optional notes about why this item was favorited', verbose_name='notes')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='content type')),
                ('user', models.ForeignKey(help_text='User who favorited this item', on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to=settings.AUTH_USER_MODEL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'Favorite',
                'verbose_name_plural': 'Favorites',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'content_type'], name='venues_app__user_id_e395b1_idx'), models.Index(fields=['user', 'created_at'], name='venues_app__user_id_cae524_idx')],
                'unique_together': {('user', 'content_type', 'object_id')},
            },
        ),
    ]
