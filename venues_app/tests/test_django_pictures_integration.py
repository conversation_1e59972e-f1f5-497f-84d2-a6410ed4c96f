"""
Tests for django-pictures integration in venues app.
"""

import tempfile
from io import BytesIO
from decimal import Decimal
from PIL import Image
from django.test import TestCase, override_settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth import get_user_model
from django.template import Context, Template
from django.template.loader import render_to_string

from accounts_app.models import ServiceProviderProfile
from venues_app.models import Venue, VenueImage, Service

User = get_user_model()


class DjangoPicturesIntegrationTest(TestCase):
    """Test django-pictures integration with venue models."""

    def setUp(self):
        """Set up test data."""
        # Create test user and service provider
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123"
        )
        self.provider = ServiceProviderProfile.objects.create(
            user=self.user,
            legal_name="Test Business LLC",
            display_name="Test Business",
            phone="+**********",
            contact_name="Test Contact",
            address="123 Test St",
            city="Test City",
            state="CA",
            zip_code="12345"
        )
        
        # Create test venue
        self.venue = Venue.objects.create(
            service_provider=self.provider,
            venue_name="Test Venue",
            short_description="A test venue",
            city="Test City",
            county="Test County",
            state="CA"
        )

    def create_test_image(self, width=800, height=600, format='JPEG'):
        """Create a test image file."""
        image = Image.new('RGB', (width, height), color='red')
        image_io = BytesIO()
        image.save(image_io, format=format)
        image_io.seek(0)
        
        filename = f'test_image.{format.lower()}'
        return SimpleUploadedFile(
            filename,
            image_io.getvalue(),
            content_type=f'image/{format.lower()}'
        )

    def test_venue_main_image_picturefield(self):
        """Test that venue main_image is a PictureField."""
        # Upload test image
        test_image = self.create_test_image()
        self.venue.main_image = test_image
        self.venue.save()
        
        # Verify the image was saved
        self.assertTrue(self.venue.main_image)
        self.assertTrue(self.venue.main_image.url)
        
        # Verify width and height fields are populated
        self.venue.refresh_from_db()
        self.assertIsNotNone(self.venue.main_image_width)
        self.assertIsNotNone(self.venue.main_image_height)
        self.assertEqual(self.venue.main_image_width, 800)
        self.assertEqual(self.venue.main_image_height, 600)

    def test_venue_image_picturefield(self):
        """Test that VenueImage.image is a PictureField."""
        # Create test image
        test_image = self.create_test_image()
        
        # Create venue image
        venue_image = VenueImage.objects.create(
            venue=self.venue,
            image=test_image,
            caption="Test image"
        )
        
        # Verify the image was saved
        self.assertTrue(venue_image.image)
        self.assertTrue(venue_image.image.url)
        
        # Verify width and height fields are populated
        venue_image.refresh_from_db()
        self.assertIsNotNone(venue_image.image_width)
        self.assertIsNotNone(venue_image.image_height)
        self.assertEqual(venue_image.image_width, 800)
        self.assertEqual(venue_image.image_height, 600)

    def test_service_image_picturefield(self):
        """Test that Service.service_image is a PictureField."""
        # Create test service
        service = Service.objects.create(
            venue=self.venue,
            service_title="Test Service",
            short_description="A test service",
            price_min=Decimal("100.00"),
            duration_minutes=60
        )
        
        # Upload test image
        test_image = self.create_test_image()
        service.service_image = test_image
        service.save()
        
        # Verify the image was saved
        self.assertTrue(service.service_image)
        self.assertTrue(service.service_image.url)
        
        # Verify width and height fields are populated
        service.refresh_from_db()
        self.assertIsNotNone(service.service_image_width)
        self.assertIsNotNone(service.service_image_height)
        self.assertEqual(service.service_image_width, 800)
        self.assertEqual(service.service_image_height, 600)

    def test_get_primary_image_object_method(self):
        """Test the new get_primary_image_object method."""
        # Test with no images
        self.assertIsNone(self.venue.get_primary_image_object())
        
        # Test with main_image only
        test_image = self.create_test_image()
        self.venue.main_image = test_image
        self.venue.save()
        
        primary_image = self.venue.get_primary_image_object()
        self.assertEqual(primary_image, self.venue.main_image)
        
        # Test with gallery image
        gallery_image = VenueImage.objects.create(
            venue=self.venue,
            image=self.create_test_image(),
            is_primary=True,
            is_active=True
        )
        
        primary_image = self.venue.get_primary_image_object()
        self.assertEqual(primary_image, gallery_image.image)

    def test_picture_template_tag_loading(self):
        """Test that pictures template tags can be loaded."""
        # Create a venue with an image first
        test_image = self.create_test_image()
        self.venue.main_image = test_image
        self.venue.save()

        template = Template("{% load pictures %}{% picture venue.main_image %}")

        # Should not raise an error
        context = Context({'venue': self.venue})
        rendered = template.render(context)

        # Should render something (even if empty for no image)
        self.assertIsInstance(rendered, str)

    def test_custom_picture_template_tags(self):
        """Test custom picture template tags."""
        # Load custom template tags
        template = Template("""
            {% load picture_tags %}
            {% venue_picture venue %}
        """)

        context = Context({'venue': self.venue})
        rendered = template.render(context)

        # Should render without errors
        self.assertIsInstance(rendered, str)
        # Should contain placeholder since no image
        self.assertIn('placeholder', rendered)

    def test_image_metadata_population(self):
        """Test that image metadata is properly handled."""
        # Create venue image
        test_image = self.create_test_image(width=1200, height=800)
        venue_image = VenueImage.objects.create(
            venue=self.venue,
            image=test_image,
            caption="Test image"
        )
        
        # Call metadata population
        venue_image.populate_metadata_from_file()
        
        # Verify metadata was populated
        self.assertIsNotNone(venue_image.original_filename)
        self.assertTrue(venue_image.original_filename.endswith('.jpeg'))

    @override_settings(PICTURES={'USE_PLACEHOLDERS': True})
    def test_placeholder_support(self):
        """Test that placeholder images work in development."""
        # This test verifies the configuration supports placeholders
        from django.conf import settings
        
        pictures_config = getattr(settings, 'PICTURES', {})
        self.assertTrue(pictures_config.get('USE_PLACEHOLDERS', False))

    def test_responsive_breakpoints_configuration(self):
        """Test that responsive breakpoints are properly configured."""
        from django.conf import settings
        
        pictures_config = getattr(settings, 'PICTURES', {})
        breakpoints = pictures_config.get('BREAKPOINTS', {})
        
        # Verify expected breakpoints exist
        expected_breakpoints = ['xs', 's', 'm', 'l', 'xl']
        for bp in expected_breakpoints:
            self.assertIn(bp, breakpoints)

    def test_modern_image_formats_configuration(self):
        """Test that modern image formats are configured."""
        from django.conf import settings
        
        pictures_config = getattr(settings, 'PICTURES', {})
        file_types = pictures_config.get('FILE_TYPES', [])
        
        # Verify AVIF and WebP are configured
        self.assertIn('AVIF', file_types)
        self.assertIn('WEBP', file_types)
