"""
Favorites System for Venues App

This module provides a custom favorites/bookmarks system for venues and services.
"""

# --- Django Imports ---
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from notifications_app.utils import create_notification
from notifications_app.models import Notification

User = get_user_model()


class FavoriteManager(models.Manager):
    """Custom manager for Favorite model."""
    
    def add_favorite(self, user, obj):
        """
        Add an object to user's favorites.
        
        Args:
            user: User instance
            obj: Object to favorite (Venue, Service, etc.)
            
        Returns:
            tuple: (favorite_instance, created)
        """
        content_type = ContentType.objects.get_for_model(obj)
        favorite, created = self.get_or_create(
            user=user,
            content_type=content_type,
            object_id=obj.pk,
        )
        
        if created:
            # Create activity stream entry
            try:
                from actstream import action
                action.send(
                    user,
                    verb='favorited',
                    target=obj,
                    description=f"Added {obj} to favorites"
                )
            except ImportError:
                pass  # actstream not available
                
            # Create notification for venue owner if favoriting a venue
            if hasattr(obj, 'service_provider') and obj.service_provider:
                try:
                    create_notification(
                        user=obj.service_provider.user,
                        notification_type=Notification.SYSTEM,
                        title="Someone favorited your venue!",
                        message=f"A user has added '{obj.venue_name}' to their favorites.",
                        related_object_id=obj.id,
                        related_object_type="VenueFavorited",
                        action_url=obj.get_absolute_url(),
                    )
                except Exception:
                    pass  # Fail silently if notification creation fails
        
        return favorite, created
    
    def remove_favorite(self, user, obj):
        """
        Remove an object from user's favorites.
        
        Args:
            user: User instance
            obj: Object to unfavorite
            
        Returns:
            bool: True if favorite was removed, False if it didn't exist
        """
        content_type = ContentType.objects.get_for_model(obj)
        try:
            favorite = self.get(
                user=user,
                content_type=content_type,
                object_id=obj.pk,
            )
            favorite.delete()
            
            # Create activity stream entry
            try:
                from actstream import action
                action.send(
                    user,
                    verb='unfavorited',
                    target=obj,
                    description=f"Removed {obj} from favorites"
                )
            except ImportError:
                pass  # actstream not available
                
            return True
        except self.model.DoesNotExist:
            return False
    
    def is_favorite(self, user, obj):
        """
        Check if an object is in user's favorites.
        
        Args:
            user: User instance
            obj: Object to check
            
        Returns:
            bool: True if object is favorited by user
        """
        if not user.is_authenticated:
            return False
            
        content_type = ContentType.objects.get_for_model(obj)
        return self.filter(
            user=user,
            content_type=content_type,
            object_id=obj.pk,
        ).exists()
    
    def get_user_favorites(self, user, model_class=None):
        """
        Get all favorites for a user, optionally filtered by model class.
        
        Args:
            user: User instance
            model_class: Optional model class to filter by
            
        Returns:
            QuerySet: Favorite objects
        """
        queryset = self.filter(user=user)
        
        if model_class:
            content_type = ContentType.objects.get_for_model(model_class)
            queryset = queryset.filter(content_type=content_type)
            
        return queryset.select_related('content_type').order_by('-created_at')


class Favorite(models.Model):
    """
    Generic favorite model that can be used for any object.
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='favorites',
        verbose_name=_('user'),
        help_text=_('User who favorited this item')
    )
    
    # Generic foreign key to any model
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        verbose_name=_('content type')
    )
    object_id = models.PositiveIntegerField(verbose_name=_('object id'))
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Metadata
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('created at'),
        help_text=_('When this item was favorited')
    )
    
    # Notes (optional)
    notes = models.TextField(
        blank=True,
        verbose_name=_('notes'),
        help_text=_('Optional notes about why this item was favorited')
    )
    
    objects = FavoriteManager()
    
    class Meta:
        verbose_name = _('Favorite')
        verbose_name_plural = _('Favorites')
        unique_together = ('user', 'content_type', 'object_id')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'content_type']),
            models.Index(fields=['user', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.content_object}"
    
    @property
    def object_name(self):
        """Get a display name for the favorited object."""
        obj = self.content_object
        if hasattr(obj, 'venue_name'):
            return obj.venue_name
        elif hasattr(obj, 'service_name'):
            return obj.service_name
        elif hasattr(obj, 'name'):
            return obj.name
        elif hasattr(obj, 'title'):
            return obj.title
        else:
            return str(obj)


# --- Helper Functions ---

def add_to_favorites(user, obj):
    """
    Convenience function to add an object to user's favorites.
    
    Args:
        user: User instance
        obj: Object to favorite
        
    Returns:
        tuple: (favorite_instance, created)
    """
    return Favorite.objects.add_favorite(user, obj)


def remove_from_favorites(user, obj):
    """
    Convenience function to remove an object from user's favorites.
    
    Args:
        user: User instance
        obj: Object to unfavorite
        
    Returns:
        bool: True if favorite was removed, False if it didn't exist
    """
    return Favorite.objects.remove_favorite(user, obj)


def is_favorited(user, obj):
    """
    Convenience function to check if an object is favorited by user.
    
    Args:
        user: User instance
        obj: Object to check
        
    Returns:
        bool: True if object is favorited by user
    """
    return Favorite.objects.is_favorite(user, obj)


def get_user_favorites(user, model_class=None):
    """
    Convenience function to get user's favorites.
    
    Args:
        user: User instance
        model_class: Optional model class to filter by
        
    Returns:
        QuerySet: Favorite objects
    """
    return Favorite.objects.get_user_favorites(user, model_class)


def get_favorite_venues(user):
    """
    Get user's favorite venues.
    
    Args:
        user: User instance
        
    Returns:
        QuerySet: Venue objects that are favorited by user
    """
    from .models import Venue
    
    favorite_venue_ids = Favorite.objects.get_user_favorites(
        user, Venue
    ).values_list('object_id', flat=True)
    
    return Venue.objects.filter(
        id__in=favorite_venue_ids,
        is_deleted=False,
        visibility='active'
    ).order_by('-created_at')


def get_favorite_services(user):
    """
    Get user's favorite services.
    
    Args:
        user: User instance
        
    Returns:
        QuerySet: Service objects that are favorited by user
    """
    from .models import Service
    
    favorite_service_ids = Favorite.objects.get_user_favorites(
        user, Service
    ).values_list('object_id', flat=True)
    
    return Service.objects.filter(
        id__in=favorite_service_ids,
        is_deleted=False,
        is_active=True
    ).order_by('-created_at')
