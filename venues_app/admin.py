# --- Django Imports ---
from django.contrib import admin, messages
from django.contrib.admin import SimpleListFilter
from django.db.models import Count, Q
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from admin_app.bulk_operations import VenueBulkOperations

# --- Local Imports ---
from .models import (
    Category,
    FlaggedVenue,
    HolidaySchedule,
    OperatingHours,
    Service,
    ServiceCategory,
    ServiceTag,
    USCity,
    Venue,
    VenueAmenity,
    VenueCategory,
    VenueCreationDraft,
    VenueFAQ,
    VenueImage,
)


class ApprovalStatusFilter(SimpleListFilter):
    """Custom filter for venue approval status with counts."""

    title = _("approval status")
    parameter_name = "approval_status"

    def lookups(self, request, model_admin):
        # Get counts for each status
        counts = Venue.objects.aggregate(
            pending=Count("id", filter=Q(approval_status=Venue.PENDING)),
            approved=Count("id", filter=Q(approval_status=Venue.APPROVED)),
            rejected=Count("id", filter=Q(approval_status=Venue.REJECTED)),
            draft=Count("id", filter=Q(approval_status=Venue.DRAFT)),
        )

        return (
            (Venue.PENDING, f'Pending ({counts["pending"]})'),
            (Venue.APPROVED, f'Approved ({counts["approved"]})'),
            (Venue.REJECTED, f'Rejected ({counts["rejected"]})'),
            (Venue.DRAFT, f'Draft ({counts["draft"]})'),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(approval_status=self.value())


class CompletionStatusFilter(SimpleListFilter):
    """Filter venues by completion status for better review."""

    title = _("completion status")
    parameter_name = "completion"

    def lookups(self, request, model_admin):
        return (
            ("complete", _("Complete Profile")),
            ("incomplete", _("Incomplete Profile")),
            ("needs_review", _("Needs Review")),
        )

    def queryset(self, request, queryset):
        if self.value() == "complete":
            return queryset.filter(
                ~Q(short_description="")
                & ~Q(phone="")
                & ~Q(email="")
                & Q(images__isnull=False)
            ).distinct()
        elif self.value() == "incomplete":
            return queryset.filter(
                Q(short_description="")
                | Q(phone="")
                | Q(email="")
                | Q(images__isnull=True)
            ).distinct()
        elif self.value() == "needs_review":
            return queryset.filter(
                approval_status=Venue.PENDING, rejected_at__isnull=True
            )


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """
    Admin interface for Category model with slug auto-generation.
    """

    list_display = ("category_name", "slug", "venues_count", "is_active", "created_at")
    list_filter = ("is_active", "created_at")
    search_fields = ("category_name", "category_description")
    readonly_fields = ("slug", "created_at", "updated_at", "venues_count")
    prepopulated_fields = {"slug": ("category_name",)}

    fieldsets = (
        (
            _("Category Information"),
            {"fields": ("category_name", "slug", "category_description")},
        ),
        (_("Status"), {"fields": ("is_active",)}),
        (_("Statistics"), {"fields": ("venues_count",)}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )

    def venues_count(self, obj):
        """Display number of venues in this category."""
        count = obj.venues.filter(approval_status=Venue.APPROVED).count()
        if count > 0:
            url = (
                reverse("admin:venues_app_venue_changelist")
                + f"?categories__id__exact={obj.id}"
            )
            return format_html('<a href="{}">{} venues</a>', url, count)
        return "0 venues"

    venues_count.short_description = _("Active Venues")


@admin.register(Venue)
class VenueAdmin(VenueBulkOperations, admin.ModelAdmin):
    """
    Enhanced admin interface for Venue model with comprehensive management features.
    """

    list_display = (
        "venue_name_with_status",
        "service_provider_link",
        "location_info",
        "approval_status_badge",
        "completion_score",
        "images_count",
        "services_count",
        "created_at",
        "priority_actions",
    )
    list_filter = (
        ApprovalStatusFilter,
        CompletionStatusFilter,
        "visibility",
        "state",
        "county",
        "created_at",
        "approved_at",
    )
    search_fields = (
        "venue_name",
        "short_description",
        "city",
        "state",
        "service_provider__user__email",
        "service_provider__business_name",
        "tags",
    )
    readonly_fields = (
        "slug",
        "created_at",
        "updated_at",
        "approved_at",
        "rejected_at",
        "full_address",
        "search_vector",
        "completion_score",
        "approval_timeline",
    )

    fieldsets = (
        (
            _("Basic Information"),
            {
                "fields": (
                    "service_provider",
                    "venue_name",
                    "slug",
                    "short_description",
                    "main_image",
                    "completion_score",
                )
            },
        ),
        (
            _("Location"),
            {
                "fields": (
                    "street_number",
                    "street_name",
                    "city",
                    "county",
                    "state",
                    "us_city",
                    "full_address",
                    "latitude",
                    "longitude",
                )
            },
        ),
        (_("Contact Information"), {"fields": ("phone", "email", "website_url")}),
        (
            _("Operational Details"),
            {"fields": ("operating_hours", "opening_notes", "tags")},
        ),
        (
            _("Status & Approval"),
            {
                "fields": (
                    "approval_status",
                    "visibility",
                    "admin_notes",
                    "approved_at",
                    "rejected_at",
                    "approval_timeline",
                )
            },
        ),
        (_("Metadata"), {"fields": ("created_at", "updated_at", "search_vector")}),
    )

    actions = [
        "approve_venues",
        "reject_venues",
        "request_more_info",
        "activate_venues",
        "deactivate_venues",
        "bulk_review_mode",
    ]

    def get_queryset(self, request):
        """Optimize queryset with related data."""
        return (
            super()
            .get_queryset(request)
            .select_related("service_provider__user", "us_city")
            .prefetch_related("categories", "images", "services")
        )

    def venue_name_with_status(self, obj):
        """Display venue name with visual status indicator."""
        status_colors = {
            Venue.APPROVED: "green",
            Venue.PENDING: "orange",
            Venue.REJECTED: "red",
            Venue.DRAFT: "gray",
        }
        color = status_colors.get(obj.approval_status, "black")

        flagged_count = obj.flags.filter(status=FlaggedVenue.PENDING).count()
        flag_indicator = f" ⚠️({flagged_count})" if flagged_count > 0 else ""

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>{}',
            color,
            obj.venue_name,
            flag_indicator,
        )

    venue_name_with_status.short_description = _("Venue Name")
    venue_name_with_status.admin_order_field = "venue_name"

    def service_provider_link(self, obj):
        """Display service provider with link to their profile."""
        if obj.service_provider:
            url = reverse(
                "admin:accounts_app_serviceproviderprofile_change",
                args=[obj.service_provider.id],
            )
            return format_html(
                '<a href="{}">{}</a>', url, obj.service_provider.business_name
            )
        return "-"

    service_provider_link.short_description = _("Service Provider")

    def location_info(self, obj):
        """Display compact location information."""
        return f"{obj.city}, {obj.state}"

    location_info.short_description = _("Location")

    def approval_status_badge(self, obj):
        """Display approval status with colored badge."""
        status_info = {
            Venue.APPROVED: ("✅", "green", "Approved"),
            Venue.PENDING: ("⏳", "orange", "Pending Review"),
            Venue.REJECTED: ("❌", "red", "Rejected"),
            Venue.DRAFT: ("📝", "gray", "Draft"),
        }
        icon, color, text = status_info.get(
            obj.approval_status, ("?", "black", obj.approval_status)
        )

        return format_html(
            '<span style="color: {};" title="{}">{} {}</span>', color, text, icon, text
        )

    approval_status_badge.short_description = _("Status")

    def completion_score(self, obj):
        """Calculate and display venue completion score."""
        if not hasattr(obj, "_completion_score"):
            score = 0
            total_fields = 10

            # Basic info (4 points)
            if obj.venue_name:
                score += 1
            if obj.short_description:
                score += 1
            if obj.phone:
                score += 1
            if obj.email:
                score += 1

            # Location (2 points)
            if all(
                [obj.street_number, obj.street_name, obj.city, obj.county, obj.state]
            ):
                score += 2

            # Images (2 points)
            if obj.main_image:
                score += 1
            if obj.images.exists():
                score += 1

            # Services (1 point)
            if obj.services.exists():
                score += 1

            # Categories (1 point)
            if obj.categories.exists():
                score += 1

            obj._completion_score = int((score / total_fields) * 100)

        score = obj._completion_score
        color = "green" if score >= 80 else "orange" if score >= 60 else "red"

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}%</span>', color, score
        )

    completion_score.short_description = _("Completion")

    def images_count(self, obj):
        """Display number of venue images."""
        count = obj.images.count()
        if count > 0:
            return format_html("<strong>{}</strong> images", count)
        return format_html('<span style="color: red;">No images</span>')

    images_count.short_description = _("Images")

    def services_count(self, obj):
        """Display number of venue services."""
        count = obj.services.count()
        if count > 0:
            return format_html("<strong>{}</strong> services", count)
        return format_html('<span style="color: orange;">No services</span>')

    services_count.short_description = _("Services")

    def priority_actions(self, obj):
        """Display quick action buttons for high-priority items."""
        actions = []

        if obj.approval_status == Venue.PENDING:
            approve_url = f"/admin/venues_app/venue/{obj.id}/approve/"
            reject_url = f"/admin/venues_app/venue/{obj.id}/reject/"
            actions.append(
                f'<a href="{approve_url}" style="color: green;">✅ Approve</a>'
            )
            actions.append(f'<a href="{reject_url}" style="color: red;">❌ Reject</a>')

        if obj.flags.filter(status=FlaggedVenue.PENDING).exists():
            flags_url = (
                reverse("admin:venues_app_flaggedvenue_changelist")
                + f"?venue__id__exact={obj.id}"
            )
            actions.append(
                f'<a href="{flags_url}" style="color: orange;">⚠️ View Flags</a>'
            )

        return format_html(" | ".join(actions)) if actions else "-"

    priority_actions.short_description = _("Quick Actions")

    def approval_timeline(self, obj):
        """Display approval timeline for venue."""
        timeline = []
        timeline.append(f"Created: {obj.created_at.strftime('%Y-%m-%d %H:%M')}")

        if obj.approved_at:
            timeline.append(f"Approved: {obj.approved_at.strftime('%Y-%m-%d %H:%M')}")

        if obj.rejected_at:
            timeline.append(f"Rejected: {obj.rejected_at.strftime('%Y-%m-%d %H:%M')}")

        return format_html("<br>".join(timeline))

    approval_timeline.short_description = _("Approval Timeline")

    # Enhanced Actions
    def approve_venues(self, request, queryset):
        """Approve selected venues and trigger notifications."""
        updated = 0
        for venue in queryset.filter(
            approval_status__in=[Venue.PENDING, Venue.REJECTED]
        ):
            venue.approval_status = Venue.APPROVED
            venue.approved_at = timezone.now()
            venue.rejected_at = None
            venue.admin_notes = f"Bulk approved by {request.user.username} on {timezone.now().strftime('%Y-%m-%d %H:%M')}"
            venue.save()  # This will trigger signals
            updated += 1

        self.message_user(
            request, f"{updated} venue(s) approved successfully.", messages.SUCCESS
        )

    approve_venues.short_description = "Approve selected venues"

    def reject_venues(self, request, queryset):
        """Reject selected venues and trigger notifications."""
        updated = 0
        for venue in queryset.filter(
            approval_status__in=[Venue.PENDING, Venue.APPROVED]
        ):
            venue.approval_status = Venue.REJECTED
            venue.rejected_at = timezone.now()
            venue.approved_at = None
            if not venue.admin_notes:
                venue.admin_notes = f"Bulk rejected by {request.user.username} on {timezone.now().strftime('%Y-%m-%d %H:%M')}"
            venue.save()  # This will trigger signals
            updated += 1

        self.message_user(request, f"{updated} venue(s) rejected.", messages.SUCCESS)

    reject_venues.short_description = "Reject selected venues"

    def request_more_info(self, request, queryset):
        """Request more information from venue owners."""
        updated = 0
        for venue in queryset.filter(approval_status=Venue.PENDING):
            venue.admin_notes = f"More information requested by {request.user.username} on {timezone.now().strftime('%Y-%m-%d %H:%M')}"
            venue.save()
            # TODO: Send notification to service provider
            updated += 1

        self.message_user(
            request,
            f"Requested more information from {updated} venue owner(s).",
            messages.INFO,
        )

    request_more_info.short_description = "Request more information"

    def activate_venues(self, request, queryset):
        updated = queryset.update(visibility=Venue.ACTIVE)
        self.message_user(request, f"{updated} venue(s) activated.", messages.SUCCESS)

    activate_venues.short_description = "Activate selected venues"

    def deactivate_venues(self, request, queryset):
        updated = queryset.update(visibility=Venue.INACTIVE)
        self.message_user(request, f"{updated} venue(s) deactivated.", messages.SUCCESS)

    deactivate_venues.short_description = "Deactivate selected venues"

    def bulk_review_mode(self, request, queryset):
        """Enter bulk review mode for efficient venue review."""
        venue_ids = list(queryset.values_list("id", flat=True))
        ids_param = ",".join(map(str, venue_ids))
        url = reverse("admin:venues_app_venue_changelist") + f"?bulk_review={ids_param}"
        return HttpResponseRedirect(url)

    bulk_review_mode.short_description = "Enter bulk review mode"


@admin.register(VenueAmenity)
class VenueAmenityAdmin(admin.ModelAdmin):
    """
    Admin interface for VenueAmenity model.
    """

    list_display = ("venue", "amenity_type", "custom_name", "is_active", "created_at")
    list_filter = ("amenity_type", "is_active", "created_at")
    search_fields = ("venue__venue_name", "custom_name", "description")
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (
            _("Amenity Details"),
            {"fields": ("venue", "amenity_type", "custom_name", "description")},
        ),
        (_("Status"), {"fields": ("is_active",)}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )


@admin.register(VenueCategory)
class VenueCategoryAdmin(admin.ModelAdmin):
    """
    Admin interface for VenueCategory through model.
    """

    list_display = ("venue", "category", "created_at")
    list_filter = ("category", "created_at")
    search_fields = ("venue__venue_name", "category__category_name")
    readonly_fields = ("created_at",)


@admin.register(VenueImage)
class VenueImageAdmin(admin.ModelAdmin):
    """
    Admin interface for VenueImage model.
    """

    list_display = ("venue", "order", "caption", "is_active", "created_at")
    list_filter = ("is_active", "order", "created_at")
    search_fields = ("venue__venue_name", "caption")
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (_("Image Details"), {"fields": ("venue", "image", "order", "caption")}),
        (_("Status"), {"fields": ("is_active",)}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )


@admin.register(VenueFAQ)
class VenueFAQAdmin(admin.ModelAdmin):
    """
    Admin interface for VenueFAQ model.
    """

    list_display = ("venue", "question_preview", "is_active", "order", "created_at")
    list_filter = ("is_active", "created_at")
    search_fields = ("venue__venue_name", "question", "answer")
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (_("FAQ Details"), {"fields": ("venue", "question", "answer", "order")}),
        (_("Status"), {"fields": ("is_active",)}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )

    def question_preview(self, obj):
        return obj.question[:50] + "..." if len(obj.question) > 50 else obj.question

    question_preview.short_description = "Question"


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """
    Admin interface for Service model.
    """

    list_display = (
        "service_title",
        "venue",
        "price_min",
        "price_max",
        "duration_minutes",
        "is_active",
        "created_at",
    )
    list_filter = ("is_active", "created_at", "venue__approval_status")
    search_fields = (
        "service_title",
        "short_description",
        "venue__venue_name",
        "venue__service_provider__business_name",
    )
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (
            _("Service Information"),
            {"fields": ("venue", "service_title", "short_description")},
        ),
        (
            _("Pricing & Duration"),
            {"fields": ("price_min", "price_max", "duration_minutes")},
        ),
        (_("Status"), {"fields": ("is_active",)}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )


@admin.register(OperatingHours)
class OperatingHoursAdmin(admin.ModelAdmin):
    """
    Admin interface for OperatingHours model.
    """

    list_display = (
        "venue",
        "get_day_display",
        "opening",
        "closing",
        "is_closed",
        "is_24_hours",
        "is_overnight",
        "created_at",
    )
    list_filter = ("day", "is_closed", "is_24_hours", "is_overnight", "created_at")
    search_fields = ("venue__venue_name",)
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (
            _("Schedule Details"),
            {
                "fields": (
                    "venue",
                    "day",
                    "opening",
                    "closing",
                    "is_closed",
                    "is_24_hours",
                    "is_overnight",
                )
            },
        ),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )

    def get_day_display(self, obj):
        """Display day name instead of number."""
        return obj.get_day_display()

    get_day_display.short_description = "Day"
    get_day_display.admin_order_field = "day"


@admin.register(HolidaySchedule)
class HolidayScheduleAdmin(admin.ModelAdmin):
    """
    Admin interface for HolidaySchedule model.
    """

    list_display = (
        "venue",
        "name",
        "date",
        "opening",
        "closing",
        "is_closed",
        "is_24_hours",
        "is_overnight",
        "is_active",
    )
    list_filter = (
        "date",
        "is_closed",
        "is_24_hours",
        "is_overnight",
        "is_active",
        "created_at",
    )
    search_fields = ("venue__venue_name", "name")
    readonly_fields = ("created_at", "updated_at")
    date_hierarchy = "date"

    fieldsets = (
        (_("Holiday Information"), {"fields": ("venue", "name", "date", "notes")}),
        (
            _("Schedule Details"),
            {
                "fields": (
                    "opening",
                    "closing",
                    "is_closed",
                    "is_24_hours",
                    "is_overnight",
                )
            },
        ),
        (_("Settings"), {"fields": ("is_active",)}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        queryset = super().get_queryset(request)
        return queryset.select_related("venue")

    def save_model(self, request, obj, form, change):
        """Custom save logic if needed."""
        super().save_model(request, obj, form, change)


@admin.register(FlaggedVenue)
class FlaggedVenueAdmin(admin.ModelAdmin):
    """
    Admin interface for FlaggedVenue model.
    """

    list_display = (
        "venue",
        "flagged_by",
        "status",
        "reason_preview",
        "created_at",
        "reviewed_at",
    )
    list_filter = ("status", "created_at", "reviewed_at")
    search_fields = ("venue__venue_name", "flagged_by__email", "reason", "admin_notes")
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (_("Flag Details"), {"fields": ("venue", "flagged_by", "reason")}),
        (_("Admin Review"), {"fields": ("status", "admin_notes", "reviewed_at")}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )

    actions = ["mark_as_reviewed", "mark_as_resolved"]

    def reason_preview(self, obj):
        return obj.reason[:50] + "..." if len(obj.reason) > 50 else obj.reason

    reason_preview.short_description = "Reason"

    def mark_as_reviewed(self, request, queryset):
        updated = queryset.update(
            status=FlaggedVenue.REVIEWED, reviewed_at=timezone.now()
        )
        self.message_user(
            request, f"{updated} flag(s) marked as reviewed.", messages.SUCCESS
        )

    mark_as_reviewed.short_description = "Mark as reviewed"

    def mark_as_resolved(self, request, queryset):
        updated = queryset.update(
            status=FlaggedVenue.RESOLVED, reviewed_at=timezone.now()
        )
        self.message_user(
            request, f"{updated} flag(s) marked as resolved.", messages.SUCCESS
        )

    mark_as_resolved.short_description = "Mark as resolved"


@admin.register(USCity)
class USCityAdmin(admin.ModelAdmin):
    """
    Admin interface for USCity model.
    """

    list_display = ("city", "state_name", "state_id", "county_name", "city_id")
    list_filter = ("state_name", "county_name")
    search_fields = ("city", "state_name", "county_name", "zip_codes")
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (
            _("Location Information"),
            {
                "fields": (
                    "city",
                    "state_name",
                    "state_id",
                    "county_name",
                    "city_id",
                    "zip_codes",
                )
            },
        ),
        (_("Coordinates"), {"fields": ("latitude", "longitude")}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )


@admin.register(ServiceCategory)
class ServiceCategoryAdmin(admin.ModelAdmin):
    """
    Admin interface for ServiceCategory model with comprehensive management.
    """

    list_display = (
        "name",
        "slug",
        "services_count",
        "venues_count",
        "sort_order",
        "is_active",
        "created_at",
    )
    list_filter = ("is_active", "created_at", "updated_at")
    search_fields = ("name", "description")
    readonly_fields = (
        "slug",
        "created_at",
        "updated_at",
        "services_count",
        "venues_count",
    )
    prepopulated_fields = {"slug": ("name",)}
    ordering = ("sort_order", "name")

    fieldsets = (
        (_("Category Information"), {"fields": ("name", "slug", "description")}),
        (_("Display Settings"), {"fields": ("icon_class", "color_code", "sort_order")}),
        (_("Status"), {"fields": ("is_active",)}),
        (_("Statistics"), {"fields": ("services_count", "venues_count")}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )

    def services_count(self, obj):
        """Display number of active services in this category."""
        count = obj.services.filter(is_active=True).count()
        if count > 0:
            url = (
                reverse("admin:venues_app_service_changelist")
                + f"?service_category__id__exact={obj.id}"
            )
            return format_html('<a href="{}">{} services</a>', url, count)
        return "0 services"

    services_count.short_description = _("Active Services")

    def venues_count(self, obj):
        """Display number of venues offering services in this category."""
        count = (
            obj.venues.filter(services__service_category=obj, services__is_active=True)
            .distinct()
            .count()
        )
        if count > 0:
            return format_html("{} venues", count)
        return "0 venues"

    venues_count.short_description = _("Venues with Services")

    def get_queryset(self, request):
        """Optimize queryset with prefetch_related."""
        return super().get_queryset(request).prefetch_related("services")

    actions = ["activate_categories", "deactivate_categories"]

    def activate_categories(self, request, queryset):
        """Bulk activate service categories."""
        updated = queryset.update(is_active=True)
        self.message_user(
            request,
            f"{updated} service categories were successfully activated.",
            messages.SUCCESS,
        )

    activate_categories.short_description = _("Activate selected service categories")

    def deactivate_categories(self, request, queryset):
        """Bulk deactivate service categories."""
        updated = queryset.update(is_active=False)
        self.message_user(
            request,
            f"{updated} service categories were successfully deactivated.",
            messages.SUCCESS,
        )

    deactivate_categories.short_description = _(
        "Deactivate selected service categories"
    )


@admin.register(ServiceTag)
class ServiceTagAdmin(admin.ModelAdmin):
    """
    Admin interface for ServiceTag model with usage tracking.
    """

    list_display = (
        "name",
        "slug",
        "tag_type",
        "usage_count",
        "is_active",
        "created_at",
    )
    list_filter = ("tag_type", "is_active", "created_at")
    search_fields = ("name", "description")
    readonly_fields = ("slug", "usage_count", "created_at", "updated_at")
    prepopulated_fields = {"slug": ("name",)}
    ordering = ("tag_type", "name")

    fieldsets = (
        (_("Tag Information"), {"fields": ("name", "slug", "tag_type", "description")}),
        (_("Status"), {"fields": ("is_active",)}),
        (_("Usage Statistics"), {"fields": ("usage_count",)}),
        (_("Metadata"), {"fields": ("created_at", "updated_at")}),
    )

    def get_queryset(self, request):
        """Optimize queryset with usage count annotation."""
        return (
            super()
            .get_queryset(request)
            .annotate(
                current_usage=Count("services", filter=Q(services__is_active=True))
            )
        )

    actions = ["activate_tags", "deactivate_tags", "update_usage_counts"]

    def activate_tags(self, request, queryset):
        """Bulk activate service tags."""
        updated = queryset.update(is_active=True)
        self.message_user(
            request,
            f"{updated} service tags were successfully activated.",
            messages.SUCCESS,
        )

    activate_tags.short_description = _("Activate selected service tags")

    def deactivate_tags(self, request, queryset):
        """Bulk deactivate service tags."""
        updated = queryset.update(is_active=False)
        self.message_user(
            request,
            f"{updated} service tags were successfully deactivated.",
            messages.SUCCESS,
        )

    deactivate_tags.short_description = _("Deactivate selected service tags")

    def update_usage_counts(self, request, queryset):
        """Update usage counts for selected tags."""
        updated_count = 0
        for tag in queryset:
            tag.update_usage_count()
            updated_count += 1

        self.message_user(
            request,
            f"Usage counts updated for {updated_count} service tags.",
            messages.SUCCESS,
        )

    update_usage_counts.short_description = _("Update usage counts for selected tags")


@admin.register(VenueCreationDraft)
class VenueCreationDraftAdmin(admin.ModelAdmin):
    """Admin interface for venue creation drafts"""

    list_display = [
        "service_provider",
        "venue_name",
        "current_step",
        "get_progress_percentage",
        "created_at",
        "updated_at",
    ]
    list_filter = ["current_step", "created_at", "updated_at", "state"]
    search_fields = [
        "service_provider__business_name",
        "service_provider__user__email",
        "venue_name",
        "city",
        "county",
    ]
    readonly_fields = ["created_at", "updated_at", "get_progress_percentage"]
    ordering = ["-updated_at"]

    fieldsets = (
        (
            "Service Provider",
            {
                "fields": ("service_provider",),
            },
        ),
        (
            "Basic Information",
            {
                "fields": ("venue_name", "short_description"),
            },
        ),
        (
            "Location Details",
            {
                "fields": ("state", "county", "city", "street_number", "street_name"),
            },
        ),
        (
            "Contact Information",
            {
                "fields": ("phone", "email", "website_url"),
                "classes": ("collapse",),
            },
        ),
        (
            "Social Media",
            {
                "fields": (
                    "instagram_url",
                    "facebook_url",
                    "twitter_url",
                    "linkedin_url",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Categories & Progress",
            {
                "fields": ("categories_data", "current_step", "completed_steps"),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "get_progress_percentage"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_progress_percentage(self, obj):
        """Display progress percentage in admin"""
        return f"{obj.get_progress_percentage()}%"

    get_progress_percentage.short_description = "Progress"
    get_progress_percentage.admin_order_field = "updated_at"

    def has_add_permission(self, request):
        """Prevent manual creation of drafts in admin"""
        return False
