"""
Activity Stream Integration for Venues App

This module integrates django-activity-stream with the existing notification system
to track and notify about venue-related activities.
"""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from actstream import action
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.urls import reverse

# --- Local Imports ---
from notifications_app.utils import create_notification, run_async
from notifications_app.models import Notification

# --- Local App Imports ---
from .models import Venue, VenueImage, VenueCategory, Service

User = get_user_model()
logger = logging.getLogger(__name__)


# --- Activity Stream Helpers ---

def create_venue_activity(actor, verb, target, action_object=None, description=None):
    """
    Create an activity stream entry for venue-related actions.
    
    Args:
        actor: User performing the action
        verb: Action verb (e.g., 'created', 'updated', 'approved')
        target: Target object (usually a Venue)
        action_object: Optional related object
        description: Optional description
    """
    try:
        action.send(
            actor,
            verb=verb,
            target=target,
            action_object=action_object,
            description=description,
            timestamp=None  # Use current time
        )
        logger.info(f"Activity created: {actor} {verb} {target}")
    except Exception as e:
        logger.error(f"Failed to create activity: {e}")


def notify_followers_about_venue_activity(venue, activity_type, title, message, action_url=None):
    """
    Notify followers about venue activities.
    
    Args:
        venue: Venue instance
        activity_type: Type of activity for notification
        title: Notification title
        message: Notification message
        action_url: Optional URL for the notification
    """
    try:
        # For now, we'll notify the venue owner and any admin users
        # In the future, this could be extended to notify actual followers
        
        # Notify venue owner
        if venue.service_provider and venue.service_provider.user:
            create_notification(
                user=venue.service_provider.user,
                notification_type=Notification.SYSTEM,
                title=title,
                message=message,
                related_object_id=venue.id,
                related_object_type="VenueActivity",
                action_url=action_url or venue.get_absolute_url(),
            )
        
        # Notify admin users about significant venue activities
        if activity_type in ['approved', 'rejected', 'submitted']:
            admin_users = User.objects.filter(is_staff=True, is_active=True)
            for admin_user in admin_users:
                create_notification(
                    user=admin_user,
                    notification_type=Notification.SYSTEM,
                    title=f"Admin: {title}",
                    message=f"Admin notification: {message}",
                    related_object_id=venue.id,
                    related_object_type="VenueAdminActivity",
                    action_url=action_url or f"/admin/venues_app/venue/{venue.id}/change/",
                )
                
    except Exception as e:
        logger.error(f"Failed to notify followers about venue activity: {e}")


# --- Signal Handlers ---

@receiver(post_save, sender=Venue)
def track_venue_activity(sender, instance, created, **kwargs):
    """Track venue creation and updates in activity stream."""
    try:
        if not instance.service_provider or not instance.service_provider.user:
            return
            
        actor = instance.service_provider.user
        
        if created:
            # New venue created
            create_venue_activity(
                actor=actor,
                verb='created',
                target=instance,
                description=f"Created venue '{instance.venue_name}' in {instance.location_display}"
            )
            
            # Create notification
            title = "Venue Created Successfully"
            message = f"Your venue '{instance.venue_name}' has been created and submitted for review."
            action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': instance.slug})
            
            run_async(notify_followers_about_venue_activity, 
                     instance, 'created', title, message, action_url)
            
        else:
            # Venue updated - check for significant changes
            try:
                # Get the original venue state from the database
                original = Venue.objects.get(pk=instance.pk)
                
                # Check for approval status changes
                if hasattr(instance, '_state') and instance._state.adding is False:
                    # This is an update, check for status changes
                    if instance.approval_status != getattr(original, 'approval_status', None):
                        if instance.approval_status == 'approved':
                            create_venue_activity(
                                actor=actor,
                                verb='approved',
                                target=instance,
                                description=f"Venue '{instance.venue_name}' was approved"
                            )
                            
                            title = "Venue Approved!"
                            message = f"Congratulations! Your venue '{instance.venue_name}' has been approved and is now live."
                            action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': instance.slug})
                            
                            run_async(notify_followers_about_venue_activity, 
                                     instance, 'approved', title, message, action_url)
                                     
                        elif instance.approval_status == 'rejected':
                            create_venue_activity(
                                actor=actor,
                                verb='rejected',
                                target=instance,
                                description=f"Venue '{instance.venue_name}' was rejected"
                            )
                            
                            title = "Venue Requires Changes"
                            message = f"Your venue '{instance.venue_name}' needs some updates before approval. Please check the admin notes."
                            action_url = reverse('venues_app:venue_edit_basic_information', kwargs={'venue_slug': instance.slug})
                            
                            run_async(notify_followers_about_venue_activity, 
                                     instance, 'rejected', title, message, action_url)
                                     
                        elif instance.approval_status == 'pending':
                            create_venue_activity(
                                actor=actor,
                                verb='submitted',
                                target=instance,
                                description=f"Venue '{instance.venue_name}' was submitted for review"
                            )
                            
                            title = "Venue Submitted for Review"
                            message = f"Your venue '{instance.venue_name}' has been submitted for admin review."
                            action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': instance.slug})
                            
                            run_async(notify_followers_about_venue_activity, 
                                     instance, 'submitted', title, message, action_url)
                    
                    # Check for other significant updates
                    else:
                        create_venue_activity(
                            actor=actor,
                            verb='updated',
                            target=instance,
                            description=f"Updated venue '{instance.venue_name}'"
                        )
                        
            except Venue.DoesNotExist:
                # This shouldn't happen, but handle gracefully
                pass
                
    except Exception as e:
        logger.error(f"Error tracking venue activity: {e}")


@receiver(post_save, sender=VenueImage)
def track_venue_image_activity(sender, instance, created, **kwargs):
    """Track venue image additions."""
    try:
        if not instance.venue.service_provider or not instance.venue.service_provider.user:
            return
            
        actor = instance.venue.service_provider.user
        
        if created:
            create_venue_activity(
                actor=actor,
                verb='added image to',
                target=instance.venue,
                action_object=instance,
                description=f"Added new image to venue '{instance.venue.venue_name}'"
            )
            
    except Exception as e:
        logger.error(f"Error tracking venue image activity: {e}")


@receiver(post_delete, sender=VenueImage)
def track_venue_image_deletion(sender, instance, **kwargs):
    """Track venue image deletions."""
    try:
        if not instance.venue.service_provider or not instance.venue.service_provider.user:
            return
            
        actor = instance.venue.service_provider.user
        
        create_venue_activity(
            actor=actor,
            verb='removed image from',
            target=instance.venue,
            description=f"Removed image from venue '{instance.venue.venue_name}'"
        )
        
    except Exception as e:
        logger.error(f"Error tracking venue image deletion: {e}")


@receiver(post_save, sender=Service)
def track_service_activity(sender, instance, created, **kwargs):
    """Track service creation and updates."""
    try:
        if not instance.venue.service_provider or not instance.venue.service_provider.user:
            return
            
        actor = instance.venue.service_provider.user
        
        if created:
            create_venue_activity(
                actor=actor,
                verb='added service to',
                target=instance.venue,
                action_object=instance,
                description=f"Added service '{instance.service_name}' to venue '{instance.venue.venue_name}'"
            )
            
            # Notify about new service
            title = "New Service Added"
            message = f"Service '{instance.service_name}' has been added to your venue '{instance.venue.venue_name}'."
            action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': instance.venue.slug})
            
            create_notification(
                user=actor,
                notification_type=Notification.SYSTEM,
                title=title,
                message=message,
                related_object_id=instance.id,
                related_object_type="ServiceActivity",
                action_url=action_url,
            )
            
    except Exception as e:
        logger.error(f"Error tracking service activity: {e}")


@receiver(post_save, sender=VenueCategory)
def track_venue_category_activity(sender, instance, created, **kwargs):
    """Track venue category changes."""
    try:
        if not instance.venue.service_provider or not instance.venue.service_provider.user:
            return
            
        actor = instance.venue.service_provider.user
        
        if created:
            create_venue_activity(
                actor=actor,
                verb='added category to',
                target=instance.venue,
                action_object=instance.category,
                description=f"Added category '{instance.category.name}' to venue '{instance.venue.venue_name}'"
            )
            
    except Exception as e:
        logger.error(f"Error tracking venue category activity: {e}")
